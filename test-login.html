<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Écran de Connexion DataTec</title>
    <style>
        /* Variables CSS pour les thèmes */
        :root {
            --background-color: #282828;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
            --card-background: rgba(40, 40, 40, 0.95);
            --border-color: rgba(255, 255, 255, 0.1);
            --primary-color: #667eea;
            --primary-hover: #5a6fd8;
            --error-color: #f44336;
            --success-color: #4caf50;
        }

        /* Thème clair */
        [data-theme="light"] {
            --background-color: #f5f7fa;
            --text-color: #333333;
            --secondary-text-color: #666666;
            --card-background: rgba(255, 255, 255, 0.98);
            --border-color: rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #2d2d2d 100%);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            transition: all 0.3s ease;
        }

        [data-theme="light"] body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .login-container {
            background: var(--card-background);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        [data-theme="light"] .login-container {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-circle {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-circle img {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .app-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .app-subtitle {
            font-size: 14px;
            color: var(--secondary-text-color);
            opacity: 0.8;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .input-container {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 16px 16px 16px 48px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: transparent;
            color: var(--text-color);
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-text-color);
            font-size: 20px;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 4px;
        }

        .login-button {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 12px;
            background: #007bff;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
        }

        .login-button:hover {
            background: #0056b3;
            box-shadow: 0 6px 25px rgba(0, 123, 255, 0.4);
            transform: translateY(-2px);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: var(--error-color);
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            margin-top: 24px;
            font-size: 12px;
            color: var(--secondary-text-color);
            opacity: 0.6;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                padding: 24px;
                margin: 16px;
            }
            
            .app-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="theme-toggle" onclick="toggleTheme()">
        🌙
    </div>

    <div class="login-container">
        <div class="logo-section">
            <div class="logo-circle">
                <img src="static/logo-light.svg" alt="DataTec Logo" id="logo">
            </div>
            <h1 class="app-title">DataTec Workspace</h1>
            <p class="app-subtitle">Connectez-vous pour continuer</p>
        </div>

        <form id="loginForm">
            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <div class="form-group">
                <label class="form-label" for="username">Nom d'utilisateur</label>
                <div class="input-container">
                    <span class="input-icon">👤</span>
                    <input 
                        type="text" 
                        id="username" 
                        class="form-input" 
                        placeholder="Entrez votre nom d'utilisateur"
                        autocomplete="username"
                        required
                    >
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">Mot de passe</label>
                <div class="input-container">
                    <span class="input-icon">🔒</span>
                    <input 
                        type="password" 
                        id="password" 
                        class="form-input" 
                        placeholder="Entrez votre mot de passe"
                        autocomplete="current-password"
                        required
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        👁️
                    </button>
                </div>
            </div>

            <button type="submit" class="login-button" id="loginButton">
                Se connecter
            </button>
        </form>

        <div class="footer">
            © 2024 DataTec Workspace. Tous droits réservés.
        </div>
    </div>

    <script>
        // Gestion du thème
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            // Changer le logo
            const logo = document.getElementById('logo');
            logo.src = newTheme === 'dark' ? 'static/logo-light.svg' : 'static/logo-dark.svg';
            
            // Changer l'icône du toggle
            const toggle = document.querySelector('.theme-toggle');
            toggle.textContent = newTheme === 'dark' ? '🌙' : '☀️';
            
            localStorage.setItem('theme', newTheme);
        }

        // Initialiser le thème
        const savedTheme = localStorage.getItem('theme') || 'dark';
        document.documentElement.setAttribute('data-theme', savedTheme);
        const logo = document.getElementById('logo');
        logo.src = savedTheme === 'dark' ? 'static/logo-light.svg' : 'static/logo-dark.svg';
        document.querySelector('.theme-toggle').textContent = savedTheme === 'dark' ? '🌙' : '☀️';

        // Gestion du mot de passe
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleButton.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleButton.textContent = '👁️';
            }
        }

        // Gestion du formulaire
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('errorMessage');
            const button = document.getElementById('loginButton');
            
            // Réinitialiser l'erreur
            errorDiv.style.display = 'none';
            
            // Validation
            if (!username.trim()) {
                showError('Nom d\'utilisateur requis');
                return;
            }
            
            if (!password.trim()) {
                showError('Mot de passe requis');
                return;
            }
            
            // Simulation de connexion
            button.textContent = 'Connexion...';
            button.disabled = true;
            
            setTimeout(() => {
                if ((username === 'admin' && password === 'admin123') || 
                    (username === 'user' && password === 'user123')) {
                    showSuccess('Connexion réussie !');
                    setTimeout(() => {
                        alert('Redirection vers l\'application...');
                    }, 1000);
                } else {
                    showError('Nom d\'utilisateur ou mot de passe incorrect');
                }
                
                button.textContent = 'Se connecter';
                button.disabled = false;
            }, 1500);
        });

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.background = 'rgba(76, 175, 80, 0.1)';
            errorDiv.style.borderColor = 'rgba(76, 175, 80, 0.3)';
            errorDiv.style.color = '#4caf50';
            errorDiv.style.display = 'block';
        }
    </script>
</body>
</html>
