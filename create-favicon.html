<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON> Favicon DataTec</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .preview {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Générateur de Favicon DataTec</h1>
        <p>Cet outil génère un favicon ICO à partir du logo DataTec unifié.</p>
        
        <div class="preview">
            <div>
                <h3>Logo Original (SVG)</h3>
                <svg width="64" height="64" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100" height="100" rx="20" fill="#F5F5F5"/>
                    <path d="M25 30H75M25 50H75M25 70H75" stroke="#333333" stroke-width="8" stroke-linecap="round"/>
                    <circle cx="35" cy="30" r="5" fill="#4CAF50"/>
                    <circle cx="35" cy="50" r="5" fill="#2196F3"/>
                    <circle cx="35" cy="70" r="5" fill="#F44336"/>
                </svg>
            </div>
            
            <div>
                <h3>Favicon 32x32</h3>
                <canvas id="favicon32" width="32" height="32"></canvas>
            </div>
            
            <div>
                <h3>Favicon 16x16</h3>
                <canvas id="favicon16" width="16" height="16"></canvas>
            </div>
        </div>
        
        <button onclick="generateFavicon()">Générer Favicon</button>
        <button onclick="downloadFavicon()">Télécharger favicon.ico</button>
        
        <div id="instructions" style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 4px; display: none;">
            <h3>Instructions :</h3>
            <ol>
                <li>Cliquez sur "Générer Favicon" pour créer les icônes</li>
                <li>Cliquez sur "Télécharger favicon.ico" pour sauvegarder le fichier</li>
                <li>Placez le fichier favicon.ico dans le dossier <code>public/</code> de votre projet</li>
                <li>Rechargez votre navigateur avec Ctrl+F5 (ou Cmd+Shift+R sur Mac)</li>
            </ol>
        </div>
    </div>

    <script>
        function generateFavicon() {
            // Générer favicon 32x32
            const canvas32 = document.getElementById('favicon32');
            const ctx32 = canvas32.getContext('2d');
            drawLogo(ctx32, 32);
            
            // Générer favicon 16x16
            const canvas16 = document.getElementById('favicon16');
            const ctx16 = canvas16.getContext('2d');
            drawLogo(ctx16, 16);
            
            document.getElementById('instructions').style.display = 'block';
        }
        
        function drawLogo(ctx, size) {
            // Fond
            ctx.fillStyle = '#F5F5F5';
            const radius = size * 0.2;
            roundRect(ctx, 0, 0, size, size, radius);
            ctx.fill();
            
            // Lignes
            ctx.strokeStyle = '#333333';
            ctx.lineWidth = Math.max(1, size * 0.08);
            ctx.lineCap = 'round';
            
            const startX = size * 0.25;
            const endX = size * 0.75;
            const y1 = size * 0.3;
            const y2 = size * 0.5;
            const y3 = size * 0.7;
            
            // Ligne 1
            ctx.beginPath();
            ctx.moveTo(startX, y1);
            ctx.lineTo(endX, y1);
            ctx.stroke();
            
            // Ligne 2
            ctx.beginPath();
            ctx.moveTo(startX, y2);
            ctx.lineTo(endX, y2);
            ctx.stroke();
            
            // Ligne 3
            ctx.beginPath();
            ctx.moveTo(startX, y3);
            ctx.lineTo(endX, y3);
            ctx.stroke();
            
            // Cercles
            const circleRadius = Math.max(1, size * 0.05);
            const circleX = size * 0.35;
            
            // Cercle vert
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(circleX, y1, circleRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Cercle bleu
            ctx.fillStyle = '#2196F3';
            ctx.beginPath();
            ctx.arc(circleX, y2, circleRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Cercle rouge
            ctx.fillStyle = '#F44336';
            ctx.beginPath();
            ctx.arc(circleX, y3, circleRadius, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        function downloadFavicon() {
            const canvas = document.getElementById('favicon32');
            const link = document.createElement('a');
            link.download = 'favicon.ico';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Générer automatiquement au chargement
        window.onload = function() {
            generateFavicon();
        };
    </script>
</body>
</html>
