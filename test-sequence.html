<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Séquence de Démarrage DataTec (Connexion → Splash → App)</title>
    <style>
        /* Variables CSS pour les thèmes */
        :root {
            --background-color: #181818;
            --text-color: #F8F9FA;
            --secondary-text-color: #DEE2E6;
            --card-background: rgba(40, 40, 40, 0.95);
            --border-color: rgba(255, 255, 255, 0.1);
            --primary-color: #667eea;
        }

        [data-theme="light"] {
            --background-color: #f5f7fa;
            --text-color: #333333;
            --secondary-text-color: #666666;
            --card-background: rgba(255, 255, 255, 0.98);
            --border-color: rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #2d2d2d 100%);
            color: var(--text-color);
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        [data-theme="light"] body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        /* Écran de connexion */
        .login-screen {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-container {
            background: var(--card-background);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            border-radius: 24px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        [data-theme="light"] .login-container {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-circle {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-circle img {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .app-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
        }

        .app-subtitle {
            font-size: 14px;
            color: var(--secondary-text-color);
            opacity: 0.8;
        }

        .login-button {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 12px;
            background: #007bff;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
        }

        .login-button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        /* Écran splash */
        .splash-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: var(--background-color);
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .splash-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .splash-logo {
            width: 80px;
            height: 80px;
            transform: scale(0.6);
            opacity: 0.1;
            transition: all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            margin-bottom: 30px;
        }

        .splash-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 30px;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .splash-progress-bar {
            width: 120px;
            height: 2px;
            background-color: #333333;
            border-radius: 1px;
            overflow: hidden;
            opacity: 0;
            transform: scaleX(0.9);
            transition: opacity 0.5s ease, transform 0.5s ease;
            position: relative;
        }

        [data-theme="light"] .splash-progress-bar {
            background-color: #e0e0e0;
        }

        .splash-progress-fill {
            height: 100%;
            width: 0%;
            background-color: var(--text-color);
            border-radius: 1px;
            transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Écran principal */
        .main-screen {
            display: none;
            min-height: 100vh;
            padding: 40px;
            text-align: center;
        }

        .main-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .welcome-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-color);
        }

        .welcome-subtitle {
            font-size: 18px;
            color: var(--secondary-text-color);
            margin-bottom: 40px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-top: 40px;
        }

        .feature-card {
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            text-align: left;
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .feature-description {
            font-size: 14px;
            color: var(--secondary-text-color);
            line-height: 1.5;
        }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            z-index: 10000;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        .hidden {
            display: none !important;
        }

        .visible {
            display: flex !important;
        }
    </style>
</head>
<body>
    <div class="theme-toggle" onclick="toggleTheme()">🌙</div>

    <!-- 1. Écran de connexion -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="logo-section">
                <div class="logo-circle">
                    <img src="static/logo-light.svg" alt="DataTec Logo" id="loginLogo">
                </div>
                <h1 class="app-title">DataTec Workspace</h1>
                <p class="app-subtitle">Connectez-vous pour continuer</p>
            </div>

            <button class="login-button" onclick="startLogin()">
                Se connecter (Demo)
            </button>

            <div style="text-align: center; margin-top: 24px; font-size: 12px; color: var(--secondary-text-color); opacity: 0.6;">
                © 2024 DataTec Workspace. Tous droits réservés.
            </div>
        </div>
    </div>

    <!-- 2. Écran splash -->
    <div id="splashScreen" class="splash-screen">
        <div class="splash-container">
            <img src="static/logo-light.svg" alt="DataTec Logo" class="splash-logo" id="splashLogo">
            <h1 class="splash-title">DataTec Workspace</h1>
            <div class="splash-progress-bar">
                <div class="splash-progress-fill"></div>
            </div>
        </div>
    </div>

    <!-- 3. Écran principal -->
    <div id="mainScreen" class="main-screen">
        <div class="main-content">
            <h1 class="welcome-title">Bienvenue dans DataTec Workspace</h1>
            <p class="welcome-subtitle">Votre environnement de travail intelligent et moderne</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔐</div>
                    <h3 class="feature-title">Authentification Sécurisée</h3>
                    <p class="feature-description">Système de connexion moderne avec support des thèmes sombre/clair</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3 class="feature-title">Interface Moderne</h3>
                    <p class="feature-description">Design élégant et responsive qui s'adapte à tous les appareils</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">Performance Optimisée</h3>
                    <p class="feature-description">Application rapide et fluide pour une expérience utilisateur optimale</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🌙</div>
                    <h3 class="feature-title">Thèmes Adaptatifs</h3>
                    <p class="feature-description">Basculement automatique entre les modes sombre et clair</p>
                </div>
            </div>

            <button class="login-button" onclick="restart()" style="margin-top: 40px; max-width: 200px;">
                Recommencer la séquence
            </button>
        </div>
    </div>

    <script>
        let currentStep = 1;

        // Gestion du thème
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            // Changer les logos
            const loginLogo = document.getElementById('loginLogo');
            const splashLogo = document.getElementById('splashLogo');
            const logoSrc = newTheme === 'dark' ? 'static/logo-light.svg' : 'static/logo-dark.svg';
            
            if (loginLogo) loginLogo.src = logoSrc;
            if (splashLogo) splashLogo.src = logoSrc;
            
            // Changer l'icône du toggle
            const toggle = document.querySelector('.theme-toggle');
            toggle.textContent = newTheme === 'dark' ? '🌙' : '☀️';
            
            localStorage.setItem('theme', newTheme);
        }

        // Initialiser le thème
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const loginLogo = document.getElementById('loginLogo');
            const splashLogo = document.getElementById('splashLogo');
            const logoSrc = savedTheme === 'dark' ? 'static/logo-light.svg' : 'static/logo-dark.svg';
            
            if (loginLogo) loginLogo.src = logoSrc;
            if (splashLogo) splashLogo.src = logoSrc;
            
            document.querySelector('.theme-toggle').textContent = savedTheme === 'dark' ? '🌙' : '☀️';
        }

        // Étape 1: Connexion
        function startLogin() {
            const button = event.target;
            button.textContent = 'Connexion...';
            button.disabled = true;

            setTimeout(() => {
                showSplash();
            }, 1500);
        }

        // Étape 2: Splash
        function showSplash() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('splashScreen').classList.add('visible');

            // Démarrer l'animation après un court délai
            setTimeout(() => {
                const logo = document.querySelector('.splash-logo');
                const title = document.querySelector('.splash-title');
                const progressBar = document.querySelector('.splash-progress-bar');
                const progressFill = document.querySelector('.splash-progress-fill');

                // Animation du logo
                logo.style.transform = 'scale(1)';
                logo.style.opacity = '1';

                // Animation du titre après 500ms
                setTimeout(() => {
                    title.style.opacity = '1';
                    title.style.transform = 'translateY(0)';
                }, 500);

                // Animation de la barre de progression après 800ms
                setTimeout(() => {
                    progressBar.style.opacity = '1';
                    progressBar.style.transform = 'scaleX(1)';
                    
                    // Démarrer l'animation de remplissage
                    progressFill.style.width = '100%';
                }, 800);
            }, 200);

            // Passer à l'écran principal après 3 secondes
            setTimeout(() => {
                showMainScreen();
            }, 3000);
        }

        // Étape 3: Écran principal
        function showMainScreen() {
            document.getElementById('splashScreen').classList.remove('visible');
            document.getElementById('splashScreen').classList.add('hidden');
            document.getElementById('mainScreen').style.display = 'block';
            currentStep = 3;
        }

        // Recommencer la séquence
        function restart() {
            // Réinitialiser tous les écrans
            document.getElementById('mainScreen').style.display = 'none';
            document.getElementById('splashScreen').classList.remove('visible');
            document.getElementById('splashScreen').classList.add('hidden');
            document.getElementById('loginScreen').classList.remove('hidden');

            // Réinitialiser le bouton de connexion
            const button = document.querySelector('.login-button');
            button.textContent = 'Se connecter (Demo)';
            button.disabled = false;

            // Réinitialiser les animations du splash
            const logo = document.querySelector('.splash-logo');
            const title = document.querySelector('.splash-title');
            const progressBar = document.querySelector('.splash-progress-bar');
            const progressFill = document.querySelector('.splash-progress-fill');

            logo.style.transform = 'scale(0.6)';
            logo.style.opacity = '0.1';
            title.style.opacity = '0';
            title.style.transform = 'translateY(10px)';
            progressBar.style.opacity = '0';
            progressBar.style.transform = 'scaleX(0.9)';
            progressFill.style.width = '0%';

            currentStep = 1;
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
        });
    </script>
</body>
</html>
