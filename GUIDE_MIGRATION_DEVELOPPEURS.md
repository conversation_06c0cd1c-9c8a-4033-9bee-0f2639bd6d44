# 👨‍💻 GUIDE DE MIGRATION POUR DÉVELOPPEURS

## 🎯 Objectif

Ce guide aide les développeurs à migrer du système d'authentification legacy vers le nouveau système basé sur Dexie.js.

---

## 🔄 Comparaison Ancien vs Nouveau

### 📊 Architecture

| Aspect | Ancien Système | Nouveau Système |
|--------|----------------|-----------------|
| **Stockage** | localStorage | Dexie.js (IndexedDB) |
| **Authentification** | Simple token | Sessions sécurisées + RBAC |
| **Permissions** | Basique (admin/user) | Granulaires (resource.action) |
| **Sécurité** | Limitée | Hashage bcrypt + audit |
| **Performance** | Synchrone | Asynchrone optimisé |
| **Capacité** | ~5-10MB | ~50-100MB+ |

### 🔧 APIs

#### Ancien Système
```typescript
// authAtoms.ts
const [auth, setAuth] = useAtom(authAtom)

// Connexion simple
setAuth({ user: userData, token: 'simple-token' })

// Vérification basique
if (auth.user?.role === 'admin') {
  // Action admin
}
```

#### Nouveau Système
```typescript
// newAuthAtoms.ts
const [authState] = useAtom(newAuthAtom)
const [, login] = useAtom(loginAtom)
const [, checkPermission] = useAtom(checkPermissionAtom)

// Connexion sécurisée
const result = await login({
  username: 'admin',
  password: 'admin123'
})

// Vérification granulaire
const canCreate = await checkPermission({
  resource: 'user',
  action: 'create'
})
```

---

## 🚀 Migration Étape par Étape

### 1. Remplacer les Imports

#### ❌ Ancien
```typescript
import { authAtom } from '@/stores/atoms/authAtoms'
import { useAtom } from 'jotai'

const [auth, setAuth] = useAtom(authAtom)
```

#### ✅ Nouveau
```typescript
import { 
  newAuthAtom, 
  loginAtom, 
  logoutAtom,
  checkPermissionAtom 
} from '@/stores/atoms/newAuthAtoms'
import { useAtom } from 'jotai'

const [authState] = useAtom(newAuthAtom)
const [, login] = useAtom(loginAtom)
const [, logout] = useAtom(logoutAtom)
const [, checkPermission] = useAtom(checkPermissionAtom)
```

### 2. Migrer l'Authentification

#### ❌ Ancien
```typescript
// Connexion
const handleLogin = (username: string, password: string) => {
  // Validation basique côté client
  if (username === 'admin' && password === 'admin') {
    setAuth({
      user: { username, role: 'admin' },
      token: 'simple-token'
    })
  }
}

// Déconnexion
const handleLogout = () => {
  setAuth({ user: null, token: null })
}
```

#### ✅ Nouveau
```typescript
// Connexion
const handleLogin = async (username: string, password: string) => {
  const result = await login({
    username,
    password,
    rememberMe: false
  })
  
  if (result.success) {
    console.log('Connecté:', result.user)
  } else {
    console.error('Erreur:', result.error)
  }
}

// Déconnexion
const handleLogout = async () => {
  const success = await logout()
  console.log('Déconnecté:', success)
}
```

### 3. Migrer les Vérifications de Permissions

#### ❌ Ancien
```typescript
// Vérification simple
const isAdmin = auth.user?.role === 'admin'
const canManageUsers = isAdmin

// Affichage conditionnel
{isAdmin && (
  <AdminPanel />
)}
```

#### ✅ Nouveau
```typescript
// Vérification granulaire
const [canManageUsers, setCanManageUsers] = useState(false)

useEffect(() => {
  const checkUserPermission = async () => {
    const hasPermission = await checkPermission({
      resource: 'user',
      action: 'create'
    })
    setCanManageUsers(hasPermission)
  }
  
  if (authState.isAuthenticated) {
    checkUserPermission()
  }
}, [authState.isAuthenticated, checkPermission])

// Affichage conditionnel
{canManageUsers && (
  <AdminPanel />
)}
```

### 4. Migrer les Composants

#### ❌ Ancien LoginScreen
```typescript
const LoginScreen = () => {
  const [auth, setAuth] = useAtom(authAtom)
  
  const handleSubmit = (e) => {
    e.preventDefault()
    // Logique de connexion simple
    setAuth({ user: userData, token: 'token' })
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Formulaire basique */}
    </form>
  )
}
```

#### ✅ Nouveau LoginScreen
```typescript
import { NewLoginScreen } from '@/components/NewLoginScreen'

const LoginScreen = () => {
  const handleLoginSuccess = () => {
    // Redirection après connexion réussie
    navigate('/dashboard')
  }
  
  return (
    <NewLoginScreen 
      onLoginSuccess={handleLoginSuccess}
      showLogo={true}
      allowGuestAccess={false}
    />
  )
}
```

---

## 🔧 Patterns de Migration

### 1. Hook Personnalisé pour l'Auth

#### ✅ Nouveau Hook
```typescript
// hooks/useAuth.ts
import { useAtom } from 'jotai'
import { 
  newAuthAtom, 
  loginAtom, 
  logoutAtom,
  checkPermissionAtom 
} from '@/stores/atoms/newAuthAtoms'

export const useAuth = () => {
  const [authState] = useAtom(newAuthAtom)
  const [, login] = useAtom(loginAtom)
  const [, logout] = useAtom(logoutAtom)
  const [, checkPermission] = useAtom(checkPermissionAtom)
  
  return {
    // État
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    isLoading: authState.isLoading,
    error: authState.error,
    
    // Actions
    login,
    logout,
    checkPermission,
    
    // Utilitaires
    isAdmin: authState.user?.role === 'admin',
    isUser: authState.user?.role === 'user'
  }
}
```

### 2. Hook pour les Permissions

#### ✅ Hook de Permissions
```typescript
// hooks/usePermissions.ts
import { useState, useEffect } from 'react'
import { useAtom } from 'jotai'
import { checkPermissionAtom, newAuthAtom } from '@/stores/atoms/newAuthAtoms'

export const usePermission = (resource: string, action: string) => {
  const [authState] = useAtom(newAuthAtom)
  const [, checkPermission] = useAtom(checkPermissionAtom)
  const [hasPermission, setHasPermission] = useState(false)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    const check = async () => {
      if (!authState.isAuthenticated) {
        setHasPermission(false)
        setLoading(false)
        return
      }
      
      setLoading(true)
      const result = await checkPermission({ resource, action })
      setHasPermission(result)
      setLoading(false)
    }
    
    check()
  }, [authState.isAuthenticated, resource, action, checkPermission])
  
  return { hasPermission, loading }
}

// Utilisation
const { hasPermission, loading } = usePermission('user', 'create')
```

### 3. Composant de Protection

#### ✅ Composant ProtectedRoute
```typescript
// components/ProtectedRoute.tsx
import React from 'react'
import { usePermission } from '@/hooks/usePermissions'
import { useAuth } from '@/hooks/useAuth'

interface ProtectedRouteProps {
  children: React.ReactNode
  resource?: string
  action?: string
  fallback?: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  resource,
  action,
  fallback = <div>Accès refusé</div>
}) => {
  const { isAuthenticated, isLoading } = useAuth()
  const { hasPermission, loading } = usePermission(
    resource || 'user', 
    action || 'read'
  )
  
  if (isLoading || loading) {
    return <div>Chargement...</div>
  }
  
  if (!isAuthenticated) {
    return <div>Non connecté</div>
  }
  
  if (resource && action && !hasPermission) {
    return fallback
  }
  
  return <>{children}</>
}

// Utilisation
<ProtectedRoute resource="user" action="create">
  <CreateUserButton />
</ProtectedRoute>
```

---

## 🧪 Tests de Migration

### 1. Test de Compatibilité

```typescript
// tests/migration.test.ts
import { describe, it, expect } from 'vitest'
import { userManagementService } from '@/services/UserManagementService'

describe('Migration Compatibility', () => {
  it('should maintain user data after migration', async () => {
    await userManagementService.initialize()
    
    const adminUser = await userManagementService.getUserByUsername('admin')
    expect(adminUser).toBeTruthy()
    expect(adminUser?.role).toBe('admin')
  })
  
  it('should preserve authentication flow', async () => {
    const result = await userManagementService.login({
      username: 'admin',
      password: 'admin123'
    })
    
    expect(result.success).toBe(true)
    expect(result.user).toBeTruthy()
    expect(result.token).toBeTruthy()
  })
})
```

### 2. Test de Performance

```typescript
// tests/performance.test.ts
describe('Performance Tests', () => {
  it('should login within acceptable time', async () => {
    const start = Date.now()
    
    const result = await userManagementService.login({
      username: 'admin',
      password: 'admin123'
    })
    
    const duration = Date.now() - start
    expect(duration).toBeLessThan(1000) // < 1 seconde
    expect(result.success).toBe(true)
  })
})
```

---

## 🚨 Points d'Attention

### ⚠️ Changements Majeurs

1. **Asynchrone par défaut**
   - Toutes les opérations d'auth sont maintenant async
   - Utiliser `await` ou `.then()` partout

2. **Structure des données**
   - `auth.user` → `authState.user`
   - `auth.token` → `authState.token`
   - Nouveaux champs: `isLoading`, `error`, `initialized`

3. **Permissions granulaires**
   - Plus de simple `role === 'admin'`
   - Utiliser `checkPermission(resource, action)`

4. **Gestion d'erreurs**
   - Erreurs plus détaillées avec codes
   - Gestion des états de chargement

### 🔧 Outils de Migration

#### Script de Migration Automatique
```bash
# Rechercher et remplacer les anciens patterns
grep -r "authAtom" src/ --include="*.ts" --include="*.tsx"
grep -r "auth.user?.role" src/ --include="*.ts" --include="*.tsx"
```

#### Checklist de Migration
- [ ] Remplacer tous les imports `authAtom`
- [ ] Convertir les vérifications de rôles en permissions
- [ ] Ajouter la gestion async/await
- [ ] Tester tous les composants d'authentification
- [ ] Valider les redirections après connexion
- [ ] Vérifier la persistance des sessions

---

## 📚 Ressources

### 📖 Documentation
- [NOUVEAU_SYSTEME_AUTH.md](./NOUVEAU_SYSTEME_AUTH.md) - Documentation complète
- [MIGRATION_PROGRESS.md](./MIGRATION_PROGRESS.md) - Progression de la migration

### 🔧 Outils
- Console: `runSystemValidation()` - Tests automatiques
- Console: `testDatabase()` - Test de la base de données
- Console: `showDatabaseInfo()` - Informations de debug

### 🆘 Support
- Logs détaillés dans la console du navigateur
- Tests de validation automatiques
- Système de fallback vers l'ancien système

---

**La migration vers le nouveau système d'authentification améliore significativement la sécurité et les fonctionnalités de DataTec ! 🚀**
