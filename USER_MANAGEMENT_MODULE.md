# 👥 Module de Gestion des Utilisateurs - DataTec

## 📋 Vue d'ensemble

Le module de gestion des utilisateurs de DataTec implémente un système complet d'authentification et d'autorisation avec contrôle d'accès basé sur les rôles (RBAC). Il permet la gestion complète des utilisateurs, des permissions et de la sécurité de l'application.

## 🏗️ Architecture

### Structure des fichiers

```
src/
├── shared/types/
│   └── user.ts                    # Types TypeScript pour la gestion utilisateur
├── renderer/
│   ├── services/
│   │   ├── AuthService.ts         # Service d'authentification
│   │   ├── UserService.ts         # Service de gestion des utilisateurs
│   │   └── PermissionService.ts   # Service de gestion des permissions
│   ├── stores/atoms/
│   │   └── authAtoms.ts          # États Jotai pour l'authentification
│   ├── components/user-management/
│   │   ├── UserCard.tsx          # Carte d'affichage utilisateur
│   │   ├── UserForm.tsx          # Formulaire création/édition
│   │   ├── UserFilters.tsx       # Filtres et recherche
│   │   ├── LoginForm.tsx         # Formulaire de connexion
│   │   ├── AdminDashboard.tsx    # Tableau de bord admin
│   │   ├── AuditLog.tsx          # Journal d'activité
│   │   ├── ProtectedRoute.tsx    # Protection des routes
│   │   ├── SettingsProtection.tsx # Protection des paramètres
│   │   └── AuthInitializer.tsx   # Initialisation auth
│   └── routes/settings/
│       ├── users.tsx             # Page de gestion des utilisateurs
│       └── admin.tsx             # Page d'administration
```

## 🔐 Système d'Authentification

### Rôles Utilisateur

1. **Admin** - Accès complet au système
   - Gestion des utilisateurs
   - Administration système
   - Accès à tous les paramètres
   - Journal d'activité

2. **Modérateur** - Gestion du contenu et des utilisateurs
   - Lecture/modification des utilisateurs
   - Gestion des conversations
   - Base de connaissances
   - Lecture des paramètres

3. **Utilisateur** - Accès standard
   - Gestion de ses conversations
   - Lecture des paramètres de base
   - Accès aux modèles et fournisseurs

4. **Invité** - Accès limité en lecture seule
   - Lecture des conversations publiques
   - Base de connaissances publique

### Permissions par Ressource

| Ressource | Admin | Modérateur | Utilisateur | Invité |
|-----------|-------|------------|-------------|--------|
| Utilisateurs | MANAGE | READ/UPDATE | - | - |
| Système | MANAGE | - | - | - |
| Paramètres | MANAGE | READ | READ | - |
| Fournisseurs | MANAGE | READ | READ | - |
| Modèles | MANAGE | READ | READ | - |
| Base de connaissances | MANAGE | MANAGE | READ | READ |
| Conversations | MANAGE | MANAGE | MANAGE | READ |

## 🛠️ Services

### AuthService
- Connexion/déconnexion
- Gestion des sessions
- Vérification des tokens
- Contrôle des rôles

### UserService
- CRUD des utilisateurs
- Recherche et filtrage
- Statistiques utilisateur
- Gestion des statuts

### PermissionService
- Vérification des permissions
- Contrôle d'accès aux pages
- Hiérarchie des rôles
- Conditions d'accès

## 🎨 Composants d'Interface

### UserCard
Affiche les informations d'un utilisateur avec :
- Avatar et informations de base
- Badge de rôle coloré
- Indicateur de statut en ligne
- Menu d'actions contextuelles

### UserForm
Formulaire de création/édition avec :
- Validation des champs
- Gestion des rôles
- Paramètres de langue
- Activation/désactivation

### UserFilters
Système de filtrage avancé :
- Recherche textuelle
- Filtres par rôle et statut
- Tri multi-critères
- Pagination

### AdminDashboard
Tableau de bord administrateur :
- Statistiques générales
- Graphiques de répartition
- Utilisateurs les plus actifs
- Métriques d'activité

### AuditLog
Journal d'activité complet :
- Historique des actions
- Filtrage par utilisateur/action
- Détails des modifications
- Traçabilité complète

## 🔒 Protection et Sécurité

### ProtectedRoute
Composant HOC pour protéger les routes :
```tsx
<ProtectedRoute
  requiredPermission={{
    action: PermissionAction.READ,
    resource: PermissionResource.USER
  }}
>
  <UserManagement />
</ProtectedRoute>
```

### PermissionGuard
Protection granulaire des composants :
```tsx
<PermissionGuard
  permission={{
    action: PermissionAction.CREATE,
    resource: PermissionResource.USER
  }}
>
  <CreateUserButton />
</PermissionGuard>
```

### Hooks Utilitaires
```tsx
// Vérifier une permission
const canEdit = usePermission(PermissionAction.UPDATE, PermissionResource.USER)

// Vérifier un rôle
const isAdmin = useRole(UserRole.ADMIN)

// Vérifier l'accès à une page
const hasAccess = usePageAccess('/settings/users')
```

## 📊 Données de Démonstration

### Comptes de Test

| Username | Password | Rôle | Description |
|----------|----------|------|-------------|
| admin | admin123 | Admin | Compte administrateur complet |
| laurie.poiret | password123 | Admin | Administrateur secondaire |
| maggie.davidson | password123 | Modérateur | Modérateur de contenu |
| demo | demo123 | Utilisateur | Compte utilisateur standard |

### Fonctionnalités Démo
- 4 utilisateurs pré-configurés
- Statistiques d'activité simulées
- Journal d'audit avec historique
- Permissions complètement fonctionnelles

## 🚀 Utilisation

### Accès au Module
1. Se connecter avec un compte admin/modérateur
2. Aller dans **Paramètres > Utilisateurs**
3. Gérer les utilisateurs via l'interface

### Fonctionnalités Disponibles
- ✅ Création/modification/suppression d'utilisateurs
- ✅ Attribution et modification des rôles
- ✅ Activation/désactivation des comptes
- ✅ Recherche et filtrage avancés
- ✅ Tableau de bord administrateur
- ✅ Journal d'activité complet
- ✅ Protection par permissions
- ✅ Interface responsive

### Administration Système
Pour les administrateurs uniquement :
1. **Paramètres > Administration**
2. Onglets disponibles :
   - **Tableau de bord** : Statistiques et métriques
   - **Journal d'activité** : Historique complet
   - **Système** : Informations techniques

## 🔧 Configuration

### Personnalisation des Rôles
Modifier `src/shared/types/user.ts` :
```typescript
export enum UserRole {
  ADMIN = 'Admin',
  MODERATOR = 'Modérateur',
  USER = 'Utilisateur',
  GUEST = 'Invité',
  // Ajouter de nouveaux rôles ici
}
```

### Ajout de Permissions
Modifier `src/renderer/services/PermissionService.ts` :
```typescript
this.setRolePermissions(UserRole.CUSTOM, [
  { id: 'custom-permission', action: PermissionAction.READ, resource: PermissionResource.CUSTOM, description: 'Permission personnalisée' }
])
```

## 🧪 Tests

### Tests Manuels Recommandés
1. **Authentification**
   - Connexion avec différents rôles
   - Déconnexion et reconnexion
   - Gestion des sessions

2. **Gestion des Utilisateurs**
   - Création d'utilisateurs
   - Modification des rôles
   - Activation/désactivation
   - Suppression

3. **Permissions**
   - Accès aux différentes pages selon le rôle
   - Affichage conditionnel des boutons
   - Protection des actions sensibles

4. **Interface**
   - Responsive design
   - Filtres et recherche
   - Pagination
   - Thèmes clair/sombre

## 📈 Métriques et Monitoring

### Statistiques Disponibles
- Nombre total d'utilisateurs
- Utilisateurs actifs/inactifs
- Nouveaux utilisateurs par période
- Répartition par rôles
- Utilisateurs les plus actifs

### Journal d'Audit
- Actions utilisateur tracées
- Détails des modifications
- Adresses IP et user agents
- Horodatage précis
- Filtrage et recherche

## 🔮 Évolutions Futures

### Fonctionnalités Prévues
- [ ] Authentification à deux facteurs (2FA)
- [ ] Intégration LDAP/Active Directory
- [ ] API REST pour gestion externe
- [ ] Notifications en temps réel
- [ ] Sauvegarde/restauration des données
- [ ] Rapports d'activité avancés
- [ ] Gestion des groupes d'utilisateurs
- [ ] Permissions granulaires par fonctionnalité

### Améliorations Techniques
- [ ] Tests unitaires automatisés
- [ ] Base de données persistante
- [ ] Chiffrement avancé des mots de passe
- [ ] Rate limiting et protection CSRF
- [ ] Logs de sécurité centralisés

## 📞 Support

Pour toute question ou problème concernant le module de gestion des utilisateurs :
1. Consulter cette documentation
2. Vérifier les logs de la console
3. Tester avec les comptes de démonstration
4. Contacter l'équipe de développement

---

**Version** : 1.0.0  
**Dernière mise à jour** : 19 Juillet 2025  
**Statut** : ✅ Fonctionnel et prêt pour la production
