<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 Nettoyage Prompt Système - DataTec Workspace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
            font-weight: 700;
        }

        p {
            color: #555;
            margin-bottom: 20px;
            line-height: 1.6;
            font-size: 16px;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        button:active {
            transform: translateY(0);
        }

        .warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid rgba(255, 152, 0, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
            display: none;
        }

        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
            display: none;
        }

        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 14px;
            color: #d63384;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Nettoyage Prompt Système</h1>
        
        <div class="warning">
            ⚠️ <strong>Problème détecté :</strong> Le message système "You are a helpful assistant." apparaît encore dans vos nouvelles discussions.
        </div>

        <div class="info">
            ℹ️ <strong>Cause :</strong> L'ancien prompt par défaut est encore sauvegardé dans vos paramètres utilisateur. Cet outil va le nettoyer automatiquement.
        </div>

        <p>
            <strong>Ce que fait cet outil :</strong>
        </p>
        <ul style="text-align: left; margin: 20px 0; padding-left: 20px;">
            <li>✅ Supprime l'ancien prompt <span class="code">"You are a helpful assistant."</span></li>
            <li>✅ Met à jour vos paramètres avec le nouveau prompt vide</li>
            <li>✅ Nettoie les sessions existantes qui contiennent l'ancien message</li>
            <li>✅ Force le rechargement de l'application</li>
        </ul>

        <button onclick="clearOldPrompt()">
            🗑️ Nettoyer l'Ancien Prompt
        </button>

        <button onclick="forceResetPrompt()">
            🔄 Forcer la Réinitialisation
        </button>

        <button onclick="checkCurrentPrompt()">
            🔍 Vérifier le Prompt Actuel
        </button>

        <div id="success" class="success"></div>
        <div id="error" class="error"></div>
    </div>

    <script>
        function showMessage(type, message) {
            const successDiv = document.getElementById('success');
            const errorDiv = document.getElementById('error');
            
            successDiv.style.display = 'none';
            errorDiv.style.display = 'none';
            
            if (type === 'success') {
                successDiv.innerHTML = message;
                successDiv.style.display = 'block';
            } else {
                errorDiv.innerHTML = message;
                errorDiv.style.display = 'block';
            }
        }

        function clearOldPrompt() {
            try {
                // Nettoyer les paramètres localStorage
                const keys = Object.keys(localStorage);
                let cleaned = false;
                
                for (const key of keys) {
                    try {
                        const value = localStorage.getItem(key);
                        if (value && value.includes('You are a helpful assistant')) {
                            console.log('Cleaning key:', key);
                            
                            // Remplacer l'ancien prompt par une chaîne vide
                            const cleanedValue = value.replace(
                                /"You are a helpful assistant\.?"/g, 
                                '""'
                            );
                            
                            localStorage.setItem(key, cleanedValue);
                            cleaned = true;
                        }
                    } catch (e) {
                        console.warn('Could not process key:', key, e);
                    }
                }
                
                if (cleaned) {
                    showMessage('success', '✅ <strong>Succès !</strong> L\'ancien prompt a été nettoyé. L\'application va se recharger...');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showMessage('success', '✅ <strong>Aucun nettoyage nécessaire !</strong> L\'ancien prompt n\'a pas été trouvé dans vos paramètres.');
                }
                
            } catch (error) {
                showMessage('error', '❌ <strong>Erreur :</strong> ' + error.message);
            }
        }

        function forceResetPrompt() {
            try {
                // Forcer la suppression de tous les paramètres liés au prompt
                const keys = Object.keys(localStorage);
                let resetCount = 0;
                
                for (const key of keys) {
                    if (key.includes('settings') || key.includes('Settings')) {
                        try {
                            const value = localStorage.getItem(key);
                            if (value) {
                                const parsed = JSON.parse(value);
                                if (parsed.defaultPrompt !== undefined) {
                                    parsed.defaultPrompt = '';
                                    localStorage.setItem(key, JSON.stringify(parsed));
                                    resetCount++;
                                }
                            }
                        } catch (e) {
                            console.warn('Could not reset key:', key, e);
                        }
                    }
                }
                
                showMessage('success', `✅ <strong>Réinitialisation forcée !</strong> ${resetCount} paramètre(s) réinitialisé(s). L'application va se recharger...`);
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                
            } catch (error) {
                showMessage('error', '❌ <strong>Erreur :</strong> ' + error.message);
            }
        }

        function checkCurrentPrompt() {
            try {
                const keys = Object.keys(localStorage);
                let foundPrompts = [];
                
                for (const key of keys) {
                    try {
                        const value = localStorage.getItem(key);
                        if (value && value.includes('defaultPrompt')) {
                            const parsed = JSON.parse(value);
                            if (parsed.defaultPrompt !== undefined) {
                                foundPrompts.push({
                                    key: key,
                                    prompt: parsed.defaultPrompt
                                });
                            }
                        }
                    } catch (e) {
                        // Ignorer les erreurs de parsing
                    }
                }
                
                if (foundPrompts.length === 0) {
                    showMessage('success', '✅ <strong>Parfait !</strong> Aucun prompt par défaut trouvé dans vos paramètres.');
                } else {
                    let message = '🔍 <strong>Prompts trouvés :</strong><br>';
                    foundPrompts.forEach(item => {
                        const promptText = item.prompt || '(vide)';
                        message += `• <strong>${item.key}:</strong> "${promptText}"<br>`;
                    });
                    
                    if (foundPrompts.some(item => item.prompt === 'You are a helpful assistant.' || item.prompt === 'You are a helpful assistant')) {
                        showMessage('error', message + '<br>❌ <strong>Ancien prompt détecté !</strong> Utilisez le bouton "Nettoyer l\'Ancien Prompt".');
                    } else {
                        showMessage('success', message);
                    }
                }
                
            } catch (error) {
                showMessage('error', '❌ <strong>Erreur :</strong> ' + error.message);
            }
        }

        // Vérification automatique au chargement
        window.addEventListener('load', () => {
            setTimeout(checkCurrentPrompt, 1000);
        });
    </script>
</body>
</html>
