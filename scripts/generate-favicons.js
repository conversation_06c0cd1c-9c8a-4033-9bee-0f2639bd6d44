// Script pour générer les favicons PNG à partir des SVG
// Utilise sharp pour la conversion

const fs = require('fs')
const path = require('path')

// Fonction pour créer un favicon PNG à partir d'un SVG
function createFaviconPNG(svgContent, size, outputPath) {
  // Pour l'instant, on va créer un SVG optimisé
  // En production, vous pourriez utiliser sharp ou puppeteer pour la conversion
  
  const optimizedSvg = svgContent
    .replace('width="32"', `width="${size}"`)
    .replace('height="32"', `height="${size}"`)
  
  // Créer un fichier SVG temporaire optimisé
  const svgPath = outputPath.replace('.png', '.svg')
  fs.writeFileSync(svgPath, optimizedSvg)
  
  console.log(`✅ Favicon SVG créé: ${svgPath}`)
}

// Lire les fichiers SVG source
const faviconSvg = fs.readFileSync(path.join(__dirname, '../public/favicon.svg'), 'utf8')
const faviconDarkSvg = fs.readFileSync(path.join(__dirname, '../public/favicon-dark.svg'), 'utf8')

// Créer les différentes tailles
const sizes = [16, 32, 48, 64, 128, 256]

sizes.forEach(size => {
  // Favicon clair
  createFaviconPNG(
    faviconSvg, 
    size, 
    path.join(__dirname, `../public/favicon-${size}.png`)
  )
  
  // Favicon sombre
  createFaviconPNG(
    faviconDarkSvg, 
    size, 
    path.join(__dirname, `../public/favicon-dark-${size}.png`)
  )
})

console.log('🎉 Tous les favicons ont été générés !')
