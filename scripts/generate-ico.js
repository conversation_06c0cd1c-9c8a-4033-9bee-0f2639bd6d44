// Script pour générer un favicon.ico à partir du SVG
// Utilise les outils Node.js disponibles

const fs = require('fs')
const path = require('path')

// Lire le SVG source
const faviconSvgPath = path.join(__dirname, '../public/favicon.svg')
const faviconSvg = fs.readFileSync(faviconSvgPath, 'utf8')

// Pour l'instant, on va créer un fichier SVG optimisé comme fallback
// En production, vous pourriez utiliser sharp, puppeteer ou imagemagick

const optimizedSvg = faviconSvg
  .replace('width="32"', 'width="16"')
  .replace('height="32"', 'height="16"')

// Créer les fichiers ICO (en tant que SVG pour l'instant)
const publicIcoPath = path.join(__dirname, '../public/favicon.ico')
const rendererIcoPath = path.join(__dirname, '../src/renderer/favicon.ico')

// Copier le SVG comme ICO temporaire
fs.writeFileSync(publicIcoPath, faviconSvg)
fs.writeFileSync(rendererIcoPath, faviconSvg)

console.log('✅ Fichiers favicon.ico créés (format SVG temporaire)')
console.log('📁 Emplacements:')
console.log('  - public/favicon.ico')
console.log('  - src/renderer/favicon.ico')
console.log('')
console.log('💡 Pour un vrai fichier ICO, utilisez:')
console.log('  - scripts/create-favicon-ico.html (dans le navigateur)')
console.log('  - Ou un outil comme imagemagick/sharp en production')