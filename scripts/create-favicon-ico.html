<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON>er Favicon ICO DataTec</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #ddd;
            margin: 10px;
            border-radius: 4px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .preview {
            display: flex;
            justify-content: center;
            gap: 30px;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .favicon-size {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background: #e7f3ff;
            border-radius: 8px;
            text-align: left;
            display: none;
        }
        .logo-preview {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Générateur de Favicon DataTec</h1>
        <p>Génère un favicon.ico uniforme avec le logo exact de DataTec</p>
        
        <div class="logo-preview">
            <h3>Logo DataTec Original</h3>
            <svg width="80" height="80" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- Fond gris clair -->
                <rect width="100" height="100" rx="22" fill="#f5f5f5"/>
                
                <!-- Logo DataTec exact - 3 barres avec points colorés -->
                <g transform="translate(50, 50)">
                    <!-- Barre 1 avec point vert -->
                    <rect x="-28" y="-15" width="56" height="8" rx="4" fill="#2c2c2c"/>
                    <circle cx="-18" cy="-11" r="5" fill="#4CAF50"/>
                    
                    <!-- Barre 2 avec point bleu -->
                    <rect x="-28" y="-3" width="56" height="8" rx="4" fill="#2c2c2c"/>
                    <circle cx="-18" cy="1" r="5" fill="#2196F3"/>
                    
                    <!-- Barre 3 avec point rouge -->
                    <rect x="-28" y="9" width="56" height="8" rx="4" fill="#2c2c2c"/>
                    <circle cx="-18" cy="13" r="5" fill="#F44336"/>
                </g>
            </svg>
            <p><strong>3 barres horizontales</strong> avec points colorés : Vert, Bleu, Rouge</p>
        </div>
        
        <div class="preview">
            <div class="favicon-size">
                <h4>32x32</h4>
                <canvas id="favicon32" width="32" height="32"></canvas>
            </div>
            
            <div class="favicon-size">
                <h4>16x16</h4>
                <canvas id="favicon16" width="16" height="16"></canvas>
            </div>
        </div>
        
        <button onclick="generateFavicon()">🎨 Générer Favicon</button>
        <button onclick="downloadFavicon()" id="downloadBtn" style="display: none;">💾 Télécharger favicon.ico</button>
        
        <div id="instructions" class="instructions">
            <h3>📋 Instructions d'Installation :</h3>
            <ol>
                <li><strong>Téléchargez</strong> le fichier favicon.ico généré</li>
                <li><strong>Remplacez</strong> les fichiers existants :
                    <ul>
                        <li><code>public/favicon.ico</code></li>
                        <li><code>src/renderer/favicon.ico</code></li>
                    </ul>
                </li>
                <li><strong>Rechargez</strong> votre navigateur avec <kbd>Ctrl+F5</kbd> (ou <kbd>Cmd+Shift+R</kbd> sur Mac)</li>
                <li><strong>Vérifiez</strong> l'onglet du navigateur pour voir le nouveau favicon</li>
            </ol>
            
            <h4>🔧 Vérification :</h4>
            <ul>
                <li>Le favicon doit être identique dans toutes les interfaces</li>
                <li>Il doit s'adapter automatiquement au thème (clair/sombre)</li>
                <li>Le logo doit correspondre exactement à celui de votre système</li>
            </ul>
        </div>
    </div>

    <script>
        function drawLogo(ctx, size) {
            // Effacer le canvas
            ctx.clearRect(0, 0, size, size);
            
            // Calculer les proportions
            const scale = size / 100;
            const centerX = size / 2;
            const centerY = size / 2;
            
            // Fond gris clair avec coins arrondis
            ctx.fillStyle = '#f5f5f5';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, 22 * scale);
            ctx.fill();
            
            // Dessiner les 3 barres
            ctx.fillStyle = '#2c2c2c';
            
            // Barre 1
            ctx.beginPath();
            ctx.roundRect(
                centerX - 28 * scale, 
                centerY - 15 * scale, 
                56 * scale, 
                8 * scale, 
                4 * scale
            );
            ctx.fill();
            
            // Barre 2
            ctx.beginPath();
            ctx.roundRect(
                centerX - 28 * scale, 
                centerY - 3 * scale, 
                56 * scale, 
                8 * scale, 
                4 * scale
            );
            ctx.fill();
            
            // Barre 3
            ctx.beginPath();
            ctx.roundRect(
                centerX - 28 * scale, 
                centerY + 9 * scale, 
                56 * scale, 
                8 * scale, 
                4 * scale
            );
            ctx.fill();
            
            // Dessiner les points colorés
            const pointRadius = 5 * scale;
            const pointX = centerX - 18 * scale;
            
            // Point vert
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(pointX, centerY - 11 * scale, pointRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Point bleu
            ctx.fillStyle = '#2196F3';
            ctx.beginPath();
            ctx.arc(pointX, centerY + 1 * scale, pointRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Point rouge
            ctx.fillStyle = '#F44336';
            ctx.beginPath();
            ctx.arc(pointX, centerY + 13 * scale, pointRadius, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        // Polyfill pour roundRect si nécessaire
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        function generateFavicon() {
            // Générer favicon 32x32
            const canvas32 = document.getElementById('favicon32');
            const ctx32 = canvas32.getContext('2d');
            drawLogo(ctx32, 32);
            
            // Générer favicon 16x16
            const canvas16 = document.getElementById('favicon16');
            const ctx16 = canvas16.getContext('2d');
            drawLogo(ctx16, 16);
            
            // Afficher les instructions et le bouton de téléchargement
            document.getElementById('instructions').style.display = 'block';
            document.getElementById('downloadBtn').style.display = 'inline-block';
        }
        
        function downloadFavicon() {
            // Créer un canvas pour l'ICO (on utilise 32x32 comme base)
            const canvas = document.getElementById('favicon32');
            
            // Convertir en blob et télécharger
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'favicon.ico';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                alert('✅ Favicon téléchargé ! Suivez les instructions pour l\'installer.');
            }, 'image/png');
        }
        
        // Générer automatiquement au chargement
        window.onload = function() {
            generateFavicon();
        };
    </script>
</body>
</html>
