<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Force Reload Favicon DataTec</title>
    
    <!-- Favicon DataTec avec cache-busting forcé -->
    <link rel="icon" type="image/svg+xml" href="http://localhost:1212/favicon.svg?bust=1752885162322&v=516&force=1" media="(prefers-color-scheme: light)" />
    <link rel="icon" type="image/svg+xml" href="http://localhost:1212/favicon-dark.svg?bust=1752885162322&v=516&force=1" media="(prefers-color-scheme: dark)" />
    <link rel="icon" type="image/x-icon" href="http://localhost:1212/favicon.ico?bust=1752885162322&v=516&force=1" />
    
    <!-- Meta pour forcer le rechargement -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .favicon-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .favicon-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border-left: 5px solid #4CAF50;
        }
        
        .favicon-card h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .favicon-preview {
            margin: 20px 0;
        }
        
        .favicon-preview img {
            width: 64px;
            height: 64px;
            border-radius: 8px;
            background: white;
            padding: 8px;
            margin: 5px;
        }
        
        .url-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .action-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .action-button.danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .action-button.danger:hover {
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .actions {
            text-align: center;
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Force Reload Favicon DataTec</h1>
        
        <div class="success">
            <h3>✅ Nouveau Favicon DataTec Créé</h3>
            <p><strong>Timestamp:</strong> 1752885162322 | <strong>Version:</strong> v516</p>
            <p>Le favicon a été recréé avec le logo DataTec (3 barres + points colorés)</p>
        </div>
        
        <div class="favicon-test">
            <div class="favicon-card">
                <h3>🌞 Favicon Clair</h3>
                <div class="favicon-preview">
                    <img id="favicon-light" src="http://localhost:1212/favicon.svg?bust=1752885162322&v=516&force=1" alt="Favicon clair">
                </div>
                <div class="url-display">http://localhost:1212/favicon.svg</div>
                <div>Barres noires sur fond clair</div>
            </div>
            
            <div class="favicon-card">
                <h3>🌙 Favicon Sombre</h3>
                <div class="favicon-preview">
                    <img id="favicon-dark" src="http://localhost:1212/favicon-dark.svg?bust=1752885162322&v=516&force=1" alt="Favicon sombre">
                </div>
                <div class="url-display">http://localhost:1212/favicon-dark.svg</div>
                <div>Barres blanches sur fond sombre</div>
            </div>
            
            <div class="favicon-card">
                <h3>🖼️ Favicon ICO</h3>
                <div class="favicon-preview">
                    <img id="favicon-ico" src="http://localhost:1212/favicon.ico?bust=1752885162322&v=516&force=1" alt="Favicon ICO">
                </div>
                <div class="url-display">http://localhost:1212/favicon.ico</div>
                <div>Format ICO pour compatibilité</div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🚀 Instructions de Test</h3>
            <ol>
                <li><strong>Vérifiez</strong> que les 3 favicons ci-dessus s'affichent correctement</li>
                <li><strong>Ouvrez</strong> l'application principale : <a href="http://localhost:1212/" target="_blank" style="color: #FFC107;">http://localhost:1212/</a></li>
                <li><strong>Vérifiez</strong> le favicon dans l'onglet du navigateur</li>
                <li><strong>Testez</strong> différentes routes de l'application</li>
                <li><strong>Changez</strong> le thème système (clair/sombre) pour tester l'adaptation</li>
            </ol>
        </div>
        
        <div class="actions">
            <button class="action-button danger" onclick="clearAllCache()">
                🗑️ Vider le Cache
            </button>
            <button class="action-button" onclick="forceReload()">
                🔄 Recharger la Page
            </button>
            <a href="http://localhost:1212/" class="action-button" target="_blank">
                🏠 Ouvrir l'Application
            </a>
        </div>
    </div>
    
    <script>
        // Vérifier le chargement des favicons
        function checkFavicons() {
            const favicons = ['favicon-light', 'favicon-dark', 'favicon-ico'];
            
            favicons.forEach(id => {
                const img = document.getElementById(id);
                img.onload = function() {
                    this.parentNode.style.border = '2px solid #4CAF50';
                    console.log(`✅ ${id} chargé avec succès`);
                };
                img.onerror = function() {
                    this.parentNode.style.border = '2px solid #f44336';
                    console.log(`❌ ${id} échec du chargement`);
                };
            });
        }
        
        function clearAllCache() {
            // Vider tous les caches possibles
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            
            localStorage.clear();
            sessionStorage.clear();
            
            // Forcer le rechargement des favicons
            const links = document.querySelectorAll('link[rel*="icon"]');
            links.forEach(link => {
                const href = link.href.split('?')[0];
                link.href = href + '?bust=' + Date.now() + '&force=1';
            });
            
            alert('✅ Cache vidé !\n\n🔄 La page va se recharger automatiquement...');
            setTimeout(() => location.reload(true), 1000);
        }
        
        function forceReload() {
            location.reload(true);
        }
        
        // Vérifier les favicons au chargement
        checkFavicons();
        
        // Auto-vérification toutes les 3 secondes
        setInterval(checkFavicons, 3000);
    </script>
</body>
</html>
