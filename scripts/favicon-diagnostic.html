<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnostic Favicon - DataTec</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        
        .diagnostic-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        
        .favicon-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .favicon-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .favicon-preview {
            width: 64px;
            height: 64px;
            margin: 0 auto 15px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #ddd;
        }
        
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
        
        .status.success { background: #4CAF50; }
        .status.error { background: #f44336; }
        .status.warning { background: #ff9800; }
        
        .url-test {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            word-break: break-all;
        }
        
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .cache-info {
            background: rgba(33, 150, 243, 0.2);
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic Favicon DataTec</h1>
        
        <div class="diagnostic-section">
            <h3>📊 Test de Chargement des Favicons</h3>
            <div class="favicon-test">
                <div class="favicon-card">
                    <h4>Favicon Clair (SVG)</h4>
                    <div class="favicon-preview">
                        <img id="favicon-light" src="../public/favicon.svg" width="32" height="32" alt="Favicon clair">
                    </div>
                    <div id="status-light" class="status">Test en cours...</div>
                    <div class="url-test" id="url-light">../public/favicon.svg</div>
                </div>
                
                <div class="favicon-card">
                    <h4>Favicon Sombre (SVG)</h4>
                    <div class="favicon-preview">
                        <img id="favicon-dark" src="../public/favicon-dark.svg" width="32" height="32" alt="Favicon sombre">
                    </div>
                    <div id="status-dark" class="status">Test en cours...</div>
                    <div class="url-test" id="url-dark">../public/favicon-dark.svg</div>
                </div>
                
                <div class="favicon-card">
                    <h4>Favicon ICO</h4>
                    <div class="favicon-preview">
                        <img id="favicon-ico" src="../public/favicon.ico" width="32" height="32" alt="Favicon ICO">
                    </div>
                    <div id="status-ico" class="status">Test en cours...</div>
                    <div class="url-test" id="url-ico">../public/favicon.ico</div>
                </div>
            </div>
        </div>
        
        <div class="diagnostic-section">
            <h3>🌐 Test URLs avec Cache-Busting</h3>
            <div id="cache-busting-tests"></div>
        </div>
        
        <div class="cache-info">
            <h3>💾 Informations Cache Navigateur</h3>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>Timestamp actuel:</strong> <span id="current-timestamp"></span></p>
            <p><strong>Cache-Control:</strong> <span id="cache-control">Vérification...</span></p>
        </div>
        
        <div class="instructions">
            <h3>🛠️ Actions de Résolution</h3>
            <div style="text-align: center;">
                <button class="button" onclick="forceRefresh()">🔄 Rechargement Forcé</button>
                <button class="button" onclick="clearCache()">🗑️ Vider Cache</button>
                <button class="button" onclick="openPrivate()">🕵️ Mode Privé</button>
                <button class="button" onclick="testApp()">🚀 Tester App</button>
            </div>
            
            <h4>📋 Instructions Manuelles:</h4>
            <ol>
                <li><strong>Rechargement Forcé:</strong> Ctrl+Shift+R (PC) ou Cmd+Shift+R (Mac)</li>
                <li><strong>Vider Cache:</strong> F12 → Network → Clic droit sur Reload → "Empty Cache and Hard Reload"</li>
                <li><strong>Mode Privé:</strong> Ctrl+Shift+N (Chrome) ou Ctrl+Shift+P (Firefox)</li>
                <li><strong>Redémarrer:</strong> Fermez complètement le navigateur et rouvrez-le</li>
            </ol>
        </div>
        
        <div class="diagnostic-section">
            <h3>📈 Résultats du Diagnostic</h3>
            <div id="diagnostic-results"></div>
        </div>
    </div>
    
    <script>
        // Variables globales
        let testResults = {
            light: false,
            dark: false,
            ico: false
        };
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            updateTimestamp();
            detectBrowser();
            testFavicons();
            generateCacheBustingTests();
            setInterval(updateTimestamp, 1000);
        });
        
        function updateTimestamp() {
            document.getElementById('current-timestamp').textContent = new Date().toLocaleString();
        }
        
        function detectBrowser() {
            document.getElementById('user-agent').textContent = navigator.userAgent;
        }
        
        function testFavicons() {
            // Test favicon clair
            const lightImg = document.getElementById('favicon-light');
            lightImg.onload = () => updateStatus('light', true);
            lightImg.onerror = () => updateStatus('light', false);
            
            // Test favicon sombre
            const darkImg = document.getElementById('favicon-dark');
            darkImg.onload = () => updateStatus('dark', true);
            darkImg.onerror = () => updateStatus('dark', false);
            
            // Test favicon ICO
            const icoImg = document.getElementById('favicon-ico');
            icoImg.onload = () => updateStatus('ico', true);
            icoImg.onerror = () => updateStatus('ico', false);
        }
        
        function updateStatus(type, success) {
            const statusEl = document.getElementById(`status-${type}`);
            testResults[type] = success;
            
            if (success) {
                statusEl.textContent = '✅ Chargé';
                statusEl.className = 'status success';
            } else {
                statusEl.textContent = '❌ Erreur';
                statusEl.className = 'status error';
            }
            
            updateDiagnosticResults();
        }
        
        function generateCacheBustingTests() {
            const container = document.getElementById('cache-busting-tests');
            const timestamp = Date.now();
            
            const urls = [
                `../public/favicon.svg?bust=${timestamp}&v=507`,
                `../public/favicon-dark.svg?bust=${timestamp}&v=507`,
                `../public/favicon.ico?bust=${timestamp}&v=507`
            ];
            
            urls.forEach((url, index) => {
                const div = document.createElement('div');
                div.className = 'url-test';
                div.innerHTML = `
                    <strong>Test ${index + 1}:</strong><br>
                    <a href="${url}" target="_blank" style="color: #4CAF50;">${url}</a>
                `;
                container.appendChild(div);
            });
        }
        
        function updateDiagnosticResults() {
            const container = document.getElementById('diagnostic-results');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(Boolean).length;
            
            let resultHTML = `
                <p><strong>Tests Réussis:</strong> ${passedTests}/${totalTests}</p>
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
            `;
            
            if (passedTests === totalTests) {
                resultHTML += `
                    <div class="status success">✅ Tous les favicons se chargent correctement</div>
                    <p>Si vous ne voyez toujours pas le nouveau favicon dans l'onglet, le problème vient du cache du navigateur.</p>
                `;
            } else {
                resultHTML += `
                    <div class="status error">❌ Certains favicons ne se chargent pas</div>
                    <p>Vérifiez que les fichiers existent dans le dossier public/</p>
                `;
            }
            
            resultHTML += '</div>';
            container.innerHTML = resultHTML;
        }
        
        // Fonctions d'action
        function forceRefresh() {
            location.reload(true);
        }
        
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            alert('Cache vidé ! Effectuez maintenant un rechargement forcé (Ctrl+Shift+R)');
        }
        
        function openPrivate() {
            alert('Ouvrez une fenêtre de navigation privée et naviguez vers localhost:1212');
        }
        
        function testApp() {
            window.open('http://localhost:1212', '_blank');
        }
    </script>
</body>
</html>
