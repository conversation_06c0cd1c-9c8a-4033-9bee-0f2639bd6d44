#!/usr/bin/env node

/**
 * 🧪 Script de Test de Cohérence des Favicons
 * Vérifie que tous les favicons sont identiques et cohérents
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🧪 Test de Cohérence des Favicons DataTec');
console.log('==========================================');

// Configuration des fichiers à tester
const FAVICON_FILES = {
  svg_light: [
    'public/favicon.svg',
    'src/renderer/favicon.svg'
  ],
  svg_dark: [
    'public/favicon-dark.svg', 
    'src/renderer/favicon-dark.svg'
  ],
  ico: [
    'public/favicon.ico',
    'src/renderer/favicon.ico'
  ]
};

/**
 * Calculer le hash MD5 d'un fichier
 */
function getFileHash(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }
  
  const content = fs.readFileSync(filePath);
  return crypto.createHash('md5').update(content).digest('hex');
}

/**
 * Tester la cohérence des fichiers d'un même type
 */
function testFileConsistency(fileType, filePaths) {
  console.log(`\n📁 Test ${fileType.toUpperCase()}:`);
  
  const hashes = {};
  let allExist = true;
  let allIdentical = true;
  
  filePaths.forEach(filePath => {
    const hash = getFileHash(filePath);
    
    if (hash === null) {
      console.log(`❌ ${filePath} - MANQUANT`);
      allExist = false;
    } else {
      console.log(`✅ ${filePath} - ${hash.substring(0, 8)}...`);
      hashes[filePath] = hash;
    }
  });
  
  // Vérifier que tous les hashes sont identiques
  const uniqueHashes = [...new Set(Object.values(hashes))];
  
  if (uniqueHashes.length > 1) {
    console.log(`⚠️  ATTENTION: Les fichiers ${fileType} ne sont pas identiques !`);
    allIdentical = false;
    
    // Grouper par hash
    const hashGroups = {};
    Object.entries(hashes).forEach(([file, hash]) => {
      if (!hashGroups[hash]) hashGroups[hash] = [];
      hashGroups[hash].push(file);
    });
    
    Object.entries(hashGroups).forEach(([hash, files]) => {
      console.log(`   Hash ${hash.substring(0, 8)}: ${files.join(', ')}`);
    });
  } else if (uniqueHashes.length === 1) {
    console.log(`✅ Tous les fichiers ${fileType} sont identiques`);
  }
  
  return { allExist, allIdentical, fileCount: filePaths.length, hashCount: uniqueHashes.length };
}

/**
 * Vérifier le contenu SVG pour s'assurer qu'il contient le bon logo
 */
function validateSVGContent(filePath) {
  if (!fs.existsSync(filePath)) {
    return { valid: false, reason: 'Fichier manquant' };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Vérifications essentielles
  const checks = [
    { test: content.includes('viewBox="0 0 100 100"'), name: 'ViewBox correct' },
    { test: content.includes('width="32"') && content.includes('height="32"'), name: 'Dimensions correctes' },
    { test: content.includes('rx="22"'), name: 'Coins arrondis' },
    { test: content.includes('#4CAF50'), name: 'Point vert' },
    { test: content.includes('#2196F3'), name: 'Point bleu' },
    { test: content.includes('#F44336'), name: 'Point rouge' },
    { test: content.includes('transform="translate(50, 50)"'), name: 'Centrage correct' },
    { test: content.includes('width="56"') && content.includes('height="8"'), name: 'Barres correctes' }
  ];
  
  const failedChecks = checks.filter(check => !check.test);
  
  return {
    valid: failedChecks.length === 0,
    reason: failedChecks.length > 0 ? `Échecs: ${failedChecks.map(c => c.name).join(', ')}` : 'OK',
    passedChecks: checks.length - failedChecks.length,
    totalChecks: checks.length
  };
}

/**
 * Tester le contenu des fichiers SVG
 */
function testSVGContent() {
  console.log('\n🔍 Validation du contenu SVG:');
  
  const svgFiles = [
    ...FAVICON_FILES.svg_light,
    ...FAVICON_FILES.svg_dark
  ];
  
  let allValid = true;
  
  svgFiles.forEach(filePath => {
    const validation = validateSVGContent(filePath);
    
    if (validation.valid) {
      console.log(`✅ ${filePath} - Contenu valide (${validation.passedChecks}/${validation.totalChecks})`);
    } else {
      console.log(`❌ ${filePath} - ${validation.reason}`);
      allValid = false;
    }
  });
  
  return allValid;
}

/**
 * Vérifier les templates HTML
 */
function testHTMLTemplates() {
  console.log('\n📄 Vérification des templates HTML:');
  
  const templates = [
    'src/renderer/index.ejs',
    'src/renderer/index.web.ejs'
  ];
  
  let allValid = true;
  
  templates.forEach(templatePath => {
    if (!fs.existsSync(templatePath)) {
      console.log(`❌ ${templatePath} - MANQUANT`);
      allValid = false;
      return;
    }
    
    const content = fs.readFileSync(templatePath, 'utf8');
    
    // Vérifications des balises favicon
    const checks = [
      { test: content.includes('rel="icon"'), name: 'Balises icon présentes' },
      { test: content.includes('favicon.svg'), name: 'Référence favicon.svg' },
      { test: content.includes('favicon-dark.svg'), name: 'Référence favicon-dark.svg' },
      { test: content.includes('favicon.ico'), name: 'Référence favicon.ico' },
      { test: content.includes('prefers-color-scheme'), name: 'Support thème adaptatif' },
      { test: content.includes('?bust='), name: 'Cache busting présent' }
    ];
    
    const failedChecks = checks.filter(check => !check.test);
    
    if (failedChecks.length === 0) {
      console.log(`✅ ${templatePath} - Toutes les vérifications passées`);
    } else {
      console.log(`⚠️  ${templatePath} - Problèmes: ${failedChecks.map(c => c.name).join(', ')}`);
      allValid = false;
    }
  });
  
  return allValid;
}

/**
 * Fonction principale de test
 */
function main() {
  let overallSuccess = true;
  const results = {};
  
  // Test de cohérence des fichiers
  Object.entries(FAVICON_FILES).forEach(([fileType, filePaths]) => {
    const result = testFileConsistency(fileType, filePaths);
    results[fileType] = result;
    
    if (!result.allExist || !result.allIdentical) {
      overallSuccess = false;
    }
  });
  
  // Test du contenu SVG
  const svgValid = testSVGContent();
  if (!svgValid) overallSuccess = false;
  
  // Test des templates HTML
  const templatesValid = testHTMLTemplates();
  if (!templatesValid) overallSuccess = false;
  
  // Résumé final
  console.log('\n🎯 RÉSUMÉ FINAL:');
  console.log('================');
  
  Object.entries(results).forEach(([fileType, result]) => {
    const status = (result.allExist && result.allIdentical) ? '✅' : '❌';
    console.log(`${status} ${fileType.toUpperCase()}: ${result.fileCount} fichiers, ${result.hashCount} hash unique(s)`);
  });
  
  console.log(`${svgValid ? '✅' : '❌'} CONTENU SVG: Validation du logo DataTec`);
  console.log(`${templatesValid ? '✅' : '❌'} TEMPLATES HTML: Configuration des balises favicon`);
  
  if (overallSuccess) {
    console.log('\n🎉 SUCCÈS: Tous les favicons sont unifiés et cohérents !');
    console.log('');
    console.log('✨ Votre système de favicon DataTec est parfaitement configuré.');
    console.log('🚀 Vous pouvez maintenant tester sur toutes les routes.');
  } else {
    console.log('\n❌ ÉCHEC: Des problèmes de cohérence ont été détectés.');
    console.log('');
    console.log('🔧 Exécutez `node scripts/unify-favicons.js` pour corriger automatiquement.');
    process.exit(1);
  }
}

// Exécuter le script
if (require.main === module) {
  main();
}

module.exports = {
  testFileConsistency,
  validateSVGContent,
  testHTMLTemplates,
  getFileHash,
  FAVICON_FILES
};
