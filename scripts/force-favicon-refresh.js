#!/usr/bin/env node

/**
 * 🔄 Script de Force Refresh Favicon
 * Ce script force la mise à jour des favicons en modifiant les timestamps
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Force Refresh Favicon - DataTec');
console.log('=====================================');

// Chemins des fichiers à modifier
const files = [
  'src/renderer/index.ejs',
  'src/renderer/index.web.ejs'
];

// Générer un nouveau timestamp unique
const newTimestamp = Date.now();
const newVersion = Math.floor(Math.random() * 1000) + 8; // Version aléatoire > 7

console.log(`📅 Nouveau timestamp: ${newTimestamp}`);
console.log(`🔢 Nouvelle version: v${newVersion}`);

files.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ Fichier non trouvé: ${filePath}`);
    return;
  }
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Remplacer tous les paramètres de cache-busting
    content = content.replace(
      /href="([^"]*favicon[^"]*)\?[^"]*"/g, 
      `href="$1?bust=${newTimestamp}&v=${newVersion}"`
    );
    
    // Ajouter un commentaire avec timestamp pour forcer la recompilation
    const timestampComment = `<!-- Favicon refresh: ${new Date().toISOString()} -->`;
    
    if (!content.includes('<!-- Favicon refresh:')) {
      content = content.replace(
        '<!-- Force favicon refresh with unique timestamp -->',
        `${timestampComment}\n    <!-- Force favicon refresh with unique timestamp -->`
      );
    } else {
      content = content.replace(
        /<!-- Favicon refresh: [^>]* -->/,
        timestampComment
      );
    }
    
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ Mis à jour: ${filePath}`);
    
  } catch (error) {
    console.log(`❌ Erreur lors de la mise à jour de ${filePath}:`, error.message);
  }
});

console.log('');
console.log('🎯 Actions Recommandées:');
console.log('1. Redémarrez votre serveur de développement');
console.log('2. Effectuez un rechargement forcé (Ctrl+Shift+R)');
console.log('3. Testez en mode navigation privée');
console.log('4. Si nécessaire, videz complètement le cache du navigateur');
console.log('');
console.log('💡 Astuce: Fermez complètement le navigateur et rouvrez-le');
console.log('✨ Le nouveau favicon devrait maintenant apparaître!');
