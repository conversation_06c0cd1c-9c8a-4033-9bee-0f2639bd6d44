<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Settings Route Favicon</title>
    
    <!-- Favicon DataTec avec ultra cache-busting -->
    <link rel="icon" type="image/svg+xml" href="http://localhost:1212/favicon.svg?bust=1752885464290&v=1654&ultra=1" media="(prefers-color-scheme: light)" />
    <link rel="icon" type="image/svg+xml" href="http://localhost:1212/favicon-dark.svg?bust=1752885464290&v=1654&ultra=1" media="(prefers-color-scheme: dark)" />
    <link rel="icon" type="image/x-icon" href="http://localhost:1212/favicon.ico?bust=1752885464290&v=1654&ultra=1" />
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
        }
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .url-box {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            margin: 15px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Settings Route Favicon</h1>
        
        <h3>✅ Ultra Cache-Busting Appliqué</h3>
        <p><strong>Ultra Timestamp:</strong> 1752885464290</p>
        <p><strong>Ultra Version:</strong> v1654</p>
        
        <h3>🎯 Test de la Route Settings</h3>
        <div class="url-box">http://localhost:1212/settings/?force=1752885464290</div>
        
        <div style="text-align: center;">
            <a href="http://localhost:1212/settings/?force=1752885464290" class="button" target="_blank">
                🔗 Ouvrir Settings Route
            </a>
            <button class="button" onclick="clearCacheAndTest()">
                🗑️ Vider Cache + Test
            </button>
        </div>
        
        <h3>📋 Instructions:</h3>
        <ol>
            <li>Cliquez sur "Ouvrir Settings Route"</li>
            <li>Vérifiez le favicon dans l'onglet</li>
            <li>Si l'ancien favicon persiste, cliquez "Vider Cache + Test"</li>
            <li>Testez en mode navigation privée</li>
        </ol>
    </div>
    
    <script>
        function clearCacheAndTest() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            localStorage.clear();
            sessionStorage.clear();
            
            alert('✅ Cache vidé !\n\n🔄 Ouverture de la route settings...');
            setTimeout(() => {
                window.open('http://localhost:1212/settings/?force=1752885464290', '_blank');
            }, 1000);
        }
    </script>
</body>
</html>