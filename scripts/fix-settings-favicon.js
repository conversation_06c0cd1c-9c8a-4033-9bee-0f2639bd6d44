#!/usr/bin/env node

/**
 * 🔧 Fix Settings Route Favicon
 * Solution spécifique pour la route /settings/ qui garde l'ancien favicon
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fix Settings Route Favicon - DataTec');
console.log('=======================================');

// Générer un nouveau timestamp ultra-unique pour forcer le cache
const ultraTimestamp = Date.now() + Math.floor(Math.random() * 10000);
const ultraVersion = Math.floor(Math.random() * 1000) + 1000; // Version > 1000

console.log(`📅 Ultra timestamp: ${ultraTimestamp}`);
console.log(`🔢 Ultra version: v${ultraVersion}`);

// Templates à mettre à jour
const templates = [
  'src/renderer/index.ejs',
  'src/renderer/index.web.ejs'
];

console.log('\n🔄 Mise à jour des templates avec ultra cache-busting...');

templates.forEach(templatePath => {
  if (!fs.existsSync(templatePath)) {
    console.log(`⚠️  Template non trouvé: ${templatePath}`);
    return;
  }
  
  try {
    let content = fs.readFileSync(templatePath, 'utf8');
    
    // Remplacer TOUS les paramètres de cache-busting avec les nouveaux ultra-timestamps
    content = content.replace(
      /href="([^"]*favicon[^"]*)\?[^"]*"/g, 
      `href="$1?bust=${ultraTimestamp}&v=${ultraVersion}&ultra=1"`
    );
    
    // Ajouter un meta tag spécial pour forcer le rechargement
    const forceReloadMeta = `    <meta name="favicon-force-reload" content="${ultraTimestamp}" />`;
    
    if (!content.includes('name="favicon-force-reload"')) {
      content = content.replace(
        '<meta http-equiv="Expires" content="0" />',
        `<meta http-equiv="Expires" content="0" />\n${forceReloadMeta}`
      );
    } else {
      content = content.replace(
        /name="favicon-force-reload" content="[^"]*"/,
        `name="favicon-force-reload" content="${ultraTimestamp}"`
      );
    }
    
    // Mettre à jour le commentaire de timestamp
    const timestampComment = `<!-- Favicon ULTRA refresh: ${new Date().toISOString()} -->`;
    
    if (content.includes('<!-- Favicon refresh:')) {
      content = content.replace(
        /<!-- Favicon[^>]*refresh: [^>]* -->/,
        timestampComment
      );
    } else {
      content = content.replace(
        '<!-- Force favicon refresh with unique timestamp -->',
        `${timestampComment}\n    <!-- Force favicon refresh with unique timestamp -->`
      );
    }
    
    fs.writeFileSync(templatePath, content, 'utf8');
    console.log(`✅ Template mis à jour: ${templatePath}`);
    
  } catch (error) {
    console.log(`❌ Erreur lors de la mise à jour de ${templatePath}:`, error.message);
  }
});

console.log('\n🎯 Actions Spécifiques pour /settings/:');
console.log('=====================================');
console.log('1. 🔄 Redémarrez votre serveur de développement');
console.log('2. 🗑️  Videz complètement le cache du navigateur');
console.log('3. 🔒 Testez en mode navigation privée');
console.log('4. 🌐 Testez avec un autre navigateur');
console.log('5. 💻 Utilisez les DevTools pour désactiver le cache');
console.log('');
console.log('🎯 URL à tester:');
console.log(`   http://localhost:1212/settings/?force=${ultraTimestamp}`);
console.log('');
console.log('💡 Si le problème persiste:');
console.log('   - Fermez COMPLÈTEMENT le navigateur');
console.log('   - Rouvrez le navigateur');
console.log('   - Naviguez directement vers la route settings');
console.log('');
console.log('✨ Le favicon DataTec devrait maintenant apparaître sur /settings/ !');

// Créer un fichier de test spécifique pour la route settings
const settingsTestHtml = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Settings Route Favicon</title>
    
    <!-- Favicon DataTec avec ultra cache-busting -->
    <link rel="icon" type="image/svg+xml" href="http://localhost:1212/favicon.svg?bust=${ultraTimestamp}&v=${ultraVersion}&ultra=1" media="(prefers-color-scheme: light)" />
    <link rel="icon" type="image/svg+xml" href="http://localhost:1212/favicon-dark.svg?bust=${ultraTimestamp}&v=${ultraVersion}&ultra=1" media="(prefers-color-scheme: dark)" />
    <link rel="icon" type="image/x-icon" href="http://localhost:1212/favicon.ico?bust=${ultraTimestamp}&v=${ultraVersion}&ultra=1" />
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
        }
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .url-box {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            margin: 15px 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Settings Route Favicon</h1>
        
        <h3>✅ Ultra Cache-Busting Appliqué</h3>
        <p><strong>Ultra Timestamp:</strong> ${ultraTimestamp}</p>
        <p><strong>Ultra Version:</strong> v${ultraVersion}</p>
        
        <h3>🎯 Test de la Route Settings</h3>
        <div class="url-box">http://localhost:1212/settings/?force=${ultraTimestamp}</div>
        
        <div style="text-align: center;">
            <a href="http://localhost:1212/settings/?force=${ultraTimestamp}" class="button" target="_blank">
                🔗 Ouvrir Settings Route
            </a>
            <button class="button" onclick="clearCacheAndTest()">
                🗑️ Vider Cache + Test
            </button>
        </div>
        
        <h3>📋 Instructions:</h3>
        <ol>
            <li>Cliquez sur "Ouvrir Settings Route"</li>
            <li>Vérifiez le favicon dans l'onglet</li>
            <li>Si l'ancien favicon persiste, cliquez "Vider Cache + Test"</li>
            <li>Testez en mode navigation privée</li>
        </ol>
    </div>
    
    <script>
        function clearCacheAndTest() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            localStorage.clear();
            sessionStorage.clear();
            
            alert('✅ Cache vidé !\\n\\n🔄 Ouverture de la route settings...');
            setTimeout(() => {
                window.open('http://localhost:1212/settings/?force=${ultraTimestamp}', '_blank');
            }, 1000);
        }
    </script>
</body>
</html>`;

fs.writeFileSync('scripts/test-settings-favicon.html', settingsTestHtml);
console.log('📄 Créé: scripts/test-settings-favicon.html');
