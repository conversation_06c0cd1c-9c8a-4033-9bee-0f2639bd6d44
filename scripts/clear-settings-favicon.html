<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Nettoyage Favicon Settings - DataTec</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        
        .problem-section {
            background: rgba(255, 87, 87, 0.2);
            border: 2px solid #ff5757;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .solution-section {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .button.danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .button.danger:hover {
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }
        
        .url-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.red { background: #f44336; }
        .status-indicator.green { background: #4CAF50; }
        .status-indicator.orange { background: #ff9800; }
        
        .countdown {
            font-size: 1.5em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Nettoyage Favicon Settings</h1>
        
        <div class="problem-section">
            <h3>🚨 Problème Identifié</h3>
            <p><span class="status-indicator red"></span><strong>Route problématique :</strong></p>
            <div class="url-display">http://localhost:1212/settings/provider/chatbox-ai</div>
            <p>Cette route affiche encore l'ancien favicon malgré les mises à jour.</p>
        </div>
        
        <div class="solution-section">
            <h3>✅ Solution Appliquée</h3>
            <p><span class="status-indicator green"></span>Nouveau timestamp généré : <strong id="new-timestamp">1752871315847</strong></p>
            <p><span class="status-indicator green"></span>Nouvelle version : <strong>v391</strong></p>
            <p><span class="status-indicator orange"></span>Templates HTML mis à jour avec cache-busting renforcé</p>
        </div>
        
        <div class="step">
            <h3>🔄 Étape 1 : Redémarrage Serveur</h3>
            <p>Redémarrez votre serveur de développement pour appliquer les changements :</p>
            <div class="url-display">
                # Arrêtez le serveur (Ctrl+C)<br>
                npm run dev  # ou yarn dev
            </div>
        </div>
        
        <div class="step">
            <h3>🧹 Étape 2 : Nettoyage Cache Radical</h3>
            <p>Effectuez ces actions dans l'ordre exact :</p>
            <ol>
                <li><strong>Fermez COMPLÈTEMENT</strong> votre navigateur (toutes les fenêtres)</li>
                <li><strong>Attendez 10 secondes</strong></li>
                <li><strong>Rouvrez</strong> le navigateur</li>
                <li><strong>Naviguez</strong> vers l'application</li>
                <li><strong>Rechargement forcé :</strong> <code>Cmd + Shift + R</code> (Mac) ou <code>Ctrl + Shift + R</code> (PC)</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>🎯 Étape 3 : Test Spécifique</h3>
            <p>Testez spécifiquement la route problématique :</p>
            <div style="text-align: center; margin: 20px 0;">
                <a href="http://localhost:1212/settings/provider/chatbox-ai" class="button" target="_blank">
                    🔗 Ouvrir Route Settings
                </a>
                <button class="button" onclick="openPrivateWindow()">
                    🕵️ Test Mode Privé
                </button>
            </div>
        </div>
        
        <div class="step">
            <h3>⚡ Étape 4 : Nettoyage Développeur</h3>
            <p>Si le problème persiste, utilisez les outils développeur :</p>
            <ol>
                <li>Appuyez sur <code>F12</code> pour ouvrir DevTools</li>
                <li>Onglet <strong>Network</strong></li>
                <li>Cochez <strong>"Disable cache"</strong></li>
                <li>Clic droit sur le bouton de rechargement</li>
                <li>Sélectionnez <strong>"Empty Cache and Hard Reload"</strong></li>
            </ol>
            <div style="text-align: center; margin: 15px 0;">
                <button class="button danger" onclick="clearAllCache()">
                    🗑️ Vider Tout le Cache
                </button>
            </div>
        </div>
        
        <div class="solution-section">
            <h3>🎯 Vérification du Succès</h3>
            <p>Après avoir suivi ces étapes, vous devriez voir :</p>
            <ul>
                <li><span class="status-indicator green"></span>Favicon DataTec uniforme sur toutes les routes</li>
                <li><span class="status-indicator green"></span>Logo avec 3 barres et points colorés</li>
                <li><span class="status-indicator green"></span>Adaptation automatique thème clair/sombre</li>
            </ul>
        </div>
        
        <div class="countdown" id="countdown">
            ⏱️ Redémarrez votre serveur maintenant !
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="button" onclick="runDiagnostic()">
                🔍 Lancer Diagnostic Complet
            </button>
            <button class="button" onclick="forceRefresh()">
                🔄 Force Refresh
            </button>
        </div>
    </div>
    
    <script>
        // Mise à jour du timestamp
        document.getElementById('new-timestamp').textContent = Date.now();
        
        // Countdown pour rappeler le redémarrage
        let countdownSeconds = 30;
        const countdownEl = document.getElementById('countdown');
        
        function updateCountdown() {
            if (countdownSeconds > 0) {
                countdownEl.textContent = `⏱️ Redémarrez votre serveur maintenant ! (${countdownSeconds}s)`;
                countdownSeconds--;
                setTimeout(updateCountdown, 1000);
            } else {
                countdownEl.textContent = '🚀 Serveur redémarré ? Testez maintenant !';
                countdownEl.style.color = '#4CAF50';
            }
        }
        
        updateCountdown();
        
        function openPrivateWindow() {
            alert('Ouvrez une fenêtre de navigation privée (Ctrl+Shift+N) et naviguez vers:\nhttp://localhost:1212/settings/provider/chatbox-ai');
        }
        
        function clearAllCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            
            // Vider localStorage et sessionStorage
            localStorage.clear();
            sessionStorage.clear();
            
            alert('✅ Cache vidé !\n\n🔄 Effectuez maintenant un rechargement forcé :\n• Mac: Cmd + Shift + R\n• PC: Ctrl + Shift + R');
        }
        
        function forceRefresh() {
            // Forcer le rechargement avec cache-busting
            const timestamp = Date.now();
            window.location.href = window.location.href.split('?')[0] + '?t=' + timestamp;
        }
        
        function runDiagnostic() {
            window.open('../scripts/favicon-diagnostic.html', '_blank');
        }
        
        // Auto-vérification du serveur
        function checkServer() {
            fetch('http://localhost:1212')
                .then(response => {
                    if (response.ok) {
                        countdownEl.textContent = '✅ Serveur détecté ! Testez maintenant !';
                        countdownEl.style.color = '#4CAF50';
                    }
                })
                .catch(() => {
                    // Serveur pas encore redémarré
                });
        }
        
        // Vérifier le serveur toutes les 5 secondes
        setInterval(checkServer, 5000);
    </script>
</body>
</html>
