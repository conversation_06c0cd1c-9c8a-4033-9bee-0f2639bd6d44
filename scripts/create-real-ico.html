<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Créateur de Favicon ICO DataTec</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .canvas-container {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .canvas-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
        }
        
        canvas {
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background: white;
            margin: 10px 0;
        }
        
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .button.danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .actions {
            text-align: center;
            margin: 30px 0;
        }
        
        #downloadLink {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Créateur de Favicon ICO DataTec</h1>
        
        <div class="success">
            <h3>✅ Logo DataTec Généré</h3>
            <p>Le logo DataTec avec les 3 barres et points colorés a été créé dans les tailles standard.</p>
        </div>
        
        <div class="canvas-container">
            <div class="canvas-item">
                <h3>16x16</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
                <div>Favicon petit</div>
            </div>
            
            <div class="canvas-item">
                <h3>32x32</h3>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <div>Favicon standard</div>
            </div>
            
            <div class="canvas-item">
                <h3>48x48</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <div>Favicon large</div>
            </div>
        </div>
        
        <div class="actions">
            <button class="button" onclick="generateFavicons()">
                🎨 Générer les Favicons
            </button>
            <button class="button" onclick="downloadICO()">
                💾 Télécharger ICO
            </button>
            <button class="button danger" onclick="testInBrowser()">
                🧪 Tester dans le Navigateur
            </button>
        </div>
        
        <div class="instructions">
            <h3>📋 Instructions d'Utilisation</h3>
            <ol>
                <li><strong>Cliquez sur "🎨 Générer les Favicons"</strong> pour créer les logos</li>
                <li><strong>Cliquez sur "💾 Télécharger ICO"</strong> pour télécharger le fichier</li>
                <li><strong>Remplacez</strong> les fichiers dans votre projet :
                    <ul>
                        <li><code>public/favicon.ico</code></li>
                        <li><code>src/renderer/favicon.ico</code></li>
                    </ul>
                </li>
                <li><strong>Redémarrez</strong> votre serveur de développement</li>
                <li><strong>Testez</strong> la route settings en mode navigation privée</li>
            </ol>
        </div>
        
        <a id="downloadLink" download="favicon.ico"></a>
    </div>
    
    <script>
        function drawDataTecLogo(ctx, size) {
            // Effacer le canvas
            ctx.clearRect(0, 0, size, size);
            
            // Fond clair
            ctx.fillStyle = '#f5f5f5';
            ctx.fillRect(0, 0, size, size);
            
            // Calculer les proportions selon la taille
            const scale = size / 32;
            const centerX = size / 2;
            const centerY = size / 2;
            
            // Dimensions des barres (proportionnelles)
            const barWidth = 14 * scale;
            const barHeight = 2 * scale;
            const barRadius = 1 * scale;
            const circleRadius = 1.25 * scale;
            
            // Position des barres
            const barX = centerX - barWidth / 2;
            const bar1Y = centerY - 3.75 * scale;
            const bar2Y = centerY - 0.75 * scale;
            const bar3Y = centerY + 2.25 * scale;
            
            // Position des cercles
            const circleX = centerX - 4.5 * scale;
            const circle1Y = bar1Y + barHeight / 2;
            const circle2Y = bar2Y + barHeight / 2;
            const circle3Y = bar3Y + barHeight / 2;
            
            // Dessiner les barres noires
            ctx.fillStyle = '#2c2c2c';
            
            // Barre 1
            ctx.beginPath();
            ctx.roundRect(barX, bar1Y, barWidth, barHeight, barRadius);
            ctx.fill();
            
            // Barre 2
            ctx.beginPath();
            ctx.roundRect(barX, bar2Y, barWidth, barHeight, barRadius);
            ctx.fill();
            
            // Barre 3
            ctx.beginPath();
            ctx.roundRect(barX, bar3Y, barWidth, barHeight, barRadius);
            ctx.fill();
            
            // Dessiner les cercles colorés
            
            // Cercle vert
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(circleX, circle1Y, circleRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Cercle bleu
            ctx.fillStyle = '#2196F3';
            ctx.beginPath();
            ctx.arc(circleX, circle2Y, circleRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Cercle rouge
            ctx.fillStyle = '#F44336';
            ctx.beginPath();
            ctx.arc(circleX, circle3Y, circleRadius, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function generateFavicons() {
            const sizes = [16, 32, 48];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                drawDataTecLogo(ctx, size);
            });
            
            console.log('✅ Favicons générés !');
        }
        
        function downloadICO() {
            // Pour cette démo, nous téléchargeons le canvas 32x32 comme PNG
            // En production, vous utiliseriez une bibliothèque pour créer un vrai ICO
            const canvas = document.getElementById('canvas32');
            
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.getElementById('downloadLink');
                link.href = url;
                link.download = 'favicon-datatec.png';
                link.click();
                
                // Nettoyer l'URL après téléchargement
                setTimeout(() => URL.revokeObjectURL(url), 1000);
                
                alert('📁 Fichier téléchargé !\\n\\n⚠️ Note: Ce fichier est au format PNG.\\nPour un vrai ICO, utilisez un convertisseur en ligne comme:\\n• favicon.io\\n• convertio.co\\n• ou ImageMagick');
            }, 'image/png');
        }
        
        function testInBrowser() {
            // Créer un favicon temporaire pour test
            const canvas = document.getElementById('canvas32');
            const dataURL = canvas.toDataURL('image/png');
            
            // Créer un lien favicon temporaire
            const link = document.createElement('link');
            link.rel = 'icon';
            link.type = 'image/png';
            link.href = dataURL;
            
            // Supprimer l'ancien favicon
            const oldFavicon = document.querySelector('link[rel="icon"]');
            if (oldFavicon) {
                oldFavicon.remove();
            }
            
            // Ajouter le nouveau
            document.head.appendChild(link);
            
            alert('🧪 Favicon temporaire appliqué !\\n\\nVérifiez l\\'onglet de cette page pour voir le nouveau favicon DataTec.');
        }
        
        // Générer automatiquement au chargement
        window.onload = function() {
            generateFavicons();
        };
        
        // Polyfill pour roundRect si nécessaire
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
