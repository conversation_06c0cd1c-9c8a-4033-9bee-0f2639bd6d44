<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Toutes Routes - Favicon DataTec</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .routes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .route-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid #4CAF50;
            transition: all 0.3s ease;
        }
        
        .route-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .route-card h3 {
            margin-top: 0;
            color: #4CAF50;
            font-size: 1.2em;
        }
        
        .route-url {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9em;
            margin: 5px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .test-button.private {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }
        
        .test-button.private:hover {
            box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
        }
        
        .favicon-preview {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }
        
        .favicon-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
        }
        
        .favicon-item img {
            width: 48px;
            height: 48px;
            margin-bottom: 10px;
            border-radius: 8px;
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .success-section {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .global-actions {
            text-align: center;
            margin: 30px 0;
        }
        
        .global-button {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .global-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.4);
        }
        
        .global-button.danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .global-button.danger:hover {
            box-shadow: 0 8px 20px rgba(244, 67, 54, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Toutes Routes - Favicon DataTec</h1>
        
        <div class="favicon-preview">
            <div class="favicon-item">
                <img src="../public/favicon.svg" alt="Favicon Clair">
                <div><strong>Mode Clair</strong></div>
                <div>Barres Noires</div>
            </div>
            <div class="favicon-item">
                <img src="../public/favicon-dark.svg" alt="Favicon Sombre">
                <div><strong>Mode Sombre</strong></div>
                <div>Barres Blanches</div>
            </div>
        </div>
        
        <div class="success-section">
            <h3>✅ Favicons Mis à Jour</h3>
            <p><strong>Timestamp:</strong> 1752872162910 | <strong>Version:</strong> v895</p>
            <p>Les favicons ont été recréés selon vos images :</p>
            <ul>
                <li>🌞 <strong>Mode Clair:</strong> Barres noires sur fond clair (Image 02)</li>
                <li>🌙 <strong>Mode Sombre:</strong> Barres blanches sur fond sombre (Image 01)</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>🚀 Instructions de Test</h3>
            <ol>
                <li><strong>Redémarrez</strong> votre serveur de développement</li>
                <li><strong>Fermez complètement</strong> votre navigateur</li>
                <li><strong>Rouvrez</strong> le navigateur</li>
                <li><strong>Testez chaque route</strong> ci-dessous</li>
                <li><strong>Vérifiez</strong> le favicon dans l'onglet</li>
            </ol>
        </div>
        
        <div class="global-actions">
            <button class="global-button danger" onclick="clearAllCache()">
                🗑️ Vider Tout le Cache
            </button>
            <button class="global-button" onclick="forceRefreshAll()">
                🔄 Force Refresh Global
            </button>
            <a href="http://localhost:1212" class="global-button" target="_blank">
                🏠 Accueil Principal
            </a>
        </div>
        
        <h2>📍 Toutes les Routes de l'Application</h2>
        
        <div class="routes-grid">
            <!-- Routes Principales -->
            <div class="route-card">
                <h3>🏠 Page d'Accueil</h3>
                <div class="route-url">http://localhost:1212/</div>
                <a href="http://localhost:1212/" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>🔐 Page de Connexion</h3>
                <div class="route-url">http://localhost:1212/login</div>
                <a href="http://localhost:1212/login" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/login')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>💬 Session de Chat</h3>
                <div class="route-url">http://localhost:1212/session/[ID]</div>
                <button class="test-button" onclick="testSessionRoute()">🔗 Tester Session</button>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/session/')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>ℹ️ À Propos</h3>
                <div class="route-url">http://localhost:1212/about</div>
                <a href="http://localhost:1212/about" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/about')">🕵️ Mode Privé</button>
            </div>
            
            <!-- Routes Settings -->
            <div class="route-card">
                <h3>⚙️ Paramètres Index</h3>
                <div class="route-url">http://localhost:1212/settings/</div>
                <a href="http://localhost:1212/settings/" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>🤖 Fournisseurs de Modèles</h3>
                <div class="route-url">http://localhost:1212/settings/provider</div>
                <a href="http://localhost:1212/settings/provider" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/provider')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card" style="border-left-color: #f44336;">
                <h3>🚨 Chatbox AI (Problématique)</h3>
                <div class="route-url">http://localhost:1212/settings/provider/chatbox-ai</div>
                <a href="http://localhost:1212/settings/provider/chatbox-ai" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/provider/chatbox-ai')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>📦 Modèles par Défaut</h3>
                <div class="route-url">http://localhost:1212/settings/default-models</div>
                <a href="http://localhost:1212/settings/default-models" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/default-models')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>🌐 Recherche Web</h3>
                <div class="route-url">http://localhost:1212/settings/web-search</div>
                <a href="http://localhost:1212/settings/web-search" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/web-search')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>🔧 MCP Settings</h3>
                <div class="route-url">http://localhost:1212/settings/mcp</div>
                <a href="http://localhost:1212/settings/mcp" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/mcp')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>📚 Base de Connaissances</h3>
                <div class="route-url">http://localhost:1212/settings/knowledge-base</div>
                <a href="http://localhost:1212/settings/knowledge-base" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/knowledge-base')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>💬 Paramètres Chat</h3>
                <div class="route-url">http://localhost:1212/settings/chat</div>
                <a href="http://localhost:1212/settings/chat" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/chat')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>⌨️ Raccourcis Clavier</h3>
                <div class="route-url">http://localhost:1212/settings/hotkeys</div>
                <a href="http://localhost:1212/settings/hotkeys" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/hotkeys')">🕵️ Mode Privé</button>
            </div>
            
            <div class="route-card">
                <h3>🔧 Paramètres Généraux</h3>
                <div class="route-url">http://localhost:1212/settings/general</div>
                <a href="http://localhost:1212/settings/general" class="test-button" target="_blank">🔗 Tester</a>
                <button class="test-button private" onclick="openPrivate('http://localhost:1212/settings/general')">🕵️ Mode Privé</button>
            </div>
        </div>
        
        <div class="success-section">
            <h3>🎯 Résultat Attendu</h3>
            <p>Sur <strong>TOUTES</strong> les routes ci-dessus, vous devriez voir :</p>
            <ul>
                <li>✅ Favicon DataTec uniforme dans l'onglet</li>
                <li>🌞 Mode clair : Barres noires sur fond clair</li>
                <li>🌙 Mode sombre : Barres blanches sur fond sombre</li>
                <li>🎨 Points colorés : Vert, Bleu, Rouge</li>
            </ul>
        </div>
    </div>
    
    <script>
        function openPrivate(url) {
            alert(`Ouvrez une fenêtre de navigation privée et naviguez vers :\n${url}`);
        }
        
        function testSessionRoute() {
            // Essayer d'ouvrir une session existante ou créer une nouvelle
            window.open('http://localhost:1212/', '_blank');
            alert('Créez une nouvelle session depuis la page d\'accueil, puis vérifiez le favicon dans l\'onglet de la session.');
        }
        
        function clearAllCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            
            localStorage.clear();
            sessionStorage.clear();
            
            alert('✅ Cache vidé !\n\n🔄 Effectuez maintenant :\n1. Rechargement forcé (Ctrl+Shift+R)\n2. Ou fermez/rouvrez le navigateur');
        }
        
        function forceRefreshAll() {
            // Forcer le rechargement avec cache-busting
            const timestamp = Date.now();
            window.location.href = window.location.href.split('?')[0] + '?t=' + timestamp;
        }
        
        // Auto-vérification du serveur
        function checkServer() {
            fetch('http://localhost:1212')
                .then(response => {
                    if (response.ok) {
                        document.querySelector('.global-actions').innerHTML += 
                            '<div style="color: #4CAF50; margin-top: 15px;">✅ Serveur détecté et fonctionnel !</div>';
                    }
                })
                .catch(() => {
                    document.querySelector('.global-actions').innerHTML += 
                        '<div style="color: #f44336; margin-top: 15px;">❌ Serveur non détecté. Redémarrez votre serveur de développement.</div>';
                });
        }
        
        // Vérifier le serveur au chargement
        setTimeout(checkServer, 2000);
    </script>
</body>
</html>
