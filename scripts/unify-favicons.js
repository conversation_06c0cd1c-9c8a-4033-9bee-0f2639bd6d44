#!/usr/bin/env node

/**
 * 🎨 Script d'Unification des Favicons DataTec
 * Ce script synchronise tous les favicons dans le projet pour garantir l'uniformité
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 Unification des Favicons DataTec');
console.log('===================================');

// Configuration des fichiers favicon
const FAVICON_CONFIG = {
  // Fichiers sources (référence)
  sources: {
    light: 'public/favicon.svg',
    dark: 'public/favicon-dark.svg',
    ico: 'assets/icon.ico'
  },
  
  // Destinations à synchroniser
  destinations: [
    {
      from: 'light',
      to: 'src/renderer/favicon.svg',
      description: 'Favicon clair pour renderer'
    },
    {
      from: 'dark', 
      to: 'src/renderer/favicon-dark.svg',
      description: 'Favicon sombre pour renderer'
    },
    {
      from: 'ico',
      to: 'public/favicon.ico',
      description: 'Favicon ICO pour public'
    },
    {
      from: 'ico',
      to: 'src/renderer/favicon.ico', 
      description: 'Favicon ICO pour renderer'
    }
  ],
  
  // Templates HTML à mettre à jour
  templates: [
    'src/renderer/index.ejs',
    'src/renderer/index.web.ejs'
  ]
};

/**
 * Vérifier l'existence des fichiers sources
 */
function checkSourceFiles() {
  console.log('🔍 Vérification des fichiers sources...');
  
  let allExist = true;
  
  Object.entries(FAVICON_CONFIG.sources).forEach(([key, filePath]) => {
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${key}: ${filePath}`);
    } else {
      console.log(`❌ ${key}: ${filePath} - MANQUANT`);
      allExist = false;
    }
  });
  
  return allExist;
}

/**
 * Synchroniser les fichiers favicon
 */
function syncFaviconFiles() {
  console.log('\n📁 Synchronisation des fichiers favicon...');
  
  FAVICON_CONFIG.destinations.forEach(dest => {
    const sourcePath = FAVICON_CONFIG.sources[dest.from];
    const destPath = dest.to;
    
    try {
      // Créer le dossier de destination si nécessaire
      const destDir = path.dirname(destPath);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      // Copier le fichier
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ ${dest.description}: ${sourcePath} → ${destPath}`);
      
    } catch (error) {
      console.log(`❌ Erreur lors de la copie ${sourcePath} → ${destPath}:`, error.message);
    }
  });
}

/**
 * Mettre à jour les timestamps dans les templates HTML
 */
function updateTemplateTimestamps() {
  console.log('\n🔄 Mise à jour des timestamps dans les templates...');
  
  const newTimestamp = Date.now();
  const newVersion = Math.floor(Math.random() * 1000) + 900; // Version > 900
  
  console.log(`📅 Nouveau timestamp: ${newTimestamp}`);
  console.log(`🔢 Nouvelle version: v${newVersion}`);
  
  FAVICON_CONFIG.templates.forEach(templatePath => {
    if (!fs.existsSync(templatePath)) {
      console.log(`⚠️  Template non trouvé: ${templatePath}`);
      return;
    }
    
    try {
      let content = fs.readFileSync(templatePath, 'utf8');
      
      // Remplacer tous les paramètres de cache-busting ET forcer les chemins absolus
      content = content.replace(
        /href="\.?\/?(favicon[^"]*)\?[^"]*"/g,
        `href="/$1?bust=${newTimestamp}&v=${newVersion}&absolute=1"`
      );
      
      // Mettre à jour le commentaire de timestamp
      const timestampComment = `<!-- Favicon refresh: ${new Date().toISOString()} -->`;
      
      if (content.includes('<!-- Favicon refresh:')) {
        content = content.replace(
          /<!-- Favicon refresh: [^>]* -->/,
          timestampComment
        );
      } else {
        content = content.replace(
          '<!-- Force favicon refresh with unique timestamp -->',
          `${timestampComment}\n    <!-- Force favicon refresh with unique timestamp -->`
        );
      }
      
      fs.writeFileSync(templatePath, content, 'utf8');
      console.log(`✅ Template mis à jour: ${templatePath}`);
      
    } catch (error) {
      console.log(`❌ Erreur lors de la mise à jour de ${templatePath}:`, error.message);
    }
  });
}

/**
 * Vérifier la cohérence des fichiers favicon
 */
function verifyFaviconConsistency() {
  console.log('\n🔍 Vérification de la cohérence...');
  
  // Vérifier que tous les fichiers existent
  const allFiles = [
    ...Object.values(FAVICON_CONFIG.sources),
    ...FAVICON_CONFIG.destinations.map(d => d.to)
  ];
  
  let allConsistent = true;
  
  allFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${filePath}`);
    } else {
      console.log(`❌ ${filePath} - MANQUANT`);
      allConsistent = false;
    }
  });
  
  return allConsistent;
}

/**
 * Fonction principale
 */
function main() {
  try {
    // 1. Vérifier les fichiers sources
    if (!checkSourceFiles()) {
      console.log('\n❌ Certains fichiers sources sont manquants. Arrêt du script.');
      process.exit(1);
    }
    
    // 2. Synchroniser les fichiers
    syncFaviconFiles();
    
    // 3. Mettre à jour les templates
    updateTemplateTimestamps();
    
    // 4. Vérifier la cohérence finale
    const isConsistent = verifyFaviconConsistency();
    
    console.log('\n🎯 Résumé:');
    if (isConsistent) {
      console.log('✅ Tous les favicons sont maintenant unifiés et cohérents !');
      console.log('');
      console.log('🚀 Actions recommandées:');
      console.log('1. Redémarrez votre serveur de développement');
      console.log('2. Effectuez un rechargement forcé (Ctrl+Shift+R)');
      console.log('3. Testez toutes les routes avec scripts/test-all-routes-favicon.html');
      console.log('4. Vérifiez en mode navigation privée');
    } else {
      console.log('❌ Certains problèmes persistent. Vérifiez les erreurs ci-dessus.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 Erreur fatale:', error.message);
    process.exit(1);
  }
}

// Exécuter le script
if (require.main === module) {
  main();
}

module.exports = {
  checkSourceFiles,
  syncFaviconFiles,
  updateTemplateTimestamps,
  verifyFaviconConsistency,
  FAVICON_CONFIG
};
