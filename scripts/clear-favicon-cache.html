<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 Nettoyage Cache Favicon - DataTec</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        
        .step h3 {
            margin-top: 0;
            color: #4CAF50;
            font-size: 1.3em;
        }
        
        .keyboard-shortcut {
            background: rgba(0, 0, 0, 0.3);
            padding: 8px 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            display: inline-block;
            margin: 0 5px;
        }
        
        .browser-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .browser-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .browser-icon {
            font-size: 3em;
            margin-bottom: 10px;
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
            text-decoration: none;
            text-align: center;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .current-favicon {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }
        
        .favicon-display {
            width: 64px;
            height: 64px;
            margin: 0 auto 15px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Nettoyage Cache Favicon</h1>
        
        <div class="current-favicon">
            <h3>📋 Favicon Actuel dans l'Onglet</h3>
            <div class="favicon-display">
                <img src="../public/favicon.svg" width="32" height="32" alt="Favicon actuel" id="current-favicon">
            </div>
            <p>Si vous voyez encore l'ancien favicon, suivez les étapes ci-dessous</p>
        </div>
        
        <div class="warning">
            <h3>⚠️ Problème Identifié</h3>
            <p>Les navigateurs cachent agressivement les favicons. Même après avoir mis à jour les fichiers, l'ancien favicon peut persister.</p>
        </div>
        
        <div class="step">
            <h3>🔄 Étape 1 : Rechargement Forcé</h3>
            <p>Effectuez un rechargement forcé pour ignorer le cache :</p>
            <div class="browser-list">
                <div class="browser-card">
                    <div class="browser-icon">🌐</div>
                    <h4>Chrome/Edge</h4>
                    <div class="keyboard-shortcut">Ctrl + F5</div>
                    <div class="keyboard-shortcut">Ctrl + Shift + R</div>
                </div>
                <div class="browser-card">
                    <div class="browser-icon">🦊</div>
                    <h4>Firefox</h4>
                    <div class="keyboard-shortcut">Ctrl + F5</div>
                    <div class="keyboard-shortcut">Ctrl + Shift + R</div>
                </div>
                <div class="browser-card">
                    <div class="browser-icon">🍎</div>
                    <h4>Safari (Mac)</h4>
                    <div class="keyboard-shortcut">Cmd + Shift + R</div>
                    <div class="keyboard-shortcut">Cmd + Option + R</div>
                </div>
            </div>
        </div>
        
        <div class="step">
            <h3>🗑️ Étape 2 : Vider le Cache Complet</h3>
            <p><strong>Chrome/Edge :</strong></p>
            <ol>
                <li>Appuyez sur <span class="keyboard-shortcut">F12</span> pour ouvrir les outils développeur</li>
                <li>Clic droit sur le bouton de rechargement</li>
                <li>Sélectionnez "Vider le cache et effectuer un rechargement forcé"</li>
            </ol>
            
            <p><strong>Firefox :</strong></p>
            <ol>
                <li><span class="keyboard-shortcut">Ctrl + Shift + Delete</span></li>
                <li>Sélectionnez "Cache" et "Cookies"</li>
                <li>Cliquez sur "Effacer maintenant"</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>🔧 Étape 3 : Navigation Privée</h3>
            <p>Testez dans une fenêtre de navigation privée :</p>
            <ul>
                <li><span class="keyboard-shortcut">Ctrl + Shift + N</span> (Chrome/Edge)</li>
                <li><span class="keyboard-shortcut">Ctrl + Shift + P</span> (Firefox)</li>
                <li><span class="keyboard-shortcut">Cmd + Shift + N</span> (Safari)</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>⏰ Étape 4 : Attendre la Propagation</h3>
            <p>Parfois, il faut attendre quelques minutes pour que le nouveau favicon apparaisse. Les navigateurs peuvent mettre à jour le favicon de manière asynchrone.</p>
        </div>
        
        <div class="success">
            <h3>✅ Vérification</h3>
            <p>Le nouveau favicon devrait maintenant apparaître avec :</p>
            <ul>
                <li>🌟 Logo DataTec avec les 3 barres et points colorés</li>
                <li>🌞 Fond clair en mode jour</li>
                <li>🌙 Fond sombre en mode nuit</li>
            </ul>
        </div>
        
        <a href="http://localhost:1212" class="button" target="_blank">
            🚀 Ouvrir DataTec Workspace
        </a>
        
        <div style="text-align: center; margin-top: 40px; opacity: 0.8;">
            <p>💡 <strong>Astuce :</strong> Fermez complètement le navigateur et rouvrez-le si le problème persiste</p>
        </div>
    </div>
    
    <script>
        // Vérifier si le favicon est chargé correctement
        function checkFavicon() {
            const favicon = document.getElementById('current-favicon');
            favicon.onerror = function() {
                this.style.display = 'none';
                this.parentNode.innerHTML += '<p style="color: #ff6b6b;">❌ Favicon non trouvé</p>';
            };
        }
        
        // Forcer le rechargement du favicon
        function refreshFavicon() {
            const links = document.querySelectorAll('link[rel*="icon"]');
            links.forEach(link => {
                const href = link.href;
                link.href = href + '?v=' + Date.now();
            });
        }
        
        checkFavicon();
        
        // Auto-refresh du favicon toutes les 5 secondes
        setInterval(refreshFavicon, 5000);
    </script>
</body>
</html>
