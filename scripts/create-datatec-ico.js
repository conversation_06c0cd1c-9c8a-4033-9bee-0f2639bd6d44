#!/usr/bin/env node

/**
 * 🎨 Créateur de Favicon ICO DataTec
 * Génère un fichier ICO avec le logo DataTec à partir du SVG
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 Création du Favicon ICO DataTec');
console.log('==================================');

// Pour l'instant, nous allons copier le SVG comme fallback
// En production, vous pourriez utiliser sharp, puppeteer ou imagemagick

const faviconSvgPath = path.join(__dirname, '../public/favicon.svg');
const faviconSvg = fs.readFileSync(faviconSvgPath, 'utf8');

console.log('📁 Lecture du fichier SVG source:', faviconSvgPath);

// Créer un SVG optimisé pour ICO (plus petit, plus simple)
const optimizedSvg = `<svg width="32" height="32" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Favicon ICO DataTec - Version optimisée -->
  <rect width="100" height="100" rx="22" fill="#f5f5f5"/>
  
  <!-- Logo DataTec - 3 barres avec points colorés -->
  <g transform="translate(50, 50)">
    <!-- Barre 1 avec point vert -->
    <rect x="-28" y="-15" width="56" height="8" rx="4" fill="#2c2c2c"/>
    <circle cx="-18" cy="-11" r="5" fill="#4CAF50"/>
    
    <!-- Barre 2 avec point bleu -->
    <rect x="-28" y="-3" width="56" height="8" rx="4" fill="#2c2c2c"/>
    <circle cx="-18" cy="1" r="5" fill="#2196F3"/>
    
    <!-- Barre 3 avec point rouge -->
    <rect x="-28" y="9" width="56" height="8" rx="4" fill="#2c2c2c"/>
    <circle cx="-18" cy="13" r="5" fill="#F44336"/>
  </g>
</svg>`;

// Chemins de destination
const destinations = [
  'public/favicon.ico',
  'src/renderer/favicon.ico'
];

console.log('🔄 Création des fichiers ICO (format SVG temporaire)...');

destinations.forEach(destPath => {
  try {
    // Créer le dossier de destination si nécessaire
    const destDir = path.dirname(destPath);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    
    // Écrire le fichier SVG optimisé comme ICO temporaire
    fs.writeFileSync(destPath, optimizedSvg, 'utf8');
    console.log(`✅ Créé: ${destPath}`);
    
  } catch (error) {
    console.log(`❌ Erreur lors de la création de ${destPath}:`, error.message);
  }
});

console.log('');
console.log('🎯 Fichiers ICO DataTec créés !');
console.log('');
console.log('⚠️  IMPORTANT:');
console.log('Les fichiers créés sont au format SVG (compatibles avec les navigateurs modernes).');
console.log('Pour un vrai fichier ICO binaire, utilisez un outil comme:');
console.log('  - ImageMagick: convert favicon.svg favicon.ico');
console.log('  - Sharp (Node.js): sharp(svgBuffer).ico().toFile("favicon.ico")');
console.log('  - Ou un service en ligne comme favicon.io');
console.log('');
console.log('🚀 Actions suivantes:');
console.log('1. Redémarrez votre serveur de développement');
console.log('2. Effectuez un rechargement forcé (Ctrl+Shift+R)');
console.log('3. Videz le cache du navigateur si nécessaire');
console.log('4. Testez en mode navigation privée');
