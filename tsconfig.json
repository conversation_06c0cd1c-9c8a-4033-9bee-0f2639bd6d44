{"compilerOptions": {"incremental": true, "target": "es2021", "module": "commonjs", "lib": ["dom", "es2021"], "jsx": "react-jsx", "strict": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/renderer/*"]}, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "allowJs": true, "outDir": ".erb/dll", "skipLibCheck": true}, "exclude": ["node_modules", "dist", "tmp", "test", "release", ".erb/dll", "ios", "android"]}