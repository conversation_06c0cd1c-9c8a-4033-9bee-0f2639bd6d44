# 🔍 Debug Bases de Connaissances - Chat Integration

## 🎯 **Problème à Investiguer**

L'étiquette "Loi Monétaire" est sélectionnée (affichée en bleu) mais l'IA ne prend pas en considération les instructions et documents configurés dans cette base de connaissances.

## 🔧 **Logs de Debugging Ajoutés**

J'ai ajouté des logs de debugging à plusieurs endroits pour tracer le flux des données :

### **1. KnowledgeBasePills.tsx - Sélection d'Étiquette :**
```typescript
const handlePillClick = (kb: KnowledgeBaseData) => {
  console.log('🔍 DEBUG - Clic sur étiquette:', kb.name)
  if (selectedKB?.id === kb.id) {
    console.log('🔍 DEBUG - Désélection de:', kb.name)
    setSelectedKB(null)
    onKnowledgeBaseSelect?.(null)
  } else {
    console.log('🔍 DEBUG - Sélection de:', kb.name, 'avec', kb.files?.length || 0, 'fichiers')
    setSelectedKB(kb)
    onKnowledgeBaseSelect?.(kb)
  }
}
```

### **2. InputBox.tsx - Transmission du Payload :**
```typescript
try {
  console.log('🔍 DEBUG - InputBox selectedKnowledgeBase avant envoi:', selectedKnowledgeBase)
  const res = await onSubmit?.({
    input: messageInput,
    pictureKeys,
    attachments,
    links,
    webBrowsing: webBrowsingMode,
    needGenerating,
    selectedKnowledgeBase,
  })
```

### **3. session/$sessionId.tsx - Réception et Traitement :**
```typescript
onSubmit={async ({
  needGenerating = true,
  input = '',
  pictureKeys = [],
  attachments = [],
  links = [],
  webBrowsing = false,
  selectedKnowledgeBase = null,
}) => {
  console.log('🔍 DEBUG - selectedKnowledgeBase reçu:', selectedKnowledgeBase)
  
  // ...
  
  if (selectedKnowledgeBase) {
    console.log('🔍 DEBUG - Traitement de la base de connaissances:', selectedKnowledgeBase.name)
    console.log('🔍 DEBUG - Fichiers dans la base:', selectedKnowledgeBase.files?.length || 0)
    // ...
    console.log('🔍 DEBUG - Message final envoyé à l\'IA:', newMessage.content.substring(0, 500) + '...')
  }
```

### **4. sessionActions.ts - submitNewUserMessage :**
```typescript
export async function submitNewUserMessage(params: {
  currentSessionId: string
  newUserMsg: Message
  needGenerating: boolean
  attachments: File[]
  links: { url: string }[]
  webBrowsing?: boolean
  selectedKnowledgeBase?: any  // ← AJOUTÉ
}) {
  const { currentSessionId, newUserMsg, needGenerating, attachments, links, selectedKnowledgeBase } = params
  console.log('🔍 DEBUG - submitNewUserMessage reçu selectedKnowledgeBase:', selectedKnowledgeBase)
```

## 🧪 **Instructions de Test**

### **Étapes pour Tester :**
1. **Ouvrez** l'application dans le navigateur
2. **Ouvrez** les DevTools (F12) et allez dans l'onglet Console
3. **Sélectionnez** l'étiquette "Loi Monétaire" (doit devenir bleue)
4. **Tapez** un message comme "dite moi une petite blague"
5. **Envoyez** le message
6. **Observez** les logs dans la console

### **Logs Attendus :**
```
🔍 DEBUG - Clic sur étiquette: Loi Monétaire
🔍 DEBUG - Sélection de: Loi Monétaire avec X fichiers
🔍 DEBUG - InputBox selectedKnowledgeBase avant envoi: {name: "Loi Monétaire", ...}
🔍 DEBUG - selectedKnowledgeBase reçu: {name: "Loi Monétaire", ...}
🔍 DEBUG - Traitement de la base de connaissances: Loi Monétaire
🔍 DEBUG - Fichiers dans la base: X
🔍 DEBUG - Message final envoyé à l'IA: [Base de connaissances: Loi Monétaire]...
🔍 DEBUG - submitNewUserMessage reçu selectedKnowledgeBase: {name: "Loi Monétaire", ...}
```

## 🔍 **Points de Vérification**

### **1. Sélection d'Étiquette :**
- ✅ **Clic détecté** : Log "Clic sur étiquette"
- ✅ **Sélection active** : Log "Sélection de"
- ✅ **Nombre de fichiers** : Affichage du nombre de fichiers
- ✅ **Callback appelé** : onKnowledgeBaseSelect invoqué

### **2. Transmission InputBox :**
- ✅ **selectedKnowledgeBase défini** : Objet non null
- ✅ **Propriétés présentes** : name, files, description, etc.
- ✅ **onSubmit appelé** : Fonction de soumission invoquée

### **3. Réception Session :**
- ✅ **Paramètre reçu** : selectedKnowledgeBase dans les paramètres
- ✅ **Condition if** : Bloc de traitement exécuté
- ✅ **Fichiers traités** : Contenu des fichiers ajouté
- ✅ **Message enrichi** : Contenu final avec base de connaissances

### **4. Transmission sessionActions :**
- ✅ **Paramètre accepté** : selectedKnowledgeBase dans la signature
- ✅ **Valeur reçue** : Log de réception dans submitNewUserMessage

## 🚨 **Problèmes Potentiels à Identifier**

### **1. Sélection Non Persistante :**
- ❌ **État perdu** : selectedKnowledgeBase devient null
- ❌ **Callback non appelé** : onKnowledgeBaseSelect pas invoqué
- ❌ **Re-render** : État réinitialisé lors du rendu

### **2. Transmission Interrompue :**
- ❌ **Payload incomplet** : selectedKnowledgeBase manquant
- ❌ **Sérialisation** : Objet mal sérialisé
- ❌ **Type mismatch** : Interface non compatible

### **3. Traitement Échoué :**
- ❌ **Condition non remplie** : if (selectedKnowledgeBase) false
- ❌ **Fichiers vides** : files array vide ou undefined
- ❌ **Contenu manquant** : file.content undefined

### **4. Intégration IA :**
- ❌ **Message non enrichi** : Contenu original non modifié
- ❌ **Prompt incomplet** : Instructions/fichiers non inclus
- ❌ **Format incorrect** : Structure du message invalide

## 🎯 **Actions Selon les Résultats**

### **Si Sélection Échoue :**
```typescript
// Vérifier KnowledgeBasePills state management
// Vérifier onKnowledgeBaseSelect callback
// Vérifier useState dans InputBox
```

### **Si Transmission Échoue :**
```typescript
// Vérifier InputBoxPayload type
// Vérifier onSubmit parameters
// Vérifier destructuring dans session
```

### **Si Traitement Échoue :**
```typescript
// Vérifier selectedKnowledgeBase structure
// Vérifier files array et content
// Vérifier message construction
```

### **Si Intégration IA Échoue :**
```typescript
// Vérifier newMessage.content final
// Vérifier submitNewUserMessage call
// Vérifier model prompt processing
```

## 🔧 **Corrections Déjà Appliquées**

### **1. Signature submitNewUserMessage :**
```typescript
// AVANT
export async function submitNewUserMessage(params: {
  // ... sans selectedKnowledgeBase
})

// APRÈS
export async function submitNewUserMessage(params: {
  // ... 
  selectedKnowledgeBase?: any  // ← AJOUTÉ
})
```

### **2. Destructuring Parameters :**
```typescript
// AVANT
const { currentSessionId, newUserMsg, needGenerating, attachments, links } = params

// APRÈS
const { currentSessionId, newUserMsg, needGenerating, attachments, links, selectedKnowledgeBase } = params
```

## 🚀 **Prochaines Étapes**

1. **Tester** avec les logs de debugging
2. **Identifier** où le flux se casse
3. **Corriger** le problème spécifique identifié
4. **Valider** que l'IA utilise bien les documents
5. **Nettoyer** les logs de debugging

---

**Testez maintenant et partagez les logs de la console pour identifier le problème exact !** 🔍✅
