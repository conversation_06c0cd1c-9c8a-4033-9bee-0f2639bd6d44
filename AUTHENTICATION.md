# Système d'Authentification DataTec Workspace

## Vue d'ensemble

L'application DataTec Workspace dispose maintenant d'un système d'authentification moderne avec support des thèmes sombre/clair et une interface utilisateur élégante.

## Fonctionnalités

### 🔐 Écran de Connexion
- **Design moderne** avec dégradés et effets de transparence
- **Support des thèmes** sombre/clair automatique
- **Logo DataTec** adaptatif selon le thème
- **Validation en temps réel** des champs
- **Indicateurs de chargement** et messages d'erreur
- **Interface responsive** pour desktop et mobile

### 👤 Gestion des Utilisateurs
- **Profils utilisateur** avec rôles (admin, user, guest)
- **Menu utilisateur** dans la sidebar avec avatar
- **Informations utilisateur** : nom, email, rôle
- **Déconnexion sécurisée**

### 🛡️ Protection des Routes
- **Redirection automatique** vers la connexion si non authentifié
- **Persistance de session** dans localStorage
- **Vérification d'authentification** à chaque navigation

## Utilisation

### Comptes de Test

L'application inclut des comptes de démonstration :

#### Administrateur
- **Username** : `admin`
- **Password** : `admin123`
- **Rôle** : Administrateur avec tous les privilèges

#### Utilisateur Standard
- **Username** : `user`
- **Password** : `user123`
- **Rôle** : Utilisateur standard

### Séquence de Démarrage

L'application suit une séquence de démarrage optimisée en 3 étapes :

#### **1. 🔐 Écran de Connexion**
- Interface de connexion moderne et élégante
- Validation des identifiants en temps réel
- Support des thèmes sombre/clair adaptatifs
- Comptes de test intégrés

#### **2. 🎬 Écran Splash (Post-Connexion)**
- Animation du logo DataTec avec transitions fluides
- Barre de progression élégante avec timing précis
- Durée optimisée (3 secondes)
- Thèmes adaptatifs selon les préférences

#### **3. 🏠 Application Principale**
- Interface complète de l'application DataTec
- Menu utilisateur avec avatar et informations
- Sidebar avec navigation complète
- Toutes les fonctionnalités disponibles

> **Note** : L'ancienne animation splash au démarrage a été supprimée pour une expérience utilisateur plus fluide. Seule l'animation post-connexion est conservée.

### Flux d'Authentification

1. **Démarrage** : L'application vérifie l'état d'authentification
2. **Redirection** : Si non connecté → écran de connexion
3. **Connexion** : Saisie des identifiants et validation
4. **Splash** : Animation de démarrage (logo + progression)
5. **Session** : Stockage sécurisé des informations utilisateur
6. **Navigation** : Accès à l'application principale
7. **Déconnexion** : Nettoyage de la session et retour à la connexion

## Architecture Technique

### Composants Principaux

#### `LoginScreen.tsx`
- Interface de connexion moderne
- Gestion des états de chargement et d'erreur
- Support des thèmes sombre/clair
- Validation des formulaires

#### `UserMenu.tsx`
- Menu déroulant avec informations utilisateur
- Avatar personnalisé
- Actions : profil, paramètres, déconnexion
- Indicateur de rôle avec couleurs

#### `SplashScreen.tsx`
- Animation de démarrage après connexion
- Logo animé avec transitions fluides
- Barre de progression avec timing précis
- Support des thèmes sombre/clair

#### `ProtectedRoute.tsx`
- Composant de protection des routes
- Redirection automatique si non authentifié
- Vérification en temps réel

### Gestion d'État (Jotai)

#### `authAtoms.ts`
```typescript
// État d'authentification persistant
export const authAtom = atomWithStorage<AuthState>('auth', {
  isAuthenticated: false,
  user: null,
  token: null,
})

// Atoms dérivés
export const isAuthenticatedAtom = atom((get) => get(authAtom).isAuthenticated)
export const currentUserAtom = atom((get) => get(authAtom).user)
export const splashCompletedAtom = atom(false) // Gestion du splash
export const logoutAtom = atom(null, (get, set) => { /* logout logic */ })
```

### Types TypeScript

```typescript
interface User {
  id: string
  username: string
  displayName: string
  email: string
  role: 'admin' | 'user' | 'guest'
  avatar?: string
}

interface AuthState {
  isAuthenticated: boolean
  user: User | null
  token: string | null
}
```

## Personnalisation

### Modification des Comptes

Pour modifier les comptes de test, éditez le fichier `src/renderer/routes/login.tsx` :

```typescript
// Exemple de validation personnalisée
if (username === 'votre_username' && password === 'votre_password') {
  setAuth({
    isAuthenticated: true,
    user: {
      id: 'custom_id',
      username: username,
      displayName: 'Nom Affiché',
      email: `${username}@datatec.com`,
      role: 'admin', // ou 'user'
    },
    token: 'your-jwt-token',
  })
  return true
}
```

### Intégration avec un Backend

Pour connecter à un vrai système d'authentification :

1. **Remplacer la logique de validation** dans `login.tsx`
2. **Ajouter les appels API** pour l'authentification
3. **Gérer les tokens JWT** réels
4. **Implémenter le refresh token** si nécessaire

### Styles et Thèmes

Les styles s'adaptent automatiquement aux thèmes de l'application :
- **Variables CSS** pour les couleurs
- **Détection automatique** du thème système
- **Transitions fluides** entre les thèmes

## Sécurité

### Bonnes Pratiques Implémentées
- ✅ **Validation côté client** des formulaires
- ✅ **Gestion sécurisée** des mots de passe (masqués)
- ✅ **Persistance sécurisée** dans localStorage
- ✅ **Nettoyage de session** à la déconnexion
- ✅ **Protection des routes** sensibles

### Recommandations pour la Production
- 🔒 **HTTPS obligatoire** pour toutes les communications
- 🔑 **JWT avec expiration** courte et refresh token
- 🛡️ **Validation côté serveur** stricte
- 📝 **Logs d'authentification** pour audit
- 🔐 **Chiffrement des données** sensibles

## Support

Le système d'authentification est entièrement intégré à l'application DataTec Workspace et respecte le design system existant avec Material-UI et Mantine.
