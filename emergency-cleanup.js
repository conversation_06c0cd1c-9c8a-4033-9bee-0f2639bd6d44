// Script de nettoyage d'urgence pour supprimer toutes les sessions d'exemple
// À exécuter dans la console du navigateur sur http://localhost:4343

console.log('🧹 Démarrage du nettoyage d\'urgence des sessions d\'exemple...');

// Mots-clés pour identifier les sessions d'exemple
const EXAMPLE_KEYWORDS = [
  'example', 'Example', 'EXAMPLE', '(Example)', '(example)',
  'demo', 'Demo', 'DEMO', 'sample', 'Sample', 'SAMPLE',
  'test', 'Test', 'TEST',
  'Software Developer', 'Translator', 'Social Media Influencer',
  'Travel Guide', 'Image Creator', 'Just chat', 'Markdown 101',
  'ChartWhiz', 'Snake Game', 'Artifact',
  '示例', '演示', '样例', '测试',
  '小红书文案生成器', '翻译助手', '贪吃蛇', '做图表'
];

// IDs connus des sessions d'exemple
const EXAMPLE_IDS = [
  'justchat-b612-406a-985b-3ab4d2c482ff',
  '6dafa15e-c72f-4036-ac89-33c09e875bdc',
  'e22ab364-4681-4e24-aaba-461ed0fccfd3',
  'chatbox-chat-demo-image-creator',
  'chatbox-chat-demo-artifact-1-en',
  'mermaid-demo-1-en',
  '81cfc426-48b4-4a13-ad42-bfcfc4544299',
  '3e091ac6-ebfa-42c9-b125-c67ac2d45ee1',
  'chatbox-chat-demo-artifact-1-cn',
  'mermaid-demo-1-cn'
];

function isExampleSession(session) {
  // Vérifier par ID
  if (EXAMPLE_IDS.includes(session.id)) {
    return true;
  }
  
  // Vérifier par nom
  if (session.name) {
    return EXAMPLE_KEYWORDS.some(keyword => session.name.includes(keyword));
  }
  
  return false;
}

function cleanupExampleSessions() {
  let removedCount = 0;
  let cleanedLists = 0;

  // 1. Nettoyer toutes les clés du localStorage
  const keysToRemove = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (!key) continue;
    
    try {
      const value = localStorage.getItem(key);
      if (!value) continue;
      
      // Vérifier si c'est une session individuelle
      if (key.startsWith('session-') || key.includes('session')) {
        const data = JSON.parse(value);
        if (data && (data.name || data.id) && isExampleSession(data)) {
          keysToRemove.push(key);
          console.log('🗑️ Session d\'exemple trouvée:', data.name || data.id);
        }
      }
      
      // Vérifier si c'est la liste des sessions
      if (key === 'ChatSessionsList' || key.includes('SessionsList')) {
        const data = JSON.parse(value);
        if (Array.isArray(data)) {
          const originalLength = data.length;
          const filteredData = data.filter(session => !isExampleSession(session));
          
          if (filteredData.length !== originalLength) {
            localStorage.setItem(key, JSON.stringify(filteredData));
            cleanedLists++;
            console.log(`🧹 Liste nettoyée: ${originalLength} → ${filteredData.length} sessions`);
          }
        }
      }
    } catch (e) {
      // Ignorer les erreurs de parsing JSON
    }
  }
  
  // Supprimer les sessions individuelles identifiées
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
    removedCount++;
  });
  
  // 2. Nettoyer les IDs connus même s'ils ne sont pas détectés
  EXAMPLE_IDS.forEach(id => {
    const sessionKey = `session-${id}`;
    if (localStorage.getItem(sessionKey)) {
      localStorage.removeItem(sessionKey);
      removedCount++;
      console.log('🗑️ Session supprimée par ID:', id);
    }
  });
  
  console.log(`✅ Nettoyage terminé:`);
  console.log(`   - ${removedCount} sessions supprimées`);
  console.log(`   - ${cleanedLists} listes nettoyées`);
  
  // 3. Forcer le rechargement de la page pour appliquer les changements
  if (removedCount > 0 || cleanedLists > 0) {
    console.log('🔄 Rechargement de la page pour appliquer les changements...');
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  } else {
    console.log('ℹ️ Aucune session d\'exemple trouvée');
  }
}

// Exécuter le nettoyage
cleanupExampleSessions();

// Exporter la fonction pour utilisation manuelle
window.cleanupExampleSessions = cleanupExampleSessions;

console.log('💡 Vous pouvez réexécuter le nettoyage avec: cleanupExampleSessions()');
