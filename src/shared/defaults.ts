import { v4 as uuidv4 } from 'uuid'
import { Config, ModelProviderEnum, ProviderBaseInfo, SessionSettings, Settings, Theme } from './types'

export function settings(): Settings {
  return {
    // aiProvider: ModelProviderEnum.OpenAI,
    // openaiKey: '',
    // apiHost: 'https://api.openai.com',
    // dalleStyle: 'vivid',
    // imageGenerateNum: 3,
    // openaiUseProxy: false,

    // azureApikey: '',
    // azureDeploymentName: '',
    // azureDeploymentNameOptions: [],
    // azureDalleDeploymentName: 'dall-e-3',
    // azureEndpoint: '',
    // azureApiVersion: '2024-05-01-preview',

    // chatglm6bUrl: '', // deprecated
    // chatglmApiKey: '',
    // chatglmModel: '',

    // model: 'gpt-4o',
    // openaiCustomModelOptions: [],
    // temperature: 0.7,
    // topP: 1,
    // // openaiMaxTokens: 0,
    // // openaiMaxContextTokens: 4000,
    // openaiMaxContextMessageCount: 20,
    // // maxContextSize: "4000",
    // // maxTokens: "2048",

    // claudeApiKey: '',
    // claudeApiHost: 'https://api.anthropic.com/v1',
    // claudeModel: 'claude-3-5-sonnet-20241022',
    // claudeApiKey: '',
    // claudeApiHost: 'https://api.anthropic.com',
    // claudeModel: 'claude-3-5-sonnet-20241022',

    // datatecAIModel: 'datatecai-3.5',

    // geminiAPIKey: '',
    // geminiAPIHost: 'https://generativelanguage.googleapis.com',
    // geminiModel: 'gemini-1.5-pro-latest',

    // ollamaHost: 'http://127.0.0.1:11434',
    // ollamaModel: '',

    // groqAPIKey: '',
    // groqModel: 'llama3-70b-8192',

    // deepseekAPIKey: '',
    // deepseekModel: 'deepseek-chat',

    // siliconCloudKey: '',
    // siliconCloudModel: 'Qwen/Qwen2.5-7B-Instruct',

    // lmStudioHost: 'http://127.0.0.1:1234/v1',
    // lmStudioModel: '',

    // perplexityApiKey: '',
    // perplexityModel: 'llama-3.1-sonar-large-128k-online',

    // xAIKey: '',
    // xAIModel: 'grok-beta',

    // customProviders: [],

    showWordCount: false,
    showTokenCount: false,
    showTokenUsed: true,
    showModelName: true,
    showMessageTimestamp: false,
    showFirstTokenLatency: false,
    userAvatarKey: '',
    defaultAssistantAvatarKey: '',
    theme: Theme.System,
    language: 'en',
    fontSize: 14,
    spellCheck: true,

    defaultPrompt: '', // Toujours vide - pas de prompt système par défaut

    allowReportingAndTracking: true,

    enableMarkdownRendering: true,
    enableLaTeXRendering: true,
    enableMermaidRendering: true,
    injectDefaultMetadata: true,
    autoPreviewArtifacts: false,
    autoCollapseCodeBlock: true,
    pasteLongTextAsAFile: true,

    autoGenerateTitle: true,

    autoLaunch: false,
    autoUpdate: true,
    betaUpdate: false,

    shortcuts: {
      quickToggle: 'Alt+`', // 快速切换窗口显隐的快捷键
      inputBoxFocus: 'mod+i', // 聚焦输入框的快捷键
      inputBoxWebBrowsingMode: 'mod+e', // 切换输入框的 web 浏览模式的快捷键
      newChat: 'mod+n', // 新建聊天的快捷键
      newPictureChat: 'mod+shift+n', // 新建图片会话的快捷键
      sessionListNavNext: 'mod+tab', // 切换到下一个会话的快捷键
      sessionListNavPrev: 'mod+shift+tab', // 切换到上一个会话的快捷键
      sessionListNavTargetIndex: 'mod', // 会话导航的快捷键
      messageListRefreshContext: 'mod+r', // 刷新上下文的快捷键
      dialogOpenSearch: 'mod+k', // 打开搜索对话框的快捷键
      inpubBoxSendMessage: 'Enter', // 发送消息的快捷键
      inpubBoxSendMessageWithoutResponse: 'Ctrl+Enter', // 发送但不生成回复的快捷键
      optionNavUp: 'up', // 选项导航的快捷键
      optionNavDown: 'down', // 选项导航的快捷键
      optionSelect: 'enter', // 选项导航的快捷键
    },
    extension: {
      webSearch: {
        provider: 'build-in',
        tavilyApiKey: '',
      },
    },
    mcp: {
      servers: [],
      enabledBuiltinServers: [],
    },
  }
}

export function newConfigs(): Config {
  return { uuid: uuidv4() }
}

export function getDefaultPrompt() {
  return ''
}

export function chatSessionSettings(): SessionSettings {
  return {
    provider: ModelProviderEnum.DataTecAI,
    modelId: 'datatecai-4',
    maxContextMessageCount: 6,
  }
}

export function pictureSessionSettings(): SessionSettings {
  return {
    provider: ModelProviderEnum.DataTecAI,
    modelId: 'DALL-E-3',
    imageGenerateNum: 3,
    dalleStyle: 'vivid',
  }
}

export const SystemProviders: ProviderBaseInfo[] = [
  {
    id: ModelProviderEnum.OpenAI,
    name: 'OpenAI',
    type: 'openai',
    urls: {
      website: 'https://openai.com',
    },
    defaultSettings: {
      apiHost: 'https://api.openai.com',
      // https://platform.openai.com/docs/models
      models: [
        {
          modelId: 'gpt-4.1',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 1_047_576,
          maxOutput: 32_768,
        },
        {
          modelId: 'gpt-4o',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
          maxOutput: 4_096,
        },
        {
          modelId: 'gpt-4o-mini',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
          maxOutput: 4_096,
        },
        {
          modelId: 'o4-mini',
          capabilities: ['vision', 'tool_use', 'reasoning'],
          contextWindow: 200_000,
          maxOutput: 100_000,
        },
        {
          modelId: 'o3-mini',
          capabilities: ['vision', 'tool_use', 'reasoning'],
          contextWindow: 200_000,
          maxOutput: 200_000,
        },
        {
          modelId: 'o1-mini',
          capabilities: ['vision', 'tool_use', 'reasoning'],
          contextWindow: 128_000,
          maxOutput: 128_000,
        },
        {
          modelId: 'o3',
          capabilities: ['vision', 'tool_use', 'reasoning'],
          contextWindow: 200_000,
          maxOutput: 100_000,
        },
        {
          modelId: 'o1',
          capabilities: ['vision', 'tool_use', 'reasoning'],
          contextWindow: 200_000,
          maxOutput: 100_000,
        },
      ],
    },
  },
  {
    id: ModelProviderEnum.Claude,
    name: 'Claude',
    type: 'claude',
    urls: {
      website: 'https://www.anthropic.com',
    },
    defaultSettings: {
      apiHost: 'https://api.anthropic.com/v1',
      // https://docs.anthropic.com/en/docs/about-claude/models/overview
      models: [
        {
          modelId: 'claude-opus-4-0',
          contextWindow: 200_000,
          maxOutput: 32_000,
          capabilities: ['vision', 'reasoning', 'tool_use'],
        },
        {
          modelId: 'claude-sonnet-4-0',
          contextWindow: 200_000,
          maxOutput: 64_000,
          capabilities: ['vision', 'reasoning', 'tool_use'],
        },
        {
          modelId: 'claude-3-7-sonnet-latest',
          capabilities: ['vision', 'tool_use', 'reasoning'],
          contextWindow: 200_000,
        },
        {
          modelId: 'claude-3-5-sonnet-latest',
          capabilities: ['vision'],
          contextWindow: 200_000,
        },
        {
          modelId: 'claude-3-5-haiku-latest',
          capabilities: ['vision'],
          contextWindow: 200_000,
        },
        {
          modelId: 'claude-3-opus-latest',
          capabilities: ['vision'],
          contextWindow: 200_000,
        },
      ],
    },
  },
  {
    id: ModelProviderEnum.Gemini,
    name: 'Gemini',
    type: 'gemini',
    urls: {
      website: 'https://gemini.google.com/',
    },
    defaultSettings: {
      apiHost: 'https://generativelanguage.googleapis.com',
      // https://ai.google.dev/models/gemini
      models: [
        {
          modelId: 'gemini-2.5-flash-preview-05-20',
          capabilities: ['vision', 'reasoning'],
        },
        {
          modelId: 'gemini-2.5-pro-preview-06-05',
          capabilities: ['vision', 'reasoning'],
        },
        {
          modelId: 'gemini-2.0-flash-exp',
          capabilities: ['vision'],
        },
        {
          modelId: 'gemini-2.0-flash-thinking-exp',
          capabilities: ['vision', 'reasoning'],
        },
        {
          modelId: 'gemini-2.0-flash-thinking-exp-1219',
          capabilities: ['vision', 'reasoning'],
        },
        {
          modelId: 'gemini-1.5-pro-latest',
          capabilities: ['vision'],
        },
        {
          modelId: 'gemini-1.5-flash-latest',
          capabilities: ['vision'],
        },
        {
          modelId: 'gemini-1.5-pro-exp-0827',
          capabilities: ['vision'],
        },
        {
          modelId: 'gemini-1.5-flash-exp-0827',
          capabilities: ['vision'],
        },
        {
          modelId: 'gemini-1.5-flash-8b-exp-0924',
          capabilities: ['vision'],
        },
        {
          modelId: 'gemini-pro',
        },
      ],
    },
  },
  {
    id: ModelProviderEnum.Ollama,
    name: 'Ollama',
    type: 'openai',
    defaultSettings: {
      apiHost: 'http://127.0.0.1:11434',
    },
  },
  {
    id: ModelProviderEnum.DeepSeek,
    name: 'DeepSeek',
    type: 'openai',
    defaultSettings: {
      models: [
        {
          modelId: 'deepseek-chat',
          contextWindow: 64_000,
          capabilities: ['tool_use'],
        },
        {
          modelId: 'deepseek-coder',
          contextWindow: 64_000,
        },
        {
          modelId: 'deepseek-reasoner',
          contextWindow: 64_000,
          capabilities: ['reasoning', 'tool_use'],
        },
      ],
    },
  },
  {
    id: ModelProviderEnum.Groq,
    name: 'Groq',
    type: 'openai',
    defaultSettings: {
      apiHost: 'https://api.groq.com/openai',
      models: [
        {
          modelId: 'llama-3.2-1b-preview',
        },
        {
          modelId: 'llama-3.2-3b-preview',
        },
        {
          modelId: 'llama-3.2-11b-text-preview',
        },
        {
          modelId: 'llama-3.2-90b-text-preview',
        },
      ],
    },
  },
  {
    id: ModelProviderEnum.Mistral,
    name: 'Mistral AI',
    type: 'openai',
    urls: {
      website: 'https://mistral.ai',
      apiKey: 'https://console.mistral.ai/',
      docs: 'https://docs.mistral.ai/',
    },
    defaultSettings: {
      apiHost: 'https://api.mistral.ai/v1',
      // https://docs.mistral.ai/getting-started/models/models_overview/
      models: [
        // Premier models
        {
          modelId: 'mistral-large-2411',
          nickname: 'Mistral Large 2.1',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'pixtral-large-2411',
          nickname: 'Pixtral Large',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'mistral-small-2407',
          nickname: 'Mistral Small 2',
          capabilities: ['tool_use'],
          contextWindow: 32_000,
        },
        {
          modelId: 'codestral-2501',
          nickname: 'Codestral 2',
          capabilities: ['tool_use'],
          contextWindow: 256_000,
        },
        {
          modelId: 'ministral-8b-2410',
          nickname: 'Ministral 8B',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'ministral-3b-2410',
          nickname: 'Ministral 3B',
          contextWindow: 128_000,
        },
        // Open models
        {
          modelId: 'mistral-small-2506',
          nickname: 'Mistral Small 3.2',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'mistral-small-2503',
          nickname: 'Mistral Small 3.1',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'mistral-small-2501',
          nickname: 'Mistral Small 3',
          capabilities: ['tool_use'],
          contextWindow: 32_000,
        },
        {
          modelId: 'pixtral-12b-2409',
          nickname: 'Pixtral 12B',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'open-mistral-nemo',
          nickname: 'Mistral Nemo 12B',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'open-codestral-mamba',
          nickname: 'Codestral Mamba',
          contextWindow: 256_000,
        },
        // Latest aliases
        {
          modelId: 'mistral-large-latest',
          nickname: 'Mistral Large (Latest)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'mistral-small-latest',
          nickname: 'Mistral Small (Latest)',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'codestral-latest',
          nickname: 'Codestral (Latest)',
          capabilities: ['tool_use'],
          contextWindow: 256_000,
        },
      ],
    },
  },
  {
    id: ModelProviderEnum.OpenRouter,
    name: 'OpenRouter',
    type: 'openai',
    urls: {
      website: 'https://openrouter.ai',
      apiKey: 'https://openrouter.ai/settings/credits',
      docs: 'https://openrouter.ai/docs',
      models: 'https://openrouter.ai/models',
    },
    defaultSettings: {
      apiHost: 'https://openrouter.ai/api/v1',
      models: [
        // Modèles GRATUITS OpenRouter (priorité)
        {
          modelId: 'deepseek/deepseek-chat-v3-0324:free',
          nickname: 'DeepSeek Chat V3 (Gratuit)',
          capabilities: ['tool_use'],
          contextWindow: 64_000,
        },
        {
          modelId: 'meta-llama/llama-3.2-3b-instruct:free',
          nickname: 'Llama 3.2 3B (Gratuit)',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'meta-llama/llama-3.2-1b-instruct:free',
          nickname: 'Llama 3.2 1B (Gratuit)',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'microsoft/phi-3-mini-128k-instruct:free',
          nickname: 'Phi-3 Mini (Gratuit)',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'google/gemma-2-9b-it:free',
          nickname: 'Gemma 2 9B (Gratuit)',
          capabilities: ['tool_use'],
          contextWindow: 8_192,
        },
        // OpenAI Models via OpenRouter (payants)
        {
          modelId: 'openai/gpt-4o',
          nickname: 'GPT-4o (OpenRouter)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'openai/gpt-4o-mini',
          nickname: 'GPT-4o Mini (OpenRouter)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'openai/o1-preview',
          nickname: 'o1 Preview (OpenRouter)',
          capabilities: ['reasoning'],
          contextWindow: 128_000,
        },
        {
          modelId: 'openai/o1-mini',
          nickname: 'o1 Mini (OpenRouter)',
          capabilities: ['reasoning'],
          contextWindow: 128_000,
        },
        // Anthropic Models via OpenRouter
        {
          modelId: 'anthropic/claude-3.5-sonnet',
          nickname: 'Claude 3.5 Sonnet (OpenRouter)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 200_000,
        },
        {
          modelId: 'anthropic/claude-3.5-haiku',
          nickname: 'Claude 3.5 Haiku (OpenRouter)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 200_000,
        },
        // Google Models via OpenRouter
        {
          modelId: 'google/gemini-2.0-flash-exp',
          nickname: 'Gemini 2.0 Flash (OpenRouter)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 1_000_000,
        },
        {
          modelId: 'google/gemini-1.5-pro',
          nickname: 'Gemini 1.5 Pro (OpenRouter)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 2_000_000,
        },
        // Meta Models via OpenRouter
        {
          modelId: 'meta-llama/llama-3.3-70b-instruct',
          nickname: 'Llama 3.3 70B (OpenRouter)',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        {
          modelId: 'meta-llama/llama-3.2-90b-vision-instruct',
          nickname: 'Llama 3.2 90B Vision (OpenRouter)',
          capabilities: ['vision', 'tool_use'],
          contextWindow: 128_000,
        },
        // Mistral Models via OpenRouter
        {
          modelId: 'mistralai/mistral-large',
          nickname: 'Mistral Large (OpenRouter)',
          capabilities: ['tool_use'],
          contextWindow: 128_000,
        },
        // DeepSeek Models via OpenRouter
        {
          modelId: 'deepseek/deepseek-chat',
          nickname: 'DeepSeek Chat (OpenRouter)',
          capabilities: ['tool_use'],
          contextWindow: 64_000,
        },
        {
          modelId: 'deepseek/deepseek-r1',
          nickname: 'DeepSeek R1 (OpenRouter)',
          capabilities: ['reasoning', 'tool_use'],
          contextWindow: 64_000,
        },
      ],
    },
  },
]

// Providers cleaned up successfully
