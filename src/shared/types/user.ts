// Types pour la gestion des utilisateurs - Module User Management
// Système RBAC (Role-Based Access Control) pour DataTec

// ===== ÉNUMÉRATIONS =====

export enum UserRole {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  USER = 'user',
  GUEST = 'guest'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}

export enum PermissionAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage'
}

export enum PermissionResource {
  USER = 'user',
  CHAT = 'chat',
  SETTINGS = 'settings',
  PROVIDER = 'provider',
  MODEL = 'model',
  KNOWLEDGE_BASE = 'knowledge_base',
  SYSTEM = 'system'
}

// ===== INTERFACES PRINCIPALES =====

export interface User {
  id: number
  username: string
  email: string
  displayName: string
  role: UserRole
  status: UserStatus
  isActive: boolean
  
  // Informations personnelles
  avatar?: string
  firstName?: string
  lastName?: string
  language: string
  timezone?: string
  
  // Métadonnées
  createdAt: Date
  updatedAt: Date
  lastLogin?: Date
  loginCount: number
  
  // Sécurité
  passwordHash?: string // Ne pas exposer côté client
  salt?: string // Ne pas exposer côté client
  twoFactorEnabled: boolean
  emailVerified: boolean
  
  // Préférences
  preferences: UserPreferences
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  notifications: {
    email: boolean
    push: boolean
    desktop: boolean
  }
  privacy: {
    showOnlineStatus: boolean
    allowDirectMessages: boolean
  }
}

export interface Role {
  id: string
  name: UserRole
  displayName: string
  description: string
  permissions: Permission[]
  isSystem: boolean // Rôles système non modifiables
  createdAt: Date
  updatedAt: Date
}

export interface Permission {
  id: string
  action: PermissionAction
  resource: PermissionResource
  conditions?: PermissionCondition[]
  description: string
}

export interface PermissionCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'contains'
  value: any
}

// ===== INTERFACES POUR LES OPÉRATIONS =====

export interface CreateUserRequest {
  username: string
  email: string
  displayName: string
  password: string
  role: UserRole
  language?: string
  avatar?: string
  sendWelcomeEmail?: boolean
}

export interface UpdateUserRequest {
  displayName?: string
  email?: string
  role?: UserRole
  status?: UserStatus
  isActive?: boolean
  language?: string
  avatar?: string
  preferences?: Partial<UserPreferences>
}

export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

export interface AuthResult {
  success: boolean
  user?: User
  token?: string
  refreshToken?: string
  expiresAt?: Date
  error?: string
  requiresTwoFactor?: boolean
}

export interface Session {
  id: string
  userId: number
  token: string
  refreshToken?: string
  isActive: boolean
  createdAt: Date
  expiresAt: Date
  lastActivity: Date
  ipAddress?: string
  userAgent?: string
  deviceInfo?: string
}

// ===== INTERFACES POUR L'AUDIT =====

export interface AuditLog {
  id: number
  userId: number
  action: string
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  success: boolean
  error?: string
  timestamp: Date
}

// ===== INTERFACES POUR LES STATISTIQUES =====

export interface UserStats {
  totalUsers: number
  activeUsers: number
  newUsersThisMonth: number
  usersByRole: Record<UserRole, number>
  usersByStatus: Record<UserStatus, number>
  averageSessionDuration: number
  mostActiveUsers: Array<{
    user: User
    activityScore: number
  }>
}

// ===== INTERFACES POUR LES FILTRES ET RECHERCHE =====

export interface UserFilters {
  role?: UserRole
  status?: UserStatus
  isActive?: boolean
  search?: string
  createdAfter?: Date
  createdBefore?: Date
  lastLoginAfter?: Date
  lastLoginBefore?: Date
  limit?: number
  offset?: number
  sortBy?: 'username' | 'displayName' | 'email' | 'createdAt' | 'lastLogin'
  sortOrder?: 'asc' | 'desc'
}

export interface UserListResponse {
  users: User[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// ===== INTERFACES POUR LES ERREURS =====

export interface UserManagementError {
  code: string
  message: string
  field?: string
  details?: Record<string, any>
}

export interface ValidationError extends UserManagementError {
  field: string
  value: any
  constraint: string
}

// ===== TYPES UTILITAIRES =====

export type PublicUser = Omit<User, 'passwordHash' | 'salt'>
export type UserSummary = Pick<User, 'id' | 'username' | 'displayName' | 'email' | 'role' | 'status' | 'avatar' | 'lastLogin'>
export type CreateUserData = Omit<User, 'id' | 'createdAt' | 'updatedAt' | 'loginCount' | 'passwordHash' | 'salt'>

// ===== CONSTANTES =====

export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'system',
  language: 'fr',
  notifications: {
    email: true,
    push: true,
    desktop: false
  },
  privacy: {
    showOnlineStatus: true,
    allowDirectMessages: true
  }
}

export const SYSTEM_ROLES: Role[] = [
  {
    id: 'admin',
    name: UserRole.ADMIN,
    displayName: 'Administrateur',
    description: 'Accès complet à toutes les fonctionnalités',
    permissions: [], // À définir selon les besoins
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'moderator',
    name: UserRole.MODERATOR,
    displayName: 'Modérateur',
    description: 'Gestion des utilisateurs et du contenu',
    permissions: [], // À définir selon les besoins
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'user',
    name: UserRole.USER,
    displayName: 'Utilisateur',
    description: 'Accès standard aux fonctionnalités',
    permissions: [], // À définir selon les besoins
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'guest',
    name: UserRole.GUEST,
    displayName: 'Invité',
    description: 'Accès limité en lecture seule',
    permissions: [], // À définir selon les besoins
    isSystem: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]
