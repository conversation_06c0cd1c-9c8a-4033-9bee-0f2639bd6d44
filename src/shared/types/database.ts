// Types partagés pour le système de base de données utilisateurs
// Compatible Desktop (Electron) et Web

export interface User {
  id?: number
  username: string
  email: string
  displayName: string
  role: UserRole
  avatar?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  lastLogin?: Date
  metadata?: Record<string, any>
}

export interface UserCredentials {
  id?: number
  userId: number
  passwordHash: string
  salt: string
  algorithm: 'bcrypt' | 'scrypt' | 'argon2'
  iterations?: number
  createdAt: Date
  updatedAt: Date
}

export interface Session {
  id?: number
  userId: number
  token: string
  refreshToken?: string
  expiresAt: Date
  refreshExpiresAt?: Date
  ipAddress?: string
  userAgent?: string
  deviceInfo?: string
  isActive: boolean
  createdAt: Date
  lastActivity: Date
}

export interface Role {
  id?: number
  name: string
  displayName: string
  description?: string
  permissions: Permission[]
  isSystem: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Permission {
  id?: number
  resource: string
  action: string
  conditions?: Record<string, any>
  description?: string
}

export interface UserRole {
  id?: number
  userId: number
  roleId: number
  assignedAt: Date
  assignedBy: number
  expiresAt?: Date
  isActive: boolean
}

export interface AuditLog {
  id?: number
  userId?: number
  sessionId?: number
  action: string
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  success: boolean
  errorMessage?: string
  timestamp: Date
}

// Énumérations
export type UserRoleType = 'admin' | 'user' | 'guest' | 'moderator'

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  displayName: string
  role: UserRoleType
  avatar?: string
}

export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  user: User
  token: string
  refreshToken?: string
  expiresAt: Date
}

export interface AuthResult {
  success: boolean
  user?: User
  token?: string
  refreshToken?: string
  error?: string
  errorCode?: string
}

// Configuration de la base de données
export interface DatabaseConfig {
  name: string
  version: number
  encryptionKey?: string
  backupEnabled: boolean
  auditEnabled: boolean
  sessionTimeout: number // en minutes
  maxLoginAttempts: number
  lockoutDuration: number // en minutes
}

// Statistiques utilisateurs
export interface UserStats {
  totalUsers: number
  activeUsers: number
  adminUsers: number
  guestUsers: number
  sessionsActive: number
  lastLogin?: Date
}

// Permissions prédéfinies
export const SYSTEM_PERMISSIONS = {
  // Gestion des utilisateurs
  USER_CREATE: { resource: 'user', action: 'create' },
  USER_READ: { resource: 'user', action: 'read' },
  USER_UPDATE: { resource: 'user', action: 'update' },
  USER_DELETE: { resource: 'user', action: 'delete' },
  USER_LIST: { resource: 'user', action: 'list' },
  
  // Gestion des rôles
  ROLE_CREATE: { resource: 'role', action: 'create' },
  ROLE_READ: { resource: 'role', action: 'read' },
  ROLE_UPDATE: { resource: 'role', action: 'update' },
  ROLE_DELETE: { resource: 'role', action: 'delete' },
  
  // Gestion des sessions
  SESSION_READ: { resource: 'session', action: 'read' },
  SESSION_DELETE: { resource: 'session', action: 'delete' },
  SESSION_LIST: { resource: 'session', action: 'list' },
  
  // Administration système
  SYSTEM_CONFIG: { resource: 'system', action: 'config' },
  SYSTEM_AUDIT: { resource: 'system', action: 'audit' },
  SYSTEM_BACKUP: { resource: 'system', action: 'backup' },
  
  // Gestion des données
  DATA_EXPORT: { resource: 'data', action: 'export' },
  DATA_IMPORT: { resource: 'data', action: 'import' },
  DATA_DELETE: { resource: 'data', action: 'delete' },
} as const

// Rôles prédéfinis
export const SYSTEM_ROLES = {
  ADMIN: {
    name: 'admin',
    displayName: 'Administrateur',
    description: 'Accès complet au système',
    permissions: Object.values(SYSTEM_PERMISSIONS)
  },
  USER: {
    name: 'user',
    displayName: 'Utilisateur',
    description: 'Accès standard aux fonctionnalités',
    permissions: [
      SYSTEM_PERMISSIONS.USER_READ,
      SYSTEM_PERMISSIONS.SESSION_READ,
      SYSTEM_PERMISSIONS.DATA_EXPORT
    ]
  },
  GUEST: {
    name: 'guest',
    displayName: 'Invité',
    description: 'Accès limité en lecture seule',
    permissions: [
      SYSTEM_PERMISSIONS.USER_READ
    ]
  }
} as const
