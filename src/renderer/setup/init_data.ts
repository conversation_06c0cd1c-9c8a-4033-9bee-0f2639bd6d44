import { initSessionsIfNeeded } from '../stores/sessionStorageMutations'
import { clearExampleSessions, hasExampleSessions } from '../utils/clearExampleSessions'
import { clearDefaultPrompt, cleanExistingSessionsWithOldPrompt } from '../utils/clearDefaultPrompt'
import { getLogger } from '../lib/utils'

const log = getLogger('init_data')

export async function initData() {
  log.info('Début de l\'initialisation des données')

  // Nettoyer l'ancien prompt par défaut "You are a helpful assistant"
  try {
    await clearDefaultPrompt()
    log.info('Nettoyage du prompt par défaut terminé')
  } catch (e) {
    log.error('Erreur lors du nettoyage du prompt par défaut:', e)
  }

  // Nettoyer les sessions existantes qui contiennent l'ancien message système
  try {
    await cleanExistingSessionsWithOldPrompt()
    log.info('Nettoyage des sessions existantes terminé')
  } catch (e) {
    log.error('Erreur lors du nettoyage des sessions existantes:', e)
  }

  // Vérifier s'il y a des sessions d'exemple
  const hasExamples = await hasExampleSessions()
  if (hasExamples) {
    log.info('Sessions d\'exemple détectées, nettoyage en cours...')
    await clearExampleSessions()
  } else {
    log.info('Aucune session d\'exemple détectée')
  }

  // Initialiser les sessions (maintenant vides)
  await initSessionsIfNeeded()

  log.info('Initialisation des données terminée')
}
