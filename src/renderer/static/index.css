/* @tailwind base;
@tailwind components;
@tailwind utilities; */

.title-bar {
  app-region: drag
}

.title-bar .controls {
  app-region: no-drag;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans',
    'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overscroll-behavior: none;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

html > body {
  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica,
        Arial, sans-serif; */
  /* padding: 0; */
  margin: auto;
}

html,
body,
#root {
  /* 固定高度为100%而不是100vh或者calc(var(--vh, 1vh) * 100)，可以在解决移动端浏览器地址栏高度计算的前提下避免弹出输入法时卡顿 */
  height: 100%;
}

html[data-theme='dark'] {
  .ToolBar {
    background-color: rgb(40, 40, 40);
  }

  /* // ------------ 滚动条 --------------- */

  /* 设置滚动条的宽度, 背景色与边框 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
    border-radius: 6px;
  }
  ::scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
    border-radius: 6px;
  }
  ::-webkit-scrollbar:hover {
    background-color: #333333;
  }
  ::scrollbar:hover {
    background-color: #333333;
  }
  /* 滚动条上的滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: #b1b1b1;
    border-radius: 6px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: #8c8c8c;
  }
  /* 滚动条的轨道 */
  ::-webkit-scrollbar-track {
    border-radius: 6px;
  }
  /* 滚动条上下按钮 */
  ::-webkit-scrollbar-button {
    display: none;
  }

  a {
    color: #fff;
  }
  /* 错误消息中的链接强制成蓝色 */
  .message-error-tips a {
    color: #2563eb !important;
  }
}

html[data-theme='light'] {
  .ToolBar {
    background-color: #fff;
  }

  /* // ------------ 滚动条 --------------- */

  /* 设置滚动条的宽度, 背景色与边框 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
    border-radius: 6px;
  }
  ::scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
    border-radius: 6px;
  }
  ::-webkit-scrollbar:hover {
    background-color: #f5f5f5;
  }
  ::scrollbar:hover {
    background-color: #f5f5f5;
  }
  /* 滚动条上的滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: #c6c6c6;
    border-radius: 6px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: #a0a0a0;
  }
  /* 滚动条的轨道 */
  ::-webkit-scrollbar-track {
    border-radius: 6px;
  }
  /* 滚动条上下按钮 */
  ::-webkit-scrollbar-button {
    display: none;
  }

  a {
    color: #333;
  }
  /* 错误消息中的链接强制成蓝色 */
  .message-error-tips a {
    color: #2563eb !important;
  }
}

.App {
  width: 100%;
  height: 100%;

  /* 移动端异形屏的显示问题 */
  padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-top: var(--mobile-safe-area-inset-top, 0px);
  padding-right: var(--mobile-safe-area-inset-right, 0px);
  padding-bottom: var(--mobile-safe-area-inset-bottom, 0px);
  padding-left: var(--mobile-safe-area-inset-left, 0px);
}

.ToolBar {
  /* 移动端异形屏的显示问题 */
  padding-top: env(safe-area-inset-top, 0px);
  padding-bottom: env(safe-area-inset-bottom, 0px);
  padding-left: env(safe-area-inset-left, 0px);
  padding-top: var(--mobile-safe-area-inset-top, 0px);
  padding-bottom: var(--mobile-safe-area-inset-bottom, 0px);
  padding-left: var(--mobile-safe-area-inset-left, 0px);
}

.ThreadHistoryDrawer {
  /* 移动端异形屏的显示问题 */
  padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
  /* padding-left: env(safe-area-inset-left); */
  padding-top: var(--mobile-safe-area-inset-top, 0px);
  padding-right: var(--mobile-safe-area-inset-right, 0px);
  padding-bottom: var(--mobile-safe-area-inset-bottom, 0px);
  /* padding-left: var(--mobile-safe-area-inset-left, 0px); */
}

.Snackbar {
  /* 移动端异形屏的显示问题 */
  padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-top: var(--mobile-safe-area-inset-top, 0px);
  padding-right: var(--mobile-safe-area-inset-right, 0px);
  padding-bottom: var(--mobile-safe-area-inset-bottom, 0px);
  padding-left: var(--mobile-safe-area-inset-left, 0px);
}

@font-face {
  font-family: 'Cairo';
  src: url('./fonts/Cairo-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Cairo';
  src: url('./fonts/Cairo-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}
