/**
 * Service pour gérer l'affichage du splash screen
 * Détermine quand afficher ou non le splash selon le contexte
 */

export class SplashService {
  private static readonly SPLASH_SHOWN_KEY = 'datatec_splash_shown'
  private static readonly SESSION_START_KEY = 'datatec_session_start'
  private static readonly PAGE_LOAD_KEY = 'datatec_page_load'
  private static readonly SPLASH_COOLDOWN = 10 * 1000 // 10 secondes pour une détection stricte des rafraîchissements

  /**
   * Vérifie si le splash doit être affiché
   * @param isNewLogin - Indique si c'est une nouvelle connexion (vs rafraîchissement)
   * @returns true si le splash doit être affiché
   */
  static shouldShowSplash(isNewLogin: boolean = false): boolean {
    try {
      // Vérifications multiples pour détecter un rafraîchissement
      const isPageRefresh = this.isPageRefresh()
      const hasActiveSession = this.hasActiveSession()

      // Si c'est un rafraîchissement détecté, JAMAIS de splash
      if (isPageRefresh || (!isNewLogin && hasActiveSession)) {
        return false
      }

      // Si c'est une nouvelle connexion explicite, toujours afficher le splash
      if (isNewLogin) {
        return true
      }

      // Pour tous les autres cas, ne pas afficher le splash par défaut
      return false

    } catch (error) {
      console.error('Erreur lors de la vérification du splash:', error)
      // En cas d'erreur, ne pas afficher le splash pour éviter les interruptions
      return false
    }
  }

  /**
   * Détecte si c'est un rafraîchissement de page
   */
  static isPageRefresh(): boolean {
    try {
      // Vérifier si c'est un rafraîchissement via performance API
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navigation && navigation.type === 'reload') {
          return true
        }
      }

      // Vérifier la session active
      return this.hasActiveSession()
    } catch (error) {
      console.error('Erreur lors de la détection du rafraîchissement:', error)
      return false
    }
  }

  /**
   * Marque le splash comme affiché
   */
  static markSplashShown(): void {
    try {
      const now = Date.now().toString()
      localStorage.setItem(this.SPLASH_SHOWN_KEY, now)
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du splash:', error)
    }
  }

  /**
   * Marque le début d'une nouvelle session
   */
  static markSessionStart(): void {
    try {
      const now = Date.now().toString()
      localStorage.setItem(this.SESSION_START_KEY, now)
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la session:', error)
    }
  }

  /**
   * Nettoie les données de session (lors de la déconnexion)
   */
  static clearSession(): void {
    try {
      localStorage.removeItem(this.SESSION_START_KEY)
      // Ne pas supprimer SPLASH_SHOWN_KEY pour éviter de réafficher le splash trop souvent
    } catch (error) {
      console.error('Erreur lors du nettoyage de la session:', error)
    }
  }

  /**
   * Réinitialise complètement l'état du splash (pour les tests ou cas spéciaux)
   */
  static reset(): void {
    try {
      localStorage.removeItem(this.SPLASH_SHOWN_KEY)
      localStorage.removeItem(this.SESSION_START_KEY)
    } catch (error) {
      console.error('Erreur lors de la réinitialisation du splash:', error)
    }
  }

  /**
   * Vérifie si une session est active (pour les rafraîchissements)
   */
  static hasActiveSession(): boolean {
    try {
      const sessionStart = localStorage.getItem(this.SESSION_START_KEY)
      if (!sessionStart) return false

      const sessionStartTime = parseInt(sessionStart, 10)
      const now = Date.now()
      const timeSinceSessionStart = now - sessionStartTime

      // Considérer la session comme active si elle a commencé il y a moins de 30 secondes
      return timeSinceSessionStart < this.SPLASH_COOLDOWN
    } catch (error) {
      console.error('Erreur lors de la vérification de la session:', error)
      return false
    }
  }

  /**
   * Vérifie si c'est probablement un rafraîchissement de page
   * (session active + utilisateur authentifié)
   */
  static isLikelyPageRefresh(): boolean {
    return this.hasActiveSession()
  }

  /**
   * Force l'affichage du splash pour une nouvelle connexion
   * Nettoie les données précédentes pour s'assurer que le splash s'affiche
   */
  static forceShowSplashForNewLogin(): void {
    try {
      // Nettoyer les données précédentes pour forcer le splash
      localStorage.removeItem(this.SPLASH_SHOWN_KEY)
      localStorage.removeItem(this.SESSION_START_KEY)

      // Marquer le début de la nouvelle session APRÈS un court délai
      // pour s'assurer que le splash s'affiche
      setTimeout(() => {
        this.markSessionStart()
      }, 100)

      console.log('🎬 Splash forcé pour nouvelle connexion')
    } catch (error) {
      console.error('Erreur lors de la préparation du splash pour nouvelle connexion:', error)
    }
  }
}
