// Service de gestion des rôles et permissions (RBAC)
// Compatible Desktop (Electron) et Web

import { userDatabase } from '../database/DexieUserDatabase'
import {
  User,
  Role,
  Permission,
  UserRole,
  SYSTEM_PERMISSIONS,
  SYSTEM_ROLES,
  DatabaseError,
  ValidationError,
  PermissionError
} from '../../shared/types/database'

export class RolePermissionService {
  private static instance: RolePermissionService
  private initialized = false
  private permissionCache = new Map<string, boolean>()
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  private constructor() {}

  public static getInstance(): RolePermissionService {
    if (!RolePermissionService.instance) {
      RolePermissionService.instance = new RolePermissionService()
    }
    return RolePermissionService.instance
  }

  // Initialisation du service
  public async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await userDatabase.open()
      this.initialized = true
      console.log('✅ Service de rôles et permissions initialisé')
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation du service RBAC:', error)
      throw new DatabaseError('Impossible d\'initialiser le service RBAC', 'RBAC_INIT_ERROR', error)
    }
  }

  // Vérification des permissions
  public async checkPermission(userId: number, resource: string, action: string): Promise<boolean> {
    await this.ensureInitialized()

    try {
      // Vérifier le cache
      const cacheKey = `${userId}:${resource}:${action}`
      const cached = this.permissionCache.get(cacheKey)
      if (cached !== undefined) {
        return cached
      }

      // Vérifier l'utilisateur
      const user = await userDatabase.users.get(userId)
      if (!user || !user.isActive) {
        this.cachePermission(cacheKey, false)
        return false
      }

      // Super admin a tous les droits
      if (user.role === 'admin') {
        this.cachePermission(cacheKey, true)
        return true
      }

      // Vérifier les permissions via les rôles
      const userRoles = await userDatabase.userRoles
        .where('userId')
        .equals(userId)
        .and(ur => ur.isActive && (!ur.expiresAt || ur.expiresAt > new Date()))
        .toArray()

      for (const userRole of userRoles) {
        const role = await userDatabase.roles.get(userRole.roleId)
        if (role && this.roleHasPermission(role, resource, action)) {
          this.cachePermission(cacheKey, true)
          return true
        }
      }

      // Vérifier les permissions directes (si implémentées)
      const hasDirectPermission = await this.checkDirectPermission(userId, resource, action)
      
      this.cachePermission(cacheKey, hasDirectPermission)
      return hasDirectPermission

    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error)
      return false
    }
  }

  // Vérifier si un rôle a une permission
  private roleHasPermission(role: Role, resource: string, action: string): boolean {
    return role.permissions.some(p => p.resource === resource && p.action === action)
  }

  // Vérifier les permissions directes
  private async checkDirectPermission(userId: number, resource: string, action: string): Promise<boolean> {
    // Pour l'instant, pas de permissions directes - tout passe par les rôles
    // Cette méthode peut être étendue pour des permissions spécifiques
    return false
  }

  // Cache des permissions
  private cachePermission(key: string, value: boolean): void {
    this.permissionCache.set(key, value)
    
    // Nettoyer le cache après timeout
    setTimeout(() => {
      this.permissionCache.delete(key)
    }, this.cacheTimeout)
  }

  // Obtenir toutes les permissions d'un utilisateur
  public async getUserPermissions(userId: number): Promise<Permission[]> {
    await this.ensureInitialized()

    try {
      const user = await userDatabase.users.get(userId)
      if (!user || !user.isActive) {
        return []
      }

      // Super admin a toutes les permissions
      if (user.role === 'admin') {
        return Object.values(SYSTEM_PERMISSIONS)
      }

      const permissions: Permission[] = []
      const userRoles = await userDatabase.userRoles
        .where('userId')
        .equals(userId)
        .and(ur => ur.isActive && (!ur.expiresAt || ur.expiresAt > new Date()))
        .toArray()

      for (const userRole of userRoles) {
        const role = await userDatabase.roles.get(userRole.roleId)
        if (role) {
          permissions.push(...role.permissions)
        }
      }

      // Supprimer les doublons
      const uniquePermissions = permissions.filter((permission, index, self) =>
        index === self.findIndex(p => p.resource === permission.resource && p.action === permission.action)
      )

      return uniquePermissions
    } catch (error) {
      console.error('Erreur lors de la récupération des permissions:', error)
      return []
    }
  }

  // Gestion des rôles

  // Créer un rôle
  public async createRole(roleData: {
    name: string
    displayName: string
    description?: string
    permissions: Permission[]
  }): Promise<Role> {
    await this.ensureInitialized()

    try {
      // Vérifier l'unicité du nom
      const existingRole = await userDatabase.roles.where('name').equals(roleData.name).first()
      if (existingRole) {
        throw new ValidationError('Un rôle avec ce nom existe déjà')
      }

      const roleId = await userDatabase.roles.add({
        name: roleData.name,
        displayName: roleData.displayName,
        description: roleData.description,
        permissions: roleData.permissions,
        isSystem: false,
        createdAt: new Date(),
        updatedAt: new Date()
      })

      const role = await userDatabase.roles.get(roleId)
      
      // Log d'audit
      await userDatabase.auditLogs.add({
        action: 'ROLE_CREATED',
        resource: 'role',
        resourceId: roleId.toString(),
        details: { name: roleData.name, permissionsCount: roleData.permissions.length },
        success: true,
        timestamp: new Date()
      })

      return role!
    } catch (error) {
      console.error('Erreur lors de la création du rôle:', error)
      throw error
    }
  }

  // Obtenir un rôle
  public async getRole(roleId: number): Promise<Role | null> {
    await this.ensureInitialized()
    return await userDatabase.roles.get(roleId) || null
  }

  // Obtenir un rôle par nom
  public async getRoleByName(name: string): Promise<Role | null> {
    await this.ensureInitialized()
    return await userDatabase.roles.where('name').equals(name).first() || null
  }

  // Lister tous les rôles
  public async listRoles(): Promise<Role[]> {
    await this.ensureInitialized()
    return await userDatabase.roles.orderBy('name').toArray()
  }

  // Mettre à jour un rôle
  public async updateRole(roleId: number, updates: Partial<Role>): Promise<Role> {
    await this.ensureInitialized()

    try {
      const role = await userDatabase.roles.get(roleId)
      if (!role) {
        throw new ValidationError('Rôle non trouvé')
      }

      if (role.isSystem && (updates.name || updates.permissions)) {
        throw new PermissionError('Impossible de modifier les rôles système')
      }

      await userDatabase.roles.update(roleId, {
        ...updates,
        updatedAt: new Date()
      })

      const updatedRole = await userDatabase.roles.get(roleId)
      
      // Invalider le cache des permissions
      this.clearPermissionCache()

      // Log d'audit
      await userDatabase.auditLogs.add({
        action: 'ROLE_UPDATED',
        resource: 'role',
        resourceId: roleId.toString(),
        details: updates,
        success: true,
        timestamp: new Date()
      })

      return updatedRole!
    } catch (error) {
      console.error('Erreur lors de la mise à jour du rôle:', error)
      throw error
    }
  }

  // Supprimer un rôle
  public async deleteRole(roleId: number): Promise<boolean> {
    await this.ensureInitialized()

    try {
      const role = await userDatabase.roles.get(roleId)
      if (!role) {
        return false
      }

      if (role.isSystem) {
        throw new PermissionError('Impossible de supprimer les rôles système')
      }

      // Vérifier si le rôle est utilisé
      const usersWithRole = await userDatabase.userRoles.where('roleId').equals(roleId).count()
      if (usersWithRole > 0) {
        throw new ValidationError('Impossible de supprimer un rôle assigné à des utilisateurs')
      }

      await userDatabase.roles.delete(roleId)
      
      // Invalider le cache des permissions
      this.clearPermissionCache()

      // Log d'audit
      await userDatabase.auditLogs.add({
        action: 'ROLE_DELETED',
        resource: 'role',
        resourceId: roleId.toString(),
        details: { name: role.name },
        success: true,
        timestamp: new Date()
      })

      return true
    } catch (error) {
      console.error('Erreur lors de la suppression du rôle:', error)
      throw error
    }
  }

  // Gestion des associations utilisateur-rôle

  // Assigner un rôle à un utilisateur
  public async assignRole(userId: number, roleId: number, assignedBy: number, expiresAt?: Date): Promise<UserRole> {
    await this.ensureInitialized()

    try {
      // Vérifier que l'utilisateur et le rôle existent
      const user = await userDatabase.users.get(userId)
      const role = await userDatabase.roles.get(roleId)
      
      if (!user) throw new ValidationError('Utilisateur non trouvé')
      if (!role) throw new ValidationError('Rôle non trouvé')

      // Vérifier si l'association existe déjà
      const existingAssignment = await userDatabase.userRoles
        .where('[userId+roleId]')
        .equals([userId, roleId])
        .and(ur => ur.isActive)
        .first()

      if (existingAssignment) {
        throw new ValidationError('Ce rôle est déjà assigné à cet utilisateur')
      }

      const userRoleId = await userDatabase.userRoles.add({
        userId,
        roleId,
        assignedAt: new Date(),
        assignedBy,
        expiresAt,
        isActive: true
      })

      const userRole = await userDatabase.userRoles.get(userRoleId)
      
      // Invalider le cache des permissions
      this.clearUserPermissionCache(userId)

      // Log d'audit
      await userDatabase.auditLogs.add({
        userId: assignedBy,
        action: 'ROLE_ASSIGNED',
        resource: 'userRole',
        resourceId: userRoleId.toString(),
        details: { 
          targetUserId: userId, 
          roleId, 
          roleName: role.name,
          expiresAt: expiresAt?.toISOString()
        },
        success: true,
        timestamp: new Date()
      })

      return userRole!
    } catch (error) {
      console.error('Erreur lors de l\'assignation du rôle:', error)
      throw error
    }
  }

  // Retirer un rôle d'un utilisateur
  public async removeRole(userId: number, roleId: number): Promise<boolean> {
    await this.ensureInitialized()

    try {
      const userRole = await userDatabase.userRoles
        .where('[userId+roleId]')
        .equals([userId, roleId])
        .and(ur => ur.isActive)
        .first()

      if (!userRole) {
        return false
      }

      await userDatabase.userRoles.update(userRole.id!, {
        isActive: false
      })

      // Invalider le cache des permissions
      this.clearUserPermissionCache(userId)

      // Log d'audit
      await userDatabase.auditLogs.add({
        action: 'ROLE_REMOVED',
        resource: 'userRole',
        resourceId: userRole.id!.toString(),
        details: { userId, roleId },
        success: true,
        timestamp: new Date()
      })

      return true
    } catch (error) {
      console.error('Erreur lors de la suppression du rôle:', error)
      return false
    }
  }

  // Obtenir les rôles d'un utilisateur
  public async getUserRoles(userId: number): Promise<Role[]> {
    await this.ensureInitialized()

    try {
      const userRoles = await userDatabase.userRoles
        .where('userId')
        .equals(userId)
        .and(ur => ur.isActive && (!ur.expiresAt || ur.expiresAt > new Date()))
        .toArray()

      const roles: Role[] = []
      for (const userRole of userRoles) {
        const role = await userDatabase.roles.get(userRole.roleId)
        if (role) {
          roles.push(role)
        }
      }

      return roles
    } catch (error) {
      console.error('Erreur lors de la récupération des rôles utilisateur:', error)
      return []
    }
  }

  // Obtenir les utilisateurs d'un rôle
  public async getRoleUsers(roleId: number): Promise<User[]> {
    await this.ensureInitialized()

    try {
      const userRoles = await userDatabase.userRoles
        .where('roleId')
        .equals(roleId)
        .and(ur => ur.isActive && (!ur.expiresAt || ur.expiresAt > new Date()))
        .toArray()

      const users: User[] = []
      for (const userRole of userRoles) {
        const user = await userDatabase.users.get(userRole.userId)
        if (user && user.isActive) {
          users.push(user)
        }
      }

      return users
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs du rôle:', error)
      return []
    }
  }

  // Utilitaires de cache

  // Vider tout le cache des permissions
  public clearPermissionCache(): void {
    this.permissionCache.clear()
  }

  // Vider le cache des permissions d'un utilisateur
  public clearUserPermissionCache(userId: number): void {
    const keysToDelete: string[] = []
    for (const key of this.permissionCache.keys()) {
      if (key.startsWith(`${userId}:`)) {
        keysToDelete.push(key)
      }
    }
    keysToDelete.forEach(key => this.permissionCache.delete(key))
  }

  // Méthodes utilitaires

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize()
    }
  }

  // Obtenir les statistiques des rôles
  public async getRoleStats(): Promise<{
    totalRoles: number
    systemRoles: number
    customRoles: number
    totalAssignments: number
    activeAssignments: number
  }> {
    await this.ensureInitialized()

    const [totalRoles, systemRoles, totalAssignments, activeAssignments] = await Promise.all([
      userDatabase.roles.count(),
      userDatabase.roles.where('isSystem').equals(true).count(),
      userDatabase.userRoles.count(),
      userDatabase.userRoles.where('isActive').equals(true).and(ur => !ur.expiresAt || ur.expiresAt > new Date()).count()
    ])

    return {
      totalRoles,
      systemRoles,
      customRoles: totalRoles - systemRoles,
      totalAssignments,
      activeAssignments
    }
  }
}

// Instance singleton
export const rolePermissionService = RolePermissionService.getInstance()

// Export par défaut
export default rolePermissionService
