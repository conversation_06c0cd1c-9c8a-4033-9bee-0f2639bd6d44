// Service de gestion des permissions et rôles pour DataTec
// Implémente le système RBAC (Role-Based Access Control)

import {
  UserRole,
  Permission,
  PermissionAction,
  PermissionResource,
  Role,
  SYSTEM_ROLES
} from '../../shared/types/user'
import { authService } from './AuthenticationService'

class PermissionService {
  private permissions: Map<string, Permission[]> = new Map()

  constructor() {
    this.initializePermissions()
  }

  // ===== INITIALISATION =====

  private initializePermissions() {
    // Définir les permissions pour chaque rôle
    this.setRolePermissions(UserRole.ADMIN, [
      // Administrateur : Accès complet
      { id: 'admin-all', action: PermissionAction.MANAGE, resource: PermissionResource.SYSTEM, description: 'Gestion complète du système' },
      { id: 'admin-users', action: PermissionAction.MANAGE, resource: PermissionResource.USER, description: 'Gestion des utilisateurs' },
      { id: 'admin-settings', action: PermissionAction.MANAGE, resource: PermissionResource.SETTINGS, description: 'Gestion des paramètres' },
      { id: 'admin-providers', action: PermissionAction.MANAGE, resource: PermissionResource.PROVIDER, description: 'Gestion des fournisseurs' },
      { id: 'admin-models', action: PermissionAction.MANAGE, resource: PermissionResource.MODEL, description: 'Gestion des modèles' },
      { id: 'admin-kb', action: PermissionAction.MANAGE, resource: PermissionResource.KNOWLEDGE_BASE, description: 'Gestion de la base de connaissances' },
      { id: 'admin-chat', action: PermissionAction.MANAGE, resource: PermissionResource.CHAT, description: 'Gestion des conversations' }
    ])

    this.setRolePermissions(UserRole.MODERATOR, [
      // Modérateur : Gestion des utilisateurs et du contenu
      { id: 'mod-users-read', action: PermissionAction.READ, resource: PermissionResource.USER, description: 'Lecture des utilisateurs' },
      { id: 'mod-users-update', action: PermissionAction.UPDATE, resource: PermissionResource.USER, description: 'Modification des utilisateurs' },
      { id: 'mod-chat-manage', action: PermissionAction.MANAGE, resource: PermissionResource.CHAT, description: 'Gestion des conversations' },
      { id: 'mod-kb-manage', action: PermissionAction.MANAGE, resource: PermissionResource.KNOWLEDGE_BASE, description: 'Gestion de la base de connaissances' },
      { id: 'mod-settings-read', action: PermissionAction.READ, resource: PermissionResource.SETTINGS, description: 'Lecture des paramètres' },
      { id: 'mod-providers-read', action: PermissionAction.READ, resource: PermissionResource.PROVIDER, description: 'Lecture des fournisseurs' },
      { id: 'mod-models-read', action: PermissionAction.READ, resource: PermissionResource.MODEL, description: 'Lecture des modèles' }
    ])

    this.setRolePermissions(UserRole.USER, [
      // Utilisateur : Accès standard
      { id: 'user-chat-manage', action: PermissionAction.MANAGE, resource: PermissionResource.CHAT, description: 'Gestion de ses conversations' },
      { id: 'user-settings-read', action: PermissionAction.READ, resource: PermissionResource.SETTINGS, description: 'Lecture des paramètres' },
      { id: 'user-providers-read', action: PermissionAction.READ, resource: PermissionResource.PROVIDER, description: 'Lecture des fournisseurs' },
      { id: 'user-models-read', action: PermissionAction.READ, resource: PermissionResource.MODEL, description: 'Lecture des modèles' },
      { id: 'user-kb-read', action: PermissionAction.READ, resource: PermissionResource.KNOWLEDGE_BASE, description: 'Lecture de la base de connaissances' }
    ])

    this.setRolePermissions(UserRole.GUEST, [
      // Invité : Accès limité en lecture seule
      { id: 'guest-chat-read', action: PermissionAction.READ, resource: PermissionResource.CHAT, description: 'Lecture des conversations publiques' },
      { id: 'guest-kb-read', action: PermissionAction.READ, resource: PermissionResource.KNOWLEDGE_BASE, description: 'Lecture de la base de connaissances publique' }
    ])
  }

  // ===== GESTION DES PERMISSIONS =====

  /**
   * Définir les permissions pour un rôle
   */
  private setRolePermissions(role: UserRole, permissions: Permission[]): void {
    this.permissions.set(role, permissions)
  }

  /**
   * Obtenir les permissions d'un rôle
   */
  getRolePermissions(role: UserRole): Permission[] {
    return this.permissions.get(role) || []
  }

  /**
   * Vérifier si un rôle a une permission spécifique
   */
  hasPermission(role: UserRole, action: PermissionAction, resource: PermissionResource): boolean {
    const permissions = this.getRolePermissions(role)
    
    return permissions.some(permission => {
      // Vérifier la permission exacte
      if (permission.action === action && permission.resource === resource) {
        return true
      }
      
      // Vérifier la permission MANAGE (inclut toutes les actions)
      if (permission.action === PermissionAction.MANAGE && permission.resource === resource) {
        return true
      }
      
      // Vérifier la permission système (admin uniquement)
      if (permission.resource === PermissionResource.SYSTEM && permission.action === PermissionAction.MANAGE) {
        return true
      }
      
      return false
    })
  }

  /**
   * Vérifier si l'utilisateur actuel a une permission
   */
  async currentUserHasPermission(action: PermissionAction, resource: PermissionResource): Promise<boolean> {
    const user = await authService.getCurrentUser()
    if (!user) return false
    
    return this.hasPermission(user.role, action, resource)
  }

  /**
   * Vérifier si l'utilisateur actuel peut accéder à une page
   */
  async canAccessPage(page: string): Promise<boolean> {
    const user = await authService.getCurrentUser()
    if (!user) return false

    // Définir les restrictions d'accès par page
    const pagePermissions: Record<string, { action: PermissionAction; resource: PermissionResource }> = {
      '/settings/admin': { action: PermissionAction.READ, resource: PermissionResource.USER },
      '/settings/provider': { action: PermissionAction.READ, resource: PermissionResource.PROVIDER },
      '/settings/default-models': { action: PermissionAction.READ, resource: PermissionResource.MODEL },
      '/settings/general': { action: PermissionAction.READ, resource: PermissionResource.SETTINGS },
      '/settings/knowledge-base': { action: PermissionAction.READ, resource: PermissionResource.KNOWLEDGE_BASE }
    }

    const pagePermission = pagePermissions[page]
    if (!pagePermission) {
      // Si la page n'est pas dans la liste, autoriser l'accès par défaut
      return true
    }

    return this.hasPermission(user.role, pagePermission.action, pagePermission.resource)
  }

  /**
   * Obtenir les pages accessibles pour l'utilisateur actuel
   */
  async getAccessiblePages(): Promise<string[]> {
    const user = await authService.getCurrentUser()
    if (!user) return []

    const allPages = [
      '/settings/users',
      '/settings/provider', 
      '/settings/default-models',
      '/settings/general',
      '/settings/knowledge-base',
      '/settings/chat',
      '/settings/hotkeys'
    ]

    const accessiblePages: string[] = []
    
    for (const page of allPages) {
      if (await this.canAccessPage(page)) {
        accessiblePages.push(page)
      }
    }

    return accessiblePages
  }

  // ===== UTILITAIRES DE RÔLE =====

  /**
   * Vérifier si un rôle est supérieur à un autre
   */
  isRoleHigherThan(role1: UserRole, role2: UserRole): boolean {
    const hierarchy = {
      [UserRole.ADMIN]: 4,
      [UserRole.MODERATOR]: 3,
      [UserRole.USER]: 2,
      [UserRole.GUEST]: 1
    }

    return hierarchy[role1] > hierarchy[role2]
  }

  /**
   * Obtenir tous les rôles disponibles
   */
  getAllRoles(): Role[] {
    return SYSTEM_ROLES
  }

  /**
   * Obtenir les rôles que l'utilisateur actuel peut assigner
   */
  async getAssignableRoles(): Promise<UserRole[]> {
    const user = await authService.getCurrentUser()
    if (!user) return []

    switch (user.role) {
      case UserRole.ADMIN:
        // L'admin peut assigner tous les rôles
        return [UserRole.ADMIN, UserRole.MODERATOR, UserRole.USER, UserRole.GUEST]
      
      case UserRole.MODERATOR:
        // Le modérateur peut assigner des rôles inférieurs
        return [UserRole.USER, UserRole.GUEST]
      
      default:
        // Les autres ne peuvent pas assigner de rôles
        return []
    }
  }

  // ===== VÉRIFICATIONS SPÉCIFIQUES =====

  /**
   * Vérifier si l'utilisateur peut gérer les utilisateurs
   */
  async canManageUsers(): Promise<boolean> {
    return await this.currentUserHasPermission(PermissionAction.MANAGE, PermissionResource.USER) ||
           await this.currentUserHasPermission(PermissionAction.UPDATE, PermissionResource.USER)
  }

  /**
   * Vérifier si l'utilisateur peut voir les paramètres système
   */
  async canViewSystemSettings(): Promise<boolean> {
    return await this.currentUserHasPermission(PermissionAction.READ, PermissionResource.SETTINGS)
  }

  /**
   * Vérifier si l'utilisateur peut modifier les paramètres système
   */
  async canModifySystemSettings(): Promise<boolean> {
    return await this.currentUserHasPermission(PermissionAction.MANAGE, PermissionResource.SETTINGS)
  }

  /**
   * Vérifier si l'utilisateur peut gérer les fournisseurs
   */
  async canManageProviders(): Promise<boolean> {
    return await this.currentUserHasPermission(PermissionAction.MANAGE, PermissionResource.PROVIDER)
  }

  /**
   * Vérifier si l'utilisateur est administrateur
   */
  async isCurrentUserAdmin(): Promise<boolean> {
    return await authService.isAdmin()
  }

  /**
   * Vérifier si l'utilisateur est modérateur ou plus
   */
  async isCurrentUserModerator(): Promise<boolean> {
    return await authService.isModerator()
  }
}

// Instance singleton
export const permissionService = new PermissionService()
export default permissionService
