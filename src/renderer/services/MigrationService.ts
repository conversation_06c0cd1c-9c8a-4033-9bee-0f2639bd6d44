// Service de migration des données vers le nouveau système
// Migre depuis l'ancien système d'authentification vers Dexie.js

import { userManagementService } from './UserManagementService'
import { userDatabase } from '../database/DexieUserDatabase'
import platform from '../platform'
import { User, CreateUserRequest, DatabaseError } from '../../shared/types/database'

export interface MigrationResult {
  success: boolean
  migratedUsers: number
  errors: string[]
  warnings: string[]
  backupCreated: boolean
  backupPath?: string
}

export class MigrationService {
  private static instance: MigrationService

  private constructor() {}

  public static getInstance(): MigrationService {
    if (!MigrationService.instance) {
      MigrationService.instance = new MigrationService()
    }
    return MigrationService.instance
  }

  // Migration complète depuis l'ancien système
  public async migrateFromOldSystem(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      migratedUsers: 0,
      errors: [],
      warnings: [],
      backupCreated: false
    }

    try {
      console.log('🔄 Début de la migration des données...')

      // 1. Créer une sauvegarde de l'ancien système
      result.backupCreated = await this.createBackup()
      if (result.backupCreated) {
        console.log('✅ Sauvegarde de l\'ancien système créée')
      } else {
        result.warnings.push('Impossible de créer une sauvegarde de l\'ancien système')
      }

      // 2. Initialiser le nouveau système
      await userManagementService.initialize()
      console.log('✅ Nouveau système initialisé')

      // 3. Migrer les données d'authentification existantes
      const authData = await this.extractOldAuthData()
      console.log(`📊 ${authData.length} utilisateurs trouvés dans l'ancien système`)

      // 4. Migrer chaque utilisateur
      for (const oldUser of authData) {
        try {
          await this.migrateUser(oldUser)
          result.migratedUsers++
          console.log(`✅ Utilisateur migré: ${oldUser.username}`)
        } catch (error) {
          const errorMsg = `Erreur lors de la migration de ${oldUser.username}: ${error}`
          result.errors.push(errorMsg)
          console.error(`❌ ${errorMsg}`)
        }
      }

      // 5. Vérifier la migration
      const verificationResult = await this.verifyMigration(authData)
      if (!verificationResult.success) {
        result.errors.push(...verificationResult.errors)
        result.warnings.push(...verificationResult.warnings)
      }

      // 6. Marquer la migration comme terminée
      if (result.errors.length === 0) {
        await this.markMigrationComplete()
        result.success = true
        console.log('🎉 Migration terminée avec succès !')
      } else {
        console.log(`⚠️ Migration terminée avec ${result.errors.length} erreurs`)
      }

      return result

    } catch (error) {
      console.error('❌ Erreur critique lors de la migration:', error)
      result.errors.push(`Erreur critique: ${error}`)
      return result
    }
  }

  // Extraire les données d'authentification de l'ancien système
  private async extractOldAuthData(): Promise<Array<{
    username: string
    email: string
    displayName: string
    role: 'admin' | 'user' | 'guest'
    isActive: boolean
    createdAt?: Date
  }>> {
    const users: Array<any> = []

    try {
      // Récupérer les données de l'ancien système d'authentification
      const oldAuthData = await platform.getStoreValue('auth')
      
      if (oldAuthData && oldAuthData.user) {
        // Utilisateur actuellement connecté
        const currentUser = oldAuthData.user
        users.push({
          username: currentUser.username || 'user',
          email: currentUser.email || `${currentUser.username}@datatec.com`,
          displayName: currentUser.displayName || currentUser.username,
          role: currentUser.role || 'user',
          isActive: true,
          createdAt: new Date()
        })
      }

      // Ajouter les utilisateurs par défaut s'ils n'existent pas déjà
      const defaultUsers = [
        {
          username: 'admin',
          email: '<EMAIL>',
          displayName: 'Administrateur',
          role: 'admin' as const,
          isActive: true,
          createdAt: new Date()
        },
        {
          username: 'user',
          email: '<EMAIL>',
          displayName: 'Utilisateur',
          role: 'user' as const,
          isActive: true,
          createdAt: new Date()
        }
      ]

      // Éviter les doublons
      for (const defaultUser of defaultUsers) {
        if (!users.find(u => u.username === defaultUser.username)) {
          users.push(defaultUser)
        }
      }

      return users

    } catch (error) {
      console.error('Erreur lors de l\'extraction des données:', error)
      
      // Retourner au moins les utilisateurs par défaut
      return [
        {
          username: 'admin',
          email: '<EMAIL>',
          displayName: 'Administrateur',
          role: 'admin',
          isActive: true,
          createdAt: new Date()
        }
      ]
    }
  }

  // Migrer un utilisateur individuel
  private async migrateUser(oldUser: any): Promise<void> {
    try {
      // Vérifier si l'utilisateur existe déjà dans le nouveau système
      const existingUser = await userManagementService.getUserByUsername(oldUser.username)
      
      if (existingUser) {
        console.log(`⚠️ Utilisateur ${oldUser.username} existe déjà, mise à jour...`)
        
        // Mettre à jour l'utilisateur existant
        await userManagementService.updateUser(existingUser.id!, {
          email: oldUser.email,
          displayName: oldUser.displayName,
          role: oldUser.role,
          isActive: oldUser.isActive
        })
      } else {
        // Créer un nouveau utilisateur
        const userData: CreateUserRequest = {
          username: oldUser.username,
          email: oldUser.email,
          password: this.generateDefaultPassword(oldUser.username),
          displayName: oldUser.displayName,
          role: oldUser.role
        }

        const result = await userManagementService.createUser(userData)
        
        if (!result.success) {
          throw new Error(result.error || 'Erreur lors de la création')
        }
      }

    } catch (error) {
      throw new Error(`Migration utilisateur échouée: ${error}`)
    }
  }

  // Générer un mot de passe par défaut pour la migration
  private generateDefaultPassword(username: string): string {
    // Utiliser le même mot de passe que dans l'ancien système pour la compatibilité
    if (username === 'admin') return 'admin123'
    if (username === 'user') return 'user123'
    return `${username}123` // Mot de passe par défaut
  }

  // Vérifier que la migration s'est bien passée
  private async verifyMigration(originalUsers: any[]): Promise<{
    success: boolean
    errors: string[]
    warnings: string[]
  }> {
    const result = {
      success: true,
      errors: [] as string[],
      warnings: [] as string[]
    }

    try {
      // Vérifier que tous les utilisateurs ont été migrés
      for (const originalUser of originalUsers) {
        const migratedUser = await userManagementService.getUserByUsername(originalUser.username)
        
        if (!migratedUser) {
          result.errors.push(`Utilisateur ${originalUser.username} non trouvé après migration`)
          result.success = false
        } else {
          // Vérifier les données
          if (migratedUser.email !== originalUser.email) {
            result.warnings.push(`Email différent pour ${originalUser.username}`)
          }
          if (migratedUser.role !== originalUser.role) {
            result.warnings.push(`Rôle différent pour ${originalUser.username}`)
          }
        }
      }

      // Vérifier que l'admin existe et peut se connecter
      const adminUser = await userManagementService.getUserByUsername('admin')
      if (!adminUser) {
        result.errors.push('Utilisateur admin non trouvé après migration')
        result.success = false
      }

      // Vérifier les statistiques
      const stats = await userManagementService.getSystemStats()
      if (stats.users.totalUsers === 0) {
        result.errors.push('Aucun utilisateur dans le nouveau système')
        result.success = false
      }

      console.log(`📊 Vérification: ${stats.users.totalUsers} utilisateurs, ${stats.roles.totalRoles} rôles`)

    } catch (error) {
      result.errors.push(`Erreur lors de la vérification: ${error}`)
      result.success = false
    }

    return result
  }

  // Créer une sauvegarde de l'ancien système
  private async createBackup(): Promise<boolean> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const backupData = {
        timestamp,
        platform: platform.type,
        auth: await platform.getStoreValue('auth'),
        settings: await platform.getStoreValue('settings'),
        configs: await platform.getStoreValue('configs'),
        sessions: await platform.getStoreValue('chat-sessions-list'),
        version: '1.0.0'
      }

      const backupJson = JSON.stringify(backupData, null, 2)
      const backupKey = `migration-backup-${timestamp}`
      
      await platform.setStoreValue(backupKey, backupJson)
      console.log(`💾 Sauvegarde créée: ${backupKey}`)
      
      return true
    } catch (error) {
      console.error('Erreur lors de la création de la sauvegarde:', error)
      return false
    }
  }

  // Marquer la migration comme terminée
  private async markMigrationComplete(): Promise<void> {
    try {
      const migrationInfo = {
        completed: true,
        completedAt: new Date().toISOString(),
        version: '1.0.0',
        migratedFrom: 'legacy-auth-system'
      }

      await platform.setStoreValue('migration-status', migrationInfo)
      console.log('✅ Migration marquée comme terminée')
    } catch (error) {
      console.error('Erreur lors de la sauvegarde du statut de migration:', error)
    }
  }

  // Vérifier si la migration a déjà été effectuée
  public async isMigrationCompleted(): Promise<boolean> {
    try {
      const migrationStatus = await platform.getStoreValue('migration-status')
      return migrationStatus && migrationStatus.completed === true
    } catch (error) {
      return false
    }
  }

  // Rollback de la migration (restaurer l'ancien système)
  public async rollbackMigration(): Promise<boolean> {
    try {
      console.log('🔄 Rollback de la migration...')

      // Supprimer les données du nouveau système
      await userDatabase.delete()
      console.log('✅ Nouveau système supprimé')

      // Marquer la migration comme non terminée
      await platform.setStoreValue('migration-status', {
        completed: false,
        rolledBack: true,
        rolledBackAt: new Date().toISOString()
      })

      console.log('✅ Rollback terminé')
      return true

    } catch (error) {
      console.error('❌ Erreur lors du rollback:', error)
      return false
    }
  }

  // Obtenir le statut de la migration
  public async getMigrationStatus(): Promise<{
    completed: boolean
    completedAt?: string
    rolledBack?: boolean
    rolledBackAt?: string
    version?: string
  }> {
    try {
      const status = await platform.getStoreValue('migration-status')
      return status || { completed: false }
    } catch (error) {
      return { completed: false }
    }
  }

  // Test de la migration (sans modifier les données)
  public async testMigration(): Promise<{
    canMigrate: boolean
    usersFound: number
    issues: string[]
    recommendations: string[]
  }> {
    const result = {
      canMigrate: true,
      usersFound: 0,
      issues: [] as string[],
      recommendations: [] as string[]
    }

    try {
      // Vérifier les données existantes
      const oldUsers = await this.extractOldAuthData()
      result.usersFound = oldUsers.length

      if (oldUsers.length === 0) {
        result.issues.push('Aucun utilisateur trouvé dans l\'ancien système')
        result.recommendations.push('Vérifier que l\'ancien système d\'authentification contient des données')
      }

      // Vérifier si le nouveau système est déjà initialisé
      const migrationCompleted = await this.isMigrationCompleted()
      if (migrationCompleted) {
        result.issues.push('La migration a déjà été effectuée')
        result.recommendations.push('Utiliser rollbackMigration() pour revenir en arrière si nécessaire')
        result.canMigrate = false
      }

      // Vérifier l'espace de stockage (approximatif)
      if (result.usersFound > 1000) {
        result.recommendations.push('Grand nombre d\'utilisateurs détecté, la migration peut prendre du temps')
      }

    } catch (error) {
      result.issues.push(`Erreur lors du test: ${error}`)
      result.canMigrate = false
    }

    return result
  }
}

// Instance singleton
export const migrationService = MigrationService.getInstance()

// Export par défaut
export default migrationService
