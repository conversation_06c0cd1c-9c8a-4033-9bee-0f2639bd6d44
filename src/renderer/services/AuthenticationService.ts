// Service d'authentification utilisant Dexie.js
// Compatible Desktop (Electron) et Web

import bcrypt from 'bcryptjs'
import CryptoJS from 'crypto-js'
import { userDatabase } from '../database/DexieUserDatabase'
import { databaseConfig } from '../database/DatabaseConfig'
import {
  User,
  Session,
  CreateUserRequest,
  LoginRequest,
  LoginResponse,
  AuthResult
} from '../../shared/types/database'

// Classes d'erreur personnalisées
class ValidationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ValidationError'
  }
}

class AuthenticationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'AuthenticationError'
  }
}

class DatabaseError extends Error {
  constructor(message: string, public code?: string, public originalError?: any) {
    super(message)
    this.name = 'DatabaseError'
  }
}

export class AuthenticationService {
  private static instance: AuthenticationService
  private initialized = false
  private loginAttempts = new Map<string, { count: number; lastAttempt: Date; lockedUntil?: Date }>()

  private constructor() {}

  public static getInstance(): AuthenticationService {
    if (!AuthenticationService.instance) {
      AuthenticationService.instance = new AuthenticationService()
    }
    return AuthenticationService.instance
  }

  // Initialisation du service
  public async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Charger la configuration
      await databaseConfig.loadConfig()

      // Initialiser la base de données
      await userDatabase.open()
      await userDatabase.initializeDefaultData()

      // 🔧 CORRECTION : Attendre que l'initialisation se termine complètement
      await new Promise(resolve => setTimeout(resolve, 200))

      // Nettoyer les données expirées
      await this.cleanupExpiredData()

      this.initialized = true
      console.log('✅ Service d\'authentification initialisé')
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation du service d\'authentification:', error)
      throw new DatabaseError('Impossible d\'initialiser le service d\'authentification', 'INIT_ERROR', error)
    }
  }

  // Vérifier si le service est initialisé
  public isInitialized(): boolean {
    return this.initialized
  }



  // Connexion utilisateur
  public async login(credentials: LoginRequest): Promise<AuthResult> {
    await this.ensureInitialized()

    try {
      // Vérifier les tentatives de connexion
      if (await this.isAccountLocked(credentials.username)) {
        return {
          success: false,
          error: 'Compte temporairement verrouillé en raison de trop nombreuses tentatives',
          errorCode: 'ACCOUNT_LOCKED'
        }
      }

      // Rechercher l'utilisateur
      const user = await userDatabase.users
        .where('username')
        .equals(credentials.username)
        .first()

      if (!user || !user.isActive) {
        await this.recordFailedAttempt(credentials.username)
        return {
          success: false,
          error: 'Nom d\'utilisateur ou mot de passe incorrect',
          errorCode: 'INVALID_CREDENTIALS'
        }
      }

      // Vérifier le mot de passe
      const userCredentials = await userDatabase.credentials
        .where('userId')
        .equals(user.id!)
        .first()

      if (!userCredentials) {
        await this.recordFailedAttempt(credentials.username)
        return {
          success: false,
          error: 'Erreur de configuration du compte',
          errorCode: 'CREDENTIALS_ERROR'
        }
      }

      const isPasswordValid = await this.verifyPassword(
        credentials.password,
        userCredentials.passwordHash,
        userCredentials.salt,
        userCredentials.algorithm
      )

      if (!isPasswordValid) {
        await this.recordFailedAttempt(credentials.username)
        return {
          success: false,
          error: 'Nom d\'utilisateur ou mot de passe incorrect',
          errorCode: 'INVALID_CREDENTIALS'
        }
      }

      // Connexion réussie - réinitialiser les tentatives
      this.loginAttempts.delete(credentials.username)

      // Créer une session
      const session = await this.createSession(user, {
        rememberMe: credentials.rememberMe || false
      })

      // Mettre à jour la dernière connexion
      await userDatabase.users.update(user.id!, {
        lastLogin: new Date(),
        updatedAt: new Date()
      })

      // Log d'audit
      await userDatabase.auditLogs.add({
        userId: user.id,
        sessionId: session.id,
        action: 'LOGIN_SUCCESS',
        resource: 'auth',
        success: true,
        timestamp: new Date()
      })

      return {
        success: true,
        user,
        token: session.token,
        refreshToken: session.refreshToken
      }

    } catch (error) {
      console.error('Erreur lors de la connexion:', error)
      return {
        success: false,
        error: 'Erreur interne du serveur',
        errorCode: 'INTERNAL_ERROR'
      }
    }
  }

  // Déconnexion
  public async logout(token: string): Promise<boolean> {
    await this.ensureInitialized()

    try {
      const session = await userDatabase.sessions
        .where('token')
        .equals(token)
        .first()

      if (session && session.isActive) {
        // Désactiver la session
        await userDatabase.sessions.update(session.id!, {
          isActive: false,
          lastActivity: new Date()
        })

        // Log d'audit
        await userDatabase.auditLogs.add({
          userId: session.userId,
          sessionId: session.id,
          action: 'LOGOUT',
          resource: 'auth',
          success: true,
          timestamp: new Date()
        })

        return true
      }

      return false
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error)
      return false
    }
  }

  // Validation de session
  public async validateSession(token: string): Promise<User | null> {
    await this.ensureInitialized()

    try {
      const session = await userDatabase.sessions
        .where('token')
        .equals(token)
        .first()

      if (!session || !session.isActive || session.expiresAt <= new Date()) {
        return null
      }

      // Mettre à jour l'activité de la session
      await userDatabase.sessions.update(session.id!, {
        lastActivity: new Date()
      })

      // Récupérer l'utilisateur
      const user = await userDatabase.users.get(session.userId)
      
      if (!user || !user.isActive) {
        // Désactiver la session si l'utilisateur n'est plus actif
        await userDatabase.sessions.update(session.id!, {
          isActive: false
        })
        return null
      }

      return user
    } catch (error) {
      console.error('Erreur lors de la validation de session:', error)
      return null
    }
  }

  // Création d'utilisateur
  public async createUser(userData: CreateUserRequest): Promise<AuthResult> {
    await this.ensureInitialized()

    try {
      // Validation des données
      this.validateUserData(userData)

      // Vérifier l'unicité
      const existingUser = await userDatabase.users
        .where('username')
        .equals(userData.username)
        .or('email')
        .equals(userData.email)
        .first()

      if (existingUser) {
        return {
          success: false,
          error: 'Nom d\'utilisateur ou email déjà utilisé',
          errorCode: 'USER_EXISTS'
        }
      }

      // Hasher le mot de passe
      const { hash, salt } = await this.hashPassword(userData.password)

      // Créer l'utilisateur dans une transaction
      const userId = await userDatabase.transaction('rw', [userDatabase.users, userDatabase.credentials], async () => {
        const userId = await userDatabase.users.add({
          username: userData.username,
          email: userData.email,
          displayName: userData.displayName,
          role: userData.role,
          avatar: userData.avatar,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        })

        await userDatabase.credentials.add({
          userId: userId as number,
          passwordHash: hash,
          salt,
          algorithm: 'bcrypt',
          iterations: 12,
          createdAt: new Date(),
          updatedAt: new Date()
        })

        return userId as number
      })

      const user = await userDatabase.users.get(userId)
      
      return {
        success: true,
        user: user!
      }

    } catch (error) {
      if (error instanceof ValidationError) {
        return {
          success: false,
          error: error.message,
          errorCode: 'VALIDATION_ERROR'
        }
      }

      console.error('Erreur lors de la création d\'utilisateur:', error)
      return {
        success: false,
        error: 'Erreur lors de la création du compte',
        errorCode: 'CREATION_ERROR'
      }
    }
  }

  // Changement de mot de passe
  public async changePassword(userId: number, oldPassword: string, newPassword: string): Promise<boolean> {
    await this.ensureInitialized()

    try {
      const credentials = await userDatabase.credentials
        .where('userId')
        .equals(userId)
        .first()

      if (!credentials) {
        return false
      }

      // Vérifier l'ancien mot de passe
      const isOldPasswordValid = await this.verifyPassword(
        oldPassword,
        credentials.passwordHash,
        credentials.salt,
        credentials.algorithm
      )

      if (!isOldPasswordValid) {
        return false
      }

      // Hasher le nouveau mot de passe
      const { hash, salt } = await this.hashPassword(newPassword)

      // Mettre à jour les credentials
      await userDatabase.credentials.update(credentials.id!, {
        passwordHash: hash,
        salt,
        updatedAt: new Date()
      })

      // Log d'audit
      await userDatabase.auditLogs.add({
        userId,
        action: 'PASSWORD_CHANGED',
        resource: 'auth',
        success: true,
        timestamp: new Date()
      })

      return true
    } catch (error) {
      console.error('Erreur lors du changement de mot de passe:', error)
      return false
    }
  }

  // Méthodes privées

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize()
    }
  }

  private async hashPassword(password: string): Promise<{ hash: string; salt: string }> {
    const salt = await bcrypt.genSalt(12)
    const hash = await bcrypt.hash(password, salt)
    return { hash, salt }
  }

  private async verifyPassword(
    password: string,
    hash: string,
    salt: string,
    algorithm: string
  ): Promise<boolean> {
    switch (algorithm) {
      case 'bcrypt':
        return await bcrypt.compare(password, hash)
      default:
        throw new Error(`Algorithme de hashage non supporté: ${algorithm}`)
    }
  }

  private async createSession(user: User, options: { rememberMe: boolean }): Promise<Session> {
    const config = databaseConfig.getConfig()!
    const now = new Date()
    
    // Durée de session selon les options
    const sessionDuration = options.rememberMe 
      ? 30 * 24 * 60 * 60 * 1000 // 30 jours
      : config.sessionTimeout * 60 * 1000 // Configuration

    const expiresAt = new Date(now.getTime() + sessionDuration)
    const refreshExpiresAt = new Date(now.getTime() + sessionDuration * 2)

    const token = this.generateToken()
    const refreshToken = this.generateToken()

    const sessionId = await userDatabase.sessions.add({
      userId: user.id!,
      token,
      refreshToken,
      expiresAt,
      refreshExpiresAt,
      isActive: true,
      createdAt: now,
      lastActivity: now
    })

    return {
      id: sessionId as number,
      userId: user.id!,
      token,
      refreshToken,
      expiresAt,
      refreshExpiresAt,
      isActive: true,
      createdAt: now,
      lastActivity: now
    }
  }

  private generateToken(): string {
    return CryptoJS.lib.WordArray.random(256 / 8).toString()
  }

  private validateUserData(userData: CreateUserRequest): void {
    if (!userData.username || userData.username.length < 3) {
      throw new ValidationError('Le nom d\'utilisateur doit contenir au moins 3 caractères')
    }

    if (!/^[a-zA-Z0-9_-]{3,20}$/.test(userData.username)) {
      throw new ValidationError('Le nom d\'utilisateur ne peut contenir que des lettres, chiffres, tirets et underscores')
    }

    if (!userData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
      throw new ValidationError('Adresse email invalide')
    }

    if (!userData.password || userData.password.length < 8) {
      throw new ValidationError('Le mot de passe doit contenir au moins 8 caractères')
    }

    if (!userData.displayName || userData.displayName.length < 1) {
      throw new ValidationError('Le nom d\'affichage est requis')
    }
  }

  private async isAccountLocked(username: string): Promise<boolean> {
    const config = databaseConfig.getConfig()!
    const attempts = this.loginAttempts.get(username)

    if (!attempts) return false

    if (attempts.lockedUntil && attempts.lockedUntil > new Date()) {
      return true
    }

    if (attempts.count >= config.maxLoginAttempts) {
      const lockDuration = config.lockoutDuration * 60 * 1000
      attempts.lockedUntil = new Date(Date.now() + lockDuration)
      return true
    }

    return false
  }

  private async recordFailedAttempt(username: string): Promise<void> {
    const attempts = this.loginAttempts.get(username) || { count: 0, lastAttempt: new Date() }
    attempts.count++
    attempts.lastAttempt = new Date()
    this.loginAttempts.set(username, attempts)

    // Log d'audit
    await userDatabase.auditLogs.add({
      action: 'LOGIN_FAILED',
      resource: 'auth',
      details: { username, attempts: attempts.count },
      success: false,
      timestamp: new Date()
    })
  }

  private async cleanupExpiredData(): Promise<void> {
    try {
      const result = await userDatabase.cleanupExpiredData()
      console.log(`🧹 Nettoyage: ${result.sessions} sessions, ${result.auditLogs} logs supprimés`)
    } catch (error) {
      console.error('Erreur lors du nettoyage:', error)
    }
  }

  // Obtenir les statistiques d'authentification
  public async getAuthStats(): Promise<{
    totalUsers: number
    activeUsers: number
    activeSessions: number
    failedAttempts: number
    lockedAccounts: number
  }> {
    await this.ensureInitialized()

    const stats = await userDatabase.getStats()

    return {
      totalUsers: stats.users,
      activeUsers: stats.activeUsers,
      activeSessions: stats.activeSessions,
      failedAttempts: this.loginAttempts.size,
      lockedAccounts: Array.from(this.loginAttempts.values()).filter(a => a.lockedUntil && a.lockedUntil > new Date()).length
    }
  }

  // Débloquer un compte
  public async unlockAccount(username: string): Promise<boolean> {
    if (this.loginAttempts.has(username)) {
      this.loginAttempts.delete(username)

      // Log d'audit
      await userDatabase.auditLogs.add({
        action: 'ACCOUNT_UNLOCKED',
        resource: 'auth',
        details: { username },
        success: true,
        timestamp: new Date()
      })

      return true
    }
    return false
  }

  // === MÉTHODES DE COMPATIBILITÉ AVEC L'ANCIEN SYSTÈME ===

  // Vérifier si l'utilisateur est authentifié (via session localStorage)
  public isAuthenticated(): boolean {
    try {
      const sessionData = localStorage.getItem('datatec_session')
      if (!sessionData) return false

      const session = JSON.parse(sessionData)
      return session && new Date(session.expiresAt) > new Date()
    } catch {
      return false
    }
  }

  // Obtenir le token depuis localStorage
  public getToken(): string | null {
    try {
      const sessionData = localStorage.getItem('datatec_session')
      if (!sessionData) return null

      const session = JSON.parse(sessionData)
      return session?.token || null
    } catch {
      return null
    }
  }

  // Obtenir l'utilisateur actuel depuis la session
  public async getCurrentUser(): Promise<User | null> {
    try {
      const token = this.getToken()
      if (!token) return null

      return await this.validateSession(token)
    } catch {
      return null
    }
  }

  // === MÉTHODES DE GESTION DES UTILISATEURS ===

  // Lister les utilisateurs avec filtres
  public async listUsers(filters: any = {}): Promise<{ users: any[], total: number }> {
    try {
      await this.ensureInitialized()

      const { limit = 50, offset = 0, sortBy = 'displayName', sortOrder = 'asc', search, role, isActive } = filters

      let query = userDatabase.users.orderBy(sortBy)

      if (sortOrder === 'desc') {
        query = query.reverse()
      }

      // Appliquer les filtres
      let users = await query.toArray()

      if (search) {
        const searchLower = search.toLowerCase()
        users = users.filter(user =>
          user.username?.toLowerCase().includes(searchLower) ||
          user.displayName?.toLowerCase().includes(searchLower) ||
          user.email?.toLowerCase().includes(searchLower)
        )
      }

      if (role) {
        users = users.filter(user => user.role === role)
      }

      if (typeof isActive === 'boolean') {
        users = users.filter(user => user.isActive === isActive)
      }

      const total = users.length
      const paginatedUsers = users.slice(offset, offset + limit)

      // Convertir en format PublicUser (sans données sensibles)
      const publicUsers = paginatedUsers.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        displayName: user.displayName,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        avatar: user.avatar,
        language: user.language || 'fr'
      }))

      return { users: publicUsers, total }
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error)
      throw new DatabaseError('Impossible de récupérer les utilisateurs', 'LIST_USERS_ERROR', error)
    }
  }

  // Mettre à jour un utilisateur
  public async updateUser(userId: number, updateData: any): Promise<any> {
    try {
      await this.ensureInitialized()

      const user = await userDatabase.users.get(userId)
      if (!user) {
        throw new DatabaseError('Utilisateur non trouvé', 'USER_NOT_FOUND')
      }

      const updatedUser = {
        ...user,
        ...updateData,
        updatedAt: new Date()
      }

      await userDatabase.users.update(userId, updatedUser)

      // Retourner l'utilisateur mis à jour (format public)
      return {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        displayName: updatedUser.displayName,
        role: updatedUser.role,
        isActive: updatedUser.isActive,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
        avatar: updatedUser.avatar,
        language: updatedUser.language || 'fr'
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'utilisateur:', error)
      throw new DatabaseError('Impossible de mettre à jour l\'utilisateur', 'UPDATE_USER_ERROR', error)
    }
  }

  // Supprimer un utilisateur
  public async deleteUser(userId: number): Promise<boolean> {
    try {
      await this.ensureInitialized()

      const user = await userDatabase.users.get(userId)
      if (!user) {
        throw new DatabaseError('Utilisateur non trouvé', 'USER_NOT_FOUND')
      }

      // Ne pas permettre de supprimer l'admin principal
      if (user.username === 'admin') {
        throw new DatabaseError('Impossible de supprimer l\'administrateur principal', 'CANNOT_DELETE_ADMIN')
      }

      // Supprimer l'utilisateur et ses credentials dans une transaction
      await userDatabase.transaction('rw', [userDatabase.users, userDatabase.credentials], async () => {
        await userDatabase.users.delete(userId)
        await userDatabase.credentials.where('userId').equals(userId).delete()
      })

      return true
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'utilisateur:', error)
      throw new DatabaseError('Impossible de supprimer l\'utilisateur', 'DELETE_USER_ERROR', error)
    }
  }

  // Obtenir les statistiques des utilisateurs pour le dashboard admin
  public async getUserStats(): Promise<any> {
    try {
      await this.ensureInitialized()

      const users = await userDatabase.users.toArray()
      const now = new Date()
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

      // Calculer les statistiques de base
      const totalUsers = users.length
      const activeUsers = users.filter(u => u.isActive).length
      const newUsersThisMonth = users.filter(u => u.createdAt >= thisMonth).length

      // Répartition par rôles
      const usersByRole = users.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      // Répartition par statuts (basé sur isActive)
      const usersByStatus = {
        'ACTIVE': activeUsers,
        'INACTIVE': totalUsers - activeUsers
      }

      // Utilisateurs les plus actifs (simulé pour l'instant)
      const mostActiveUsers = users
        .filter(u => u.isActive)
        .slice(0, 5)
        .map(user => ({
          user: {
            id: user.id,
            username: user.username,
            displayName: user.displayName
          },
          activityScore: Math.floor(Math.random() * 50) + 10 // Simulé
        }))

      return {
        totalUsers,
        activeUsers,
        newUsersThisMonth,
        usersByRole,
        usersByStatus,
        mostActiveUsers
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques utilisateur:', error)
      throw new DatabaseError('Impossible de récupérer les statistiques utilisateur', 'GET_USER_STATS_ERROR', error)
    }
  }
}

// Instance singleton
export const authService = AuthenticationService.getInstance()

// Export par défaut
export default authService
