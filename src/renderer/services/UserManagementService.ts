// Service proxy unifié pour la gestion des utilisateurs
// Simple proxy vers AuthenticationService - Service unique d'authentification

import { authService } from './AuthenticationService'
import {
  User,
  CreateUserRequest,
  LoginRequest,
  AuthResult
} from '../../shared/types/database'

/**
 * Service proxy unifié pour la gestion des utilisateurs
 * Toutes les opérations sont déléguées à AuthenticationService
 * 
 * ✅ UN SEUL SYSTÈME D'AUTHENTIFICATION
 * ✅ UNE SEULE BASE DE DONNÉES (Dexie/IndexedDB)
 * ✅ AUCUNE LOGIQUE MÉTIER DUPLIQUÉE
 */
export class UserManagementService {
  private static instance: UserManagementService

  private constructor() {}

  public static getInstance(): UserManagementService {
    if (!UserManagementService.instance) {
      UserManagementService.instance = new UserManagementService()
    }
    return UserManagementService.instance
  }

  // === PROXY DIRECT VERS AUTHENTICATIONSERVICE ===

  // Initialisation
  public async initialize(): Promise<void> {
    console.log('🚀 Initialisation du système unifié d\'authentification...')
    await authService.initialize()
    console.log('✅ Système d\'authentification unifié opérationnel !')
  }

  // Vérification d'initialisation
  public isInitialized(): boolean {
    return authService.isInitialized()
  }

  // Connexion
  public async login(credentials: LoginRequest): Promise<AuthResult> {
    return await authService.login(credentials)
  }

  // Déconnexion
  public async logout(token: string): Promise<boolean> {
    return await authService.logout(token)
  }

  // Validation de session
  public async validateSession(token: string): Promise<User | null> {
    return await authService.validateSession(token)
  }

  // Création d'utilisateur
  public async createUser(userData: CreateUserRequest): Promise<AuthResult> {
    return await authService.createUser(userData)
  }

  // Changement de mot de passe
  public async changePassword(userId: number, oldPassword: string, newPassword: string): Promise<boolean> {
    return await authService.changePassword(userId, oldPassword, newPassword)
  }

  // Obtenir un utilisateur par ID
  public async getUser(userId: number): Promise<User | null> {
    return await authService.getUserById(userId)
  }

  // Obtenir un utilisateur par nom
  public async getUserByUsername(username: string): Promise<User | null> {
    return await authService.getUserByUsername(username)
  }

  // Obtenir les statistiques
  public async getSystemStats(): Promise<any> {
    return await authService.getAuthStats()
  }

  // === MÉTHODES SUPPLÉMENTAIRES POUR LA GESTION DES UTILISATEURS ===

  // Lister les utilisateurs avec filtres
  public async listUsers(filters: any): Promise<{ users: any[], total: number }> {
    try {
      await this.ensureInitialized()
      return await authService.listUsers(filters)
    } catch (error) {
      console.error('Erreur lors de la récupération des utilisateurs:', error)
      throw error
    }
  }

  // Mettre à jour un utilisateur
  public async updateUser(userId: number, updateData: any): Promise<any> {
    try {
      await this.ensureInitialized()
      return await authService.updateUser(userId, updateData)
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'utilisateur:', error)
      throw error
    }
  }

  // Supprimer un utilisateur
  public async deleteUser(userId: number): Promise<boolean> {
    try {
      await this.ensureInitialized()
      return await authService.deleteUser(userId)
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'utilisateur:', error)
      throw error
    }
  }

  // Obtenir les statistiques des utilisateurs pour le dashboard
  public async getUserStats(): Promise<any> {
    try {
      await this.ensureInitialized()
      return await authService.getUserStats()
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques utilisateur:', error)
      throw error
    }
  }

  // S'assurer que le service est initialisé
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized()) {
      await this.initialize()
    }
  }
}

// Instance singleton
export const userManagementService = UserManagementService.getInstance()

// Export par défaut
export default userManagementService
