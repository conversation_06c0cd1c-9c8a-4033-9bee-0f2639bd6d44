// Service centralisé pour la gestion des rôles
// Permet de partager les rôles entre tous les composants

export interface Role {
  id: string
  name: string
  displayName: string
  isSystem: boolean
  isProtected: boolean
}

// Rôles par défaut du système
const DEFAULT_ROLES: Role[] = [
  { id: 'admin', name: 'admin', displayName: 'Administrateur', isSystem: true, isProtected: true },
  { id: 'moderator', name: 'moderator', displayName: 'Modérateur', isSystem: true, isProtected: false },
  { id: 'user', name: 'user', displayName: 'Utilisateur', isSystem: true, isProtected: false },
  { id: 'guest', name: 'guest', displayName: 'Invité', isSystem: true, isProtected: false }
]

// Clé pour le localStorage
const ROLES_STORAGE_KEY = 'datatec_custom_roles'

class RoleService {
  private roles: Role[] = []
  private listeners: Array<(roles: Role[]) => void> = []

  constructor() {
    this.loadRoles()
  }

  // Charger les rôles depuis le localStorage
  private loadRoles(): void {
    try {
      const savedRoles = localStorage.getItem(ROLES_STORAGE_KEY)
      if (savedRoles) {
        const customRoles = JSON.parse(savedRoles)
        // Combiner les rôles par défaut avec les rôles personnalisés
        this.roles = [...DEFAULT_ROLES, ...customRoles]
      } else {
        this.roles = [...DEFAULT_ROLES]
      }
    } catch (error) {
      console.error('Erreur lors du chargement des rôles:', error)
      this.roles = [...DEFAULT_ROLES]
    }
    this.notifyListeners()
  }

  // Sauvegarder les rôles dans le localStorage
  private saveRoles(): void {
    try {
      // Sauvegarder seulement les rôles personnalisés (non système)
      const customRoles = this.roles.filter(role => !role.isSystem)
      localStorage.setItem(ROLES_STORAGE_KEY, JSON.stringify(customRoles))
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des rôles:', error)
    }
  }

  // Notifier tous les listeners des changements
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.roles]))
  }

  // Obtenir tous les rôles
  getRoles(): Role[] {
    return [...this.roles]
  }

  // Obtenir les rôles formatés pour les composants Select
  getRolesForSelect(): Array<{ value: string; label: string }> {
    return this.roles.map(role => ({
      value: role.name,
      label: role.displayName
    }))
  }

  // Ajouter un nouveau rôle
  addRole(name: string, displayName: string): boolean {
    // Vérifier si le rôle existe déjà
    if (this.roles.some(role => role.name.toLowerCase() === name.toLowerCase())) {
      return false
    }

    const newRole: Role = {
      id: name.toLowerCase(),
      name: name.toLowerCase(),
      displayName,
      isSystem: false,
      isProtected: false
    }

    this.roles.push(newRole)
    this.saveRoles()
    this.notifyListeners()
    return true
  }

  // Mettre à jour un rôle
  updateRole(roleId: string, displayName: string): boolean {
    const roleIndex = this.roles.findIndex(role => role.id === roleId)
    if (roleIndex === -1 || this.roles[roleIndex].isProtected) {
      return false
    }

    this.roles[roleIndex].displayName = displayName
    this.saveRoles()
    this.notifyListeners()
    return true
  }

  // Supprimer un rôle
  deleteRole(roleId: string): boolean {
    const roleIndex = this.roles.findIndex(role => role.id === roleId)
    if (roleIndex === -1 || this.roles[roleIndex].isProtected) {
      return false
    }

    this.roles.splice(roleIndex, 1)
    this.saveRoles()
    this.notifyListeners()
    return true
  }

  // S'abonner aux changements de rôles
  subscribe(listener: (roles: Role[]) => void): () => void {
    this.listeners.push(listener)
    // Envoyer immédiatement les rôles actuels
    listener([...this.roles])
    
    // Retourner une fonction de désabonnement
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  // Recharger les rôles depuis le localStorage
  reload(): void {
    this.loadRoles()
  }
}

// Instance singleton
export const roleService = new RoleService()
