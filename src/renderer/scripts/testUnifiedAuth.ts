// Script de test pour vérifier que l'authentification unifiée fonctionne
// Test de connexion avec l'utilisateur "oussama"

import { authService } from '../services/AuthenticationService'
import { userManagementService } from '../services/UserManagementService'

export async function testUnifiedAuth(): Promise<void> {
  console.log('🧪 Test de l\'authentification unifiée...\n')

  try {
    // 1. Initialiser le système
    console.log('1️⃣ Initialisation du système...')
    await authService.initialize()
    console.log('✅ Système initialisé\n')

    // 2. Tester la connexion avec l'utilisateur "oussama"
    console.log('2️⃣ Test de connexion avec l\'utilisateur "oussama"...')
    const loginResult = await authService.login({
      username: 'oussama',
      password: 'ousse123'
    })

    if (loginResult.success) {
      console.log('✅ Connexion réussie !')
      console.log(`   Utilisateur: ${loginResult.user?.displayName}`)
      console.log(`   Email: ${loginResult.user?.email}`)
      console.log(`   Rôle: ${loginResult.user?.role}`)
      console.log(`   Token: ${loginResult.token ? 'Présent' : 'Absent'}\n`)

      // 3. Tester le proxy UserManagementService
      console.log('3️⃣ Test du proxy UserManagementService...')
      const proxyUser = await userManagementService.getUserByUsername('oussama')
      
      if (proxyUser) {
        console.log('✅ Proxy fonctionne correctement !')
        console.log(`   Utilisateur récupéré: ${proxyUser.displayName}\n`)
      } else {
        console.log('❌ Proxy ne fonctionne pas correctement\n')
      }

      // 4. Tester la validation de session
      console.log('4️⃣ Test de validation de session...')
      if (loginResult.token) {
        const sessionUser = await authService.validateSession(loginResult.token)
        if (sessionUser) {
          console.log('✅ Session valide !')
          console.log(`   Utilisateur de session: ${sessionUser.displayName}\n`)
        } else {
          console.log('❌ Session invalide\n')
        }
      }

      // 5. Déconnexion
      console.log('5️⃣ Test de déconnexion...')
      if (loginResult.token) {
        const logoutResult = await authService.logout(loginResult.token)
        if (logoutResult) {
          console.log('✅ Déconnexion réussie !\n')
        } else {
          console.log('❌ Échec de déconnexion\n')
        }
      }

    } else {
      console.log('❌ Échec de connexion !')
      console.log(`   Erreur: ${loginResult.error}\n`)
      
      // Vérifier si l'utilisateur existe
      console.log('🔍 Vérification de l\'existence de l\'utilisateur...')
      const user = await authService.getUserByUsername('oussama')
      if (user) {
        console.log('✅ L\'utilisateur existe dans la base de données')
        console.log(`   Nom: ${user.displayName}`)
        console.log(`   Email: ${user.email}`)
        console.log(`   Actif: ${user.isActive ? 'Oui' : 'Non'}`)
      } else {
        console.log('❌ L\'utilisateur n\'existe pas dans la base de données')
        console.log('💡 Vous devez d\'abord créer l\'utilisateur via l\'interface')
      }
    }

    // 6. Test des statistiques
    console.log('6️⃣ Test des statistiques système...')
    const stats = await authService.getAuthStats()
    console.log('✅ Statistiques récupérées :')
    console.log(`   Utilisateurs totaux: ${stats.totalUsers}`)
    console.log(`   Utilisateurs actifs: ${stats.activeUsers}`)
    console.log(`   Sessions actives: ${stats.activeSessions}`)

  } catch (error) {
    console.error('💥 Erreur lors du test:', error)
  }

  console.log('\n🎯 Test terminé !')
}

// Fonction pour tester la création d'utilisateur
export async function testCreateUser(): Promise<void> {
  console.log('👤 Test de création d\'utilisateur...\n')

  try {
    await authService.initialize()

    const result = await authService.createUser({
      username: 'test_user',
      email: '<EMAIL>',
      password: 'test123',
      displayName: 'Utilisateur Test',
      role: 'user'
    })

    if (result.success) {
      console.log('✅ Utilisateur créé avec succès !')
      console.log(`   Nom: ${result.user?.displayName}`)
      console.log(`   Email: ${result.user?.email}`)
    } else {
      console.log('❌ Échec de création')
      console.log(`   Erreur: ${result.error}`)
    }
  } catch (error) {
    console.error('💥 Erreur:', error)
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  testUnifiedAuth()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('💥 Erreur fatale:', error)
      process.exit(1)
    })
}

export default testUnifiedAuth
