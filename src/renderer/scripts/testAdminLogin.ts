// Script de test rapide pour vérifier la connexion admin
// À exécuter dans la console du navigateur

import { authService } from '../services/AuthenticationService'

export async function testAdminLogin(): Promise<void> {
  console.log('🧪 Test de connexion admin...\n')

  try {
    // Initialiser le service
    console.log('1️⃣ Initialisation...')
    await authService.initialize()
    console.log('✅ Service initialisé\n')

    // Tester la connexion
    console.log('2️⃣ Test de connexion admin/admin123...')
    const result = await authService.login({
      username: 'admin',
      password: 'admin123'
    })

    if (result.success) {
      console.log('🎉 SUCCÈS ! Admin peut se connecter !')
      console.log(`   Utilisateur: ${result.user?.displayName}`)
      console.log(`   Email: ${result.user?.email}`)
      console.log(`   Rôle: ${result.user?.role}`)
      console.log(`   Token: ${result.token ? 'Présent' : 'Absent'}`)
    } else {
      console.log('❌ ÉCHEC de connexion')
      console.log(`   Erreur: ${result.error}`)
    }

  } catch (error) {
    console.error('💥 Erreur:', error)
  }
}

// Fonction pour exécuter dans la console du navigateur
(window as any).testAdminLogin = testAdminLogin

export default testAdminLogin
