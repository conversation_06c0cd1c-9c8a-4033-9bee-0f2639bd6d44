// Script pour nettoyer et réinitialiser la base de données
// À exécuter dans la console du navigateur

import { userDatabase } from '../database/DexieUserDatabase'

export async function resetDatabase(): Promise<void> {
  console.log('🧹 Nettoyage et réinitialisation de la base de données...\n')

  try {
    // 1. Fermer la base de données
    console.log('1️⃣ Fermeture de la base de données...')
    await userDatabase.close()
    console.log('✅ Base fermée\n')

    // 2. Supprimer la base de données
    console.log('2️⃣ Suppression de la base de données...')
    await userDatabase.delete()
    console.log('✅ Base supprimée\n')

    // 3. Attendre un peu
    console.log('3️⃣ Attente...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('✅ Attente terminée\n')

    // 4. Rouvrir et réinitialiser
    console.log('4️⃣ Réouverture et réinitialisation...')
    await userDatabase.open()
    await userDatabase.initializeDefaultData()
    console.log('✅ Base réinitialisée\n')

    // 5. Vérifier que l'admin existe
    console.log('5️⃣ Vérification de l\'admin...')
    const adminUser = await userDatabase.users.where('username').equals('admin').first()
    
    if (adminUser) {
      console.log('✅ Admin trouvé :')
      console.log(`   ID: ${adminUser.id}`)
      console.log(`   Username: ${adminUser.username}`)
      console.log(`   Email: ${adminUser.email}`)
      console.log(`   DisplayName: ${adminUser.displayName}`)
      
      // Vérifier les credentials
      const credentials = await userDatabase.credentials.where('userId').equals(adminUser.id!).first()
      if (credentials) {
        console.log('✅ Credentials trouvées')
        console.log(`   Algorithm: ${credentials.algorithm}`)
        console.log(`   Iterations: ${credentials.iterations}`)
      } else {
        console.log('❌ Credentials manquantes')
      }
    } else {
      console.log('❌ Admin non trouvé')
    }

    console.log('\n🎉 Réinitialisation terminée !')
    console.log('Vous pouvez maintenant essayer de vous connecter avec :')
    console.log('   Username: admin')
    console.log('   Password: admin123')

  } catch (error) {
    console.error('💥 Erreur lors de la réinitialisation:', error)
  }
}

// Fonction pour exécuter dans la console du navigateur
(window as any).resetDatabase = resetDatabase

export default resetDatabase
