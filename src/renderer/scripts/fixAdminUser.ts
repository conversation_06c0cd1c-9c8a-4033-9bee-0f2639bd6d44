// Script pour diagnostiquer et réparer le problème de connexion admin
// Vérifie et recrée l'utilisateur admin si nécessaire

import { authService } from '../services/AuthenticationService'
import { userDatabase } from '../database/DexieUserDatabase'
import bcrypt from 'bcryptjs'

export async function fixAdminUser(): Promise<void> {
  console.log('🔧 Diagnostic et réparation de l\'utilisateur admin...\n')

  try {
    // 1. Initialiser le système
    console.log('1️⃣ Initialisation du système...')
    await authService.initialize()
    console.log('✅ Système initialisé\n')

    // 2. Vérifier si l'utilisateur admin existe
    console.log('2️⃣ Vérification de l\'existence de l\'utilisateur admin...')
    const adminUser = await userDatabase.users.where('username').equals('admin').first()
    
    if (adminUser) {
      console.log('✅ Utilisateur admin trouvé :')
      console.log(`   ID: ${adminUser.id}`)
      console.log(`   Username: ${adminUser.username}`)
      console.log(`   Email: ${adminUser.email}`)
      console.log(`   DisplayName: ${adminUser.displayName}`)
      console.log(`   Role: ${adminUser.role}`)
      console.log(`   IsActive: ${adminUser.isActive}`)
      console.log(`   CreatedAt: ${adminUser.createdAt}\n`)

      // 3. Vérifier les credentials
      console.log('3️⃣ Vérification des credentials...')
      const credentials = await userDatabase.credentials.where('userId').equals(adminUser.id!).first()
      
      if (credentials) {
        console.log('✅ Credentials trouvées :')
        console.log(`   Algorithm: ${credentials.algorithm}`)
        console.log(`   Iterations: ${credentials.iterations}`)
        console.log(`   Salt présent: ${credentials.salt ? 'Oui' : 'Non'}`)
        console.log(`   Hash présent: ${credentials.passwordHash ? 'Oui' : 'Non'}\n`)

        // 4. Tester le mot de passe
        console.log('4️⃣ Test du mot de passe "admin123"...')
        try {
          const isValid = await bcrypt.compare('admin123', credentials.passwordHash)
          if (isValid) {
            console.log('✅ Mot de passe correct !\n')
            
            // 5. Tester la connexion via le service
            console.log('5️⃣ Test de connexion via AuthenticationService...')
            const loginResult = await authService.login({
              username: 'admin',
              password: 'admin123'
            })
            
            if (loginResult.success) {
              console.log('✅ Connexion réussie via le service !')
              console.log(`   Token: ${loginResult.token ? 'Présent' : 'Absent'}`)
              console.log(`   User: ${loginResult.user?.displayName}\n`)
            } else {
              console.log('❌ Échec de connexion via le service :')
              console.log(`   Erreur: ${loginResult.error}\n`)
            }
          } else {
            console.log('❌ Mot de passe incorrect ! Recréation nécessaire...\n')
            await recreateAdminUser(adminUser.id!)
          }
        } catch (error) {
          console.log('❌ Erreur lors de la vérification du mot de passe :')
          console.log(`   ${error}\n`)
          await recreateAdminUser(adminUser.id!)
        }
      } else {
        console.log('❌ Credentials manquantes ! Recréation nécessaire...\n')
        await recreateAdminUser(adminUser.id!)
      }
    } else {
      console.log('❌ Utilisateur admin non trouvé ! Création nécessaire...\n')
      await createAdminUser()
    }

    // 6. Test final
    console.log('6️⃣ Test final de connexion...')
    const finalTest = await authService.login({
      username: 'admin',
      password: 'admin123'
    })
    
    if (finalTest.success) {
      console.log('🎉 SUCCÈS ! L\'utilisateur admin peut maintenant se connecter !')
      console.log(`   Utilisateur: ${finalTest.user?.displayName}`)
      console.log(`   Email: ${finalTest.user?.email}`)
      console.log(`   Rôle: ${finalTest.user?.role}`)
    } else {
      console.log('💥 ÉCHEC ! Problème persistant :')
      console.log(`   Erreur: ${finalTest.error}`)
    }

  } catch (error) {
    console.error('💥 Erreur fatale lors du diagnostic :', error)
  }
}

// Fonction pour recréer les credentials de l'admin
async function recreateAdminUser(userId: number): Promise<void> {
  console.log('🔄 Recréation des credentials admin...')
  
  try {
    // Supprimer les anciennes credentials
    await userDatabase.credentials.where('userId').equals(userId).delete()
    
    // Créer de nouvelles credentials
    const salt = await bcrypt.genSalt(12)
    const passwordHash = await bcrypt.hash('admin123', salt)
    
    await userDatabase.credentials.add({
      userId,
      passwordHash,
      salt,
      algorithm: 'bcrypt',
      iterations: 12,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    
    console.log('✅ Credentials admin recréées avec succès !\n')
  } catch (error) {
    console.error('❌ Erreur lors de la recréation des credentials :', error)
  }
}

// Fonction pour créer complètement l'utilisateur admin
async function createAdminUser(): Promise<void> {
  console.log('👤 Création complète de l\'utilisateur admin...')
  
  try {
    // Créer l'utilisateur
    const userId = await userDatabase.users.add({
      username: 'admin',
      email: '<EMAIL>',
      displayName: 'Administrateur',
      role: 'admin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    
    // Créer les credentials
    const salt = await bcrypt.genSalt(12)
    const passwordHash = await bcrypt.hash('admin123', salt)
    
    await userDatabase.credentials.add({
      userId: userId as number,
      passwordHash,
      salt,
      algorithm: 'bcrypt',
      iterations: 12,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    
    console.log('✅ Utilisateur admin créé avec succès !')
    console.log(`   ID: ${userId}`)
    console.log(`   Username: admin`)
    console.log(`   Password: admin123\n`)
  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'utilisateur admin :', error)
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  fixAdminUser()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('💥 Erreur fatale:', error)
      process.exit(1)
    })
}

export default fixAdminUser
