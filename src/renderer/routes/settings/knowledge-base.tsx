import { <PERSON>, But<PERSON>, Flex, <PERSON>ack, Text, Title } from '@mantine/core'
import { IconInfoCircle, IconPlus } from '@tabler/icons-react'
import { useState, useEffect } from 'react'
import KnowledgeBaseForm from '../../components/KnowledgeBaseForm'
import KnowledgeBaseList from '../../components/KnowledgeBaseList'
import { createFileRoute } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { useKnowledgeBase, KnowledgeBaseData } from '../../hooks/useKnowledgeBase'
import { notifications } from '@mantine/notifications'

export const Route = createFileRoute('/settings/knowledge-base')({
  component: RouteComponent,
})

function RouteComponent() {
  const { t } = useTranslation()
  const { knowledgeBases, deleteKnowledgeBase, loading } = useKnowledgeBase()
  const [showForm, setShowForm] = useState(false)
  const [editingKB, setEditingKB] = useState<KnowledgeBaseData | null>(null)

  const handleAddKnowledgeBase = () => {
    setEditingKB(null)
    setShowForm(true)
  }

  const handleEditKnowledgeBase = (kb: KnowledgeBaseData) => {
    setEditingKB(kb)
    setShowForm(true)
  }

  const handleCloseForm = () => {
    setShowForm(false)
    setEditingKB(null)
  }

  const handleSaveKnowledgeBase = async (data: KnowledgeBaseData) => {
    // La sauvegarde est gérée dans le hook useKnowledgeBase
    // Fermer le formulaire après sauvegarde
    setShowForm(false)
    setEditingKB(null)
  }

  const handleDeleteKnowledgeBase = async (id: string) => {
    try {
      const result = await deleteKnowledgeBase(id)

      if (result.success) {
        notifications.show({
          title: 'Succès',
          message: 'Base de connaissances supprimée avec succès',
          color: 'green'
        })
      } else {
        notifications.show({
          title: 'Erreur',
          message: result.error || 'Erreur lors de la suppression',
          color: 'red'
        })
      }
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur inattendue lors de la suppression',
        color: 'red'
      })
    }
  }

  if (showForm) {
    return (
      <Box p="md">
        <KnowledgeBaseForm
          onClose={handleCloseForm}
          onSave={handleSaveKnowledgeBase}
          editingKB={editingKB}
        />
      </Box>
    )
  }

  return (
    <Box p="md">
      {/* Afficher la liste si des bases de connaissances existent */}
      {knowledgeBases.length > 0 ? (
        <KnowledgeBaseList
          knowledgeBases={knowledgeBases}
          onAdd={handleAddKnowledgeBase}
          onEdit={handleEditKnowledgeBase}
          onDelete={handleDeleteKnowledgeBase}
          loading={loading}
        />
      ) : (
        <>
          {/* Header with title and Add button */}
          <Flex justify="space-between" align="center" mb="lg">
            <Title order={5} c="var(--mantine-color-gray-0)" fw={500}>
              Base de connaissances
            </Title>
            <Button
              leftSection={<IconPlus size={14} />}
              variant="outline"
              size="xs"
              color="blue"
              onClick={handleAddKnowledgeBase}
              styles={{
                root: {
                  borderColor: 'var(--mantine-color-blue-6)',
                  color: 'var(--mantine-color-blue-4)',
                  fontSize: '0.75rem',
                  '&:hover': {
                    backgroundColor: 'var(--mantine-color-blue-9)',
                    borderColor: 'var(--mantine-color-blue-5)',
                  },
                },
              }}
            >
              Ajouter
            </Button>
          </Flex>

          {/* Empty state container */}
          <Box
            style={{
              backgroundColor: 'var(--mantine-color-dark-7)',
              borderRadius: '8px',
              border: '1px solid var(--mantine-color-dark-4)',
              minHeight: '350px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '2rem 1.5rem',
            }}
          >
            {/* Info icon */}
            <Box
              style={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                backgroundColor: 'var(--mantine-color-dark-5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '1rem',
              }}
            >
              <IconInfoCircle
                size={24}
                color="var(--mantine-color-gray-4)"
              />
            </Box>

            {/* Empty state text */}
            <Stack align="center" gap={4} maw={450}>
              <Text
                size="lg"
                fw={400}
                ta="center"
                c="#ffffff"
                style={{ fontSize: '18px', lineHeight: '1.4' }}
              >
                Aucune base de connaissances pour l'instant
              </Text>
              <Text
                size="sm"
                c="#888888"
                ta="center"
                lh={1.5}
                px="xs"
                style={{ fontSize: '14px', marginTop: '8px' }}
              >
                Créez votre première base de connaissances pour commencer à ajouter
                des documents et améliorer vos conversations (AI) avec des
                informations contextuelles.
              </Text>
            </Stack>

            {/* Create first knowledge base button */}
            <Button
              leftSection={<IconPlus size={14} />}
              variant="outline"
              size="sm"
              mt="lg"
              color="blue"
              onClick={handleAddKnowledgeBase}
              styles={{
                root: {
                  borderColor: 'var(--mantine-color-blue-6)',
                  color: 'var(--mantine-color-blue-4)',
                  fontSize: '0.875rem',
                  '&:hover': {
                    backgroundColor: 'var(--mantine-color-blue-9)',
                    borderColor: 'var(--mantine-color-blue-5)',
                  },
                },
              }}
            >
              Créer une première base de connaissances
            </Button>
          </Box>
        </>
      )}
    </Box>
  )
}
