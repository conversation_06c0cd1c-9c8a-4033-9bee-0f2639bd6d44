import {
  Box,
  Stack,
  Tabs,
  Title,
  Select,
  Paper,
  Divider,
  Button,
  TextInput,
  Group,
  ActionIcon,
  Table,
  Badge,
  Modal,
  Text,
  Grid,
  Card,
  Avatar,
  Switch,
  Checkbox,
  Transition,
  Flex,
  Breadcrumbs,
  Anchor,
  Menu
} from '@mantine/core'
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconCheck,
  IconX,
  IconArrowLeft,
  IconPrinter,
  IconChevronRight,
  IconSettings,
  IconCopy,
  IconDownload,
  IconFolder,
  IconFolderOpen,
  IconCategory,
  IconBuildingStore,
  IconScale,
  IconSpeakerphone,
  IconCoin,
  IconUsers,
  IconUserPlus,
  IconUserEdit,
  IconUserX,
  IconShield,
  IconEye,
  IconLock,
  IconUserCheck,
  IconUserCog,
  IconApi,
  IconCloud,
  IconServer,
  IconBrandOpenai,
  IconRobot,
  IconBrain,
  IconCpu,
  IconKey,
  IconToggleLeft,
  IconToggleRight,
  IconCloudComputing,
  IconDatabase,
  IconMessage,
  IconPhoto,
  IconSearch,
  IconFileText,
  IconTemperature,
  IconAdjustments,
  IconBolt,
  IconTarget,
  IconPalette,
  IconNumbers,
  IconClock,
  IconWorldWww,
  IconBrandBing,
  IconShieldCheck,
  IconFilter,
  IconLanguage,
  IconNews,
  IconFileSearch,
  IconGlobe,
  IconLockAccess,
  IconBooks,
  IconFolderPlus,
  IconFolders,
  IconFileUpload,
  IconFile,
  IconFileWord,
  IconShare,
  IconTags,
  IconMessageCircle,
  IconTextSize,
  IconBell,
  IconHistory,
  IconMoon,
  IconSun,
  IconVolume,
  IconVolumeOff,
  IconWorld,
  IconHome,
  IconRefresh,
  IconAccessible,
  IconKeyboard,
  IconReportAnalytics,
  IconNetwork,
  IconChartBar,
  IconActivity,
  IconTool,
  IconBug,
  IconCertificate,
  IconCloudUpload,
  IconArrowRight,
  IconArrowUp,
  IconArrowDown,
  IconPlayerPlay,
  IconWindowMaximize,
  IconWindowMinimize,
  IconCirclePlus,
  IconVideo,
  IconHammer,
  IconLink,
  IconCode
} from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'
import { createFileRoute } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'
import { useState, useEffect } from 'react'
import { useRoles } from '../../hooks/useRoles'
import { ProtectedRoute } from '@/components/user-management/ProtectedRoute'
import { AdminDashboard } from '@/components/user-management/AdminDashboard'
import { AuditLogComponent } from '@/components/user-management/AuditLog'
import { UserManagement } from '@/components/user-management/UserManagement'
import { UserRole } from '../../../shared/types/user'

export const Route = createFileRoute('/settings/admin')({
  component: RouteComponent,
})

// Permissions par défaut du système
const DEFAULT_PERMISSIONS = [
  { id: 'users', name: 'Utilisateurs', icon: 'U', color: 'blue' },
  { id: 'copilots', name: 'Mes copilotes', icon: 'M', color: 'cyan' },
  { id: 'model_provider', name: 'Fournisseur de Modèle', icon: 'F', color: 'violet' },
  { id: 'default_models', name: 'Modèles par défaut', icon: 'D', color: 'indigo' },
  { id: 'web_search', name: 'Recherche Internet', icon: 'R', color: 'teal' },
  { id: 'knowledge_base', name: 'Base de connaissances', icon: 'B', color: 'green' },
  { id: 'discussion_settings', name: 'Paramètres de discussion', icon: 'P', color: 'orange' },
  { id: 'keyboard_shortcuts', name: 'Raccourcis clavier', icon: 'K', color: 'gray' },
  { id: 'general_settings', name: 'Paramètres généraux', icon: 'G', color: 'dark' },
  { id: 'administration', name: 'Administration', icon: 'A', color: 'red' },
  { id: 'input_zone', name: 'Zone de Saisie', icon: 'Z', color: 'lime' }
]

// Structure hiérarchique des catégories
const CATEGORY_HIERARCHY = {
  categories: {
    id: 'categories',
    name: 'Catégories',
    description: 'Accès aux catégories de copilotes',
    children: [
      { id: 'category_general', name: 'Général', description: 'Copilotes de catégorie générale' },
      { id: 'category_juridique', name: 'Juridique', description: 'Copilotes spécialisés en droit' },
      { id: 'category_marketing', name: 'Marketing', description: 'Copilotes pour le marketing et communication' },
      { id: 'category_finance', name: 'Finance', description: 'Copilotes pour la gestion financière' }
    ]
  },
  manage_categories: {
    id: 'manage_categories',
    name: 'Gérer les catégories',
    description: 'Créer, modifier et supprimer des catégories',
    children: [
      { id: 'create_categories', name: 'Créer des catégories', description: 'Créer de nouvelles catégories' },
      { id: 'edit_categories', name: 'Modifier des catégories', description: 'Modifier les catégories existantes' },
      { id: 'delete_categories', name: 'Supprimer des catégories', description: 'Supprimer des catégories' }
    ]
  }
}

// Fonction pour obtenir l'icône d'une permission
const getPermissionIcon = (permissionId: string) => {
  const iconMap = {
    // === CATÉGORIES ===
    // Catégories principales
    'categories': IconFolder,
    'manage_categories': IconSettings,

    // Sous-catégories spécifiques
    'category_general': IconCategory,
    'category_juridique': IconScale,
    'category_marketing': IconSpeakerphone,
    'category_finance': IconCoin,

    // Actions de gestion des catégories
    'create_categories': IconPlus,
    'edit_categories': IconEdit,
    'delete_categories': IconTrash,

    // === UTILISATEURS ===
    // Gestion principale
    'user_management': IconUsers,
    'user_security': IconShield,

    // Actions CRUD utilisateurs
    'view_users': IconEye,
    'create_users': IconUserPlus,
    'edit_users': IconUserEdit,
    'delete_users': IconUserX,

    // Gestion des rôles et permissions
    'manage_roles': IconUserCog,
    'assign_roles': IconUserCheck,
    'manage_permissions': IconLock,

    // Sécurité et audit
    'view_audit_logs': IconEye,
    'manage_sessions': IconSettings,
    'reset_passwords': IconLock,

    // === FOURNISSEURS DE MODÈLES ===
    // Gestion principale
    'provider_management': IconApi,
    'model_configuration': IconBrain,

    // Fournisseurs spécifiques
    'openai_provider': IconBrandOpenai,
    'claude_provider': IconRobot,
    'gemini_provider': IconCloud,
    'ollama_provider': IconServer,
    'groq_provider': IconCpu,
    'deepseek_provider': IconDatabase,
    'mistral_provider': IconCloudComputing,
    'openrouter_provider': IconApi,
    'custom_providers': IconSettings,

    // Actions de gestion des fournisseurs
    'configure_providers': IconSettings,
    'manage_api_keys': IconKey,
    'enable_disable_providers': IconToggleLeft,

    // Gestion des modèles
    'view_models': IconEye,
    'add_models': IconPlus,
    'configure_models': IconSettings,
    'remove_models': IconTrash,

    // === MODÈLES PAR DÉFAUT ===
    // Configuration des modèles par défaut
    'default_models_config': IconTarget,
    'model_parameters': IconAdjustments,

    // Types de modèles par défaut
    'default_chat_model': IconMessage,
    'default_image_model': IconPhoto,
    'thread_naming_model': IconFileText,
    'search_model': IconSearch,

    // Paramètres des modèles
    'temperature_settings': IconTemperature,
    'context_settings': IconAdjustments,
    'generation_settings': IconBolt,
    'style_settings': IconPalette,
    'token_settings': IconNumbers,
    'timing_settings': IconClock,

    // === RECHERCHE INTERNET ===
    // Configuration principale
    'search_configuration': IconWorldWww,
    'search_security': IconShieldCheck,

    // Fournisseurs de recherche
    'chatbox_search': IconSearch,
    'bing_search': IconBrandBing,
    'tavily_search': IconGlobe,
    'duckduckgo_search': IconFileSearch,
    'brave_search': IconShieldCheck,

    // Configuration de recherche
    'search_providers': IconSettings,
    'search_parameters': IconAdjustments,
    'search_filters': IconFilter,
    'search_language': IconLanguage,

    // Sécurité et confidentialité
    'privacy_settings': IconLockAccess,
    'content_filters': IconFilter,
    'search_limits': IconNumbers,
    'api_key_management': IconKey,

    // === BASE DE CONNAISSANCES ===
    // Gestion des documents
    'document_management': IconBooks,
    'knowledge_security': IconShieldCheck,

    // Actions sur les documents
    'create_documents': IconFolderPlus,
    'edit_documents': IconEdit,
    'delete_documents': IconTrash,
    'organize_collections': IconFolders,

    // Types de documents
    'upload_files': IconFileUpload,
    'text_documents': IconFileText,
    'pdf_documents': IconFile,
    'word_documents': IconFileWord,

    // Recherche et indexation
    'semantic_search': IconSearch,
    'auto_indexing': IconDatabase,
    'search_parameters': IconAdjustments,

    // Sécurité et partage
    'access_control': IconLock,
    'document_sharing': IconShare,
    'category_management': IconCategory,
    'tag_management': IconTags,

    // === PARAMÈTRES DE DISCUSSION ===
    // Interface et affichage
    'chat_interface': IconMessageCircle,
    'chat_appearance': IconPalette,

    // Paramètres d'affichage
    'theme_settings': IconPalette,
    'font_settings': IconTextSize,
    'color_settings': IconPalette,
    'layout_settings': IconSettings,

    // Comportement des conversations
    'conversation_behavior': IconMessageCircle,
    'auto_save': IconDatabase,
    'message_history': IconHistory,
    'notifications': IconBell,

    // Export et sauvegarde
    'export_conversations': IconDownload,
    'import_conversations': IconFolder,
    'backup_settings': IconDatabase,

    // Paramètres avancés
    'advanced_chat': IconSettings,
    'performance_settings': IconBolt,
    'security_chat': IconShieldCheck,
    'privacy_chat': IconLock,

    // === PARAMÈTRES GÉNÉRAUX ===
    // Configuration système
    'system_configuration': IconSettings,
    'security_network': IconShield,

    // Paramètres d'affichage et interface
    'language_settings': IconWorld,
    'theme_configuration': IconPalette,
    'font_configuration': IconTextSize,
    'startup_settings': IconHome,

    // Système et performance
    'auto_launch': IconHome,
    'auto_updates': IconRefresh,
    'beta_updates': IconRefresh,
    'performance_general': IconBolt,

    // Réseau et sécurité
    'proxy_settings': IconNetwork,
    'security_general': IconShield,
    'telemetry_settings': IconReportAnalytics,
    'error_reporting': IconReportAnalytics,

    // Accessibilité et raccourcis
    'accessibility_settings': IconAccessible,
    'keyboard_shortcuts': IconKeyboard,
    'spell_check': IconTextSize,

    // Sauvegarde et données
    'data_backup': IconDatabase,
    'data_export': IconDownload,
    'data_import': IconFolder,

    // === ADMINISTRATION ===
    // Gestion système
    'system_management': IconServer,
    'security_audit': IconShield,

    // Monitoring et surveillance
    'system_monitoring': IconChartBar,
    'performance_monitoring': IconActivity,
    'resource_monitoring': IconCpu,
    'log_monitoring': IconEye,

    // Maintenance et diagnostic
    'system_maintenance': IconTool,
    'system_diagnostic': IconBug,
    'system_logs': IconFileText,
    'error_logs': IconBug,

    // Sécurité et audit
    'audit_trail': IconEye,
    'security_settings': IconShield,
    'access_control_admin': IconLock,
    'session_management': IconUsers,

    // Licence et déploiement
    'license_management': IconCertificate,
    'system_updates': IconCloudUpload,
    'deployment_config': IconServer,
    'backup_restore': IconDatabase,

    // === RACCOURCIS CLAVIER ===
    // Catégories principales
    'navigation_shortcuts': IconArrowRight,
    'editing_shortcuts': IconEdit,

    // Raccourcis de navigation
    'window_shortcuts': IconWindowMaximize,
    'session_shortcuts': IconArrowRight,
    'search_shortcuts': IconSearch,
    'focus_shortcuts': IconTarget,

    // Raccourcis d'édition
    'input_shortcuts': IconEdit,
    'message_shortcuts': IconMessage,
    'formatting_shortcuts': IconTextSize,

    // Raccourcis spécifiques
    'quick_toggle': IconWindowMinimize,
    'new_chat_shortcut': IconPlus,
    'send_message_shortcut': IconPlayerPlay,
    'navigation_arrows': IconArrowUp,

    // === ZONE DE SAISIE ===
    // Catégories principales
    'import_features': IconFolder,
    'creative_tools': IconPalette,

    // Import et fichiers
    'file_import': IconFolder,
    'drive_integration': IconDatabase,
    'code_import': IconCode,
    'link_attachment': IconLink,

    // Outils créatifs
    'canvas_tool': IconEdit,
    'image_generation': IconPhoto,
    'video_features': IconVideo,

    // Recherche et analyse
    'web_browsing': IconWorld,
    'deep_research': IconSearch,

    // Menu principal
    'plus_menu': IconCirclePlus,
    'input_tools': IconHammer
  }

  return iconMap[permissionId as keyof typeof iconMap] || IconChevronRight
}

// Sous-permissions pour chaque module (structure plate pour compatibilité)
const SUB_PERMISSIONS = {
  // === MODULE COPILOTES ===
  copilots: [
    // Catégories principales
    { id: 'categories', name: 'Catégories', description: 'Accès aux catégories de copilotes', isParent: true, icon: 'folder' },
    { id: 'manage_categories', name: 'Gérer les catégories', description: 'Créer, modifier et supprimer des catégories', isParent: true, icon: 'settings' },

    // Sous-catégories spécifiques
    { id: 'category_general', name: 'Général', description: 'Copilotes de catégorie générale', parent: 'categories', level: 1, icon: 'category' },
    { id: 'category_juridique', name: 'Juridique', description: 'Copilotes spécialisés en droit', parent: 'categories', level: 1, icon: 'scale' },
    { id: 'category_marketing', name: 'Marketing', description: 'Copilotes pour le marketing et communication', parent: 'categories', level: 1, icon: 'speakerphone' },
    { id: 'category_finance', name: 'Finance', description: 'Copilotes pour la gestion financière', parent: 'categories', level: 1, icon: 'coin' },

    // Actions de gestion
    { id: 'create_categories', name: 'Créer des catégories', description: 'Créer de nouvelles catégories', parent: 'manage_categories', level: 1, icon: 'plus' },
    { id: 'edit_categories', name: 'Modifier des catégories', description: 'Modifier les catégories existantes', parent: 'manage_categories', level: 1, icon: 'edit' },
    { id: 'delete_categories', name: 'Supprimer des catégories', description: 'Supprimer des catégories', parent: 'manage_categories', level: 1, icon: 'trash' }
  ],

  // === MODULE UTILISATEURS ===
  users: [
    // Gestion principale des utilisateurs
    { id: 'user_management', name: 'Gestion des utilisateurs', description: 'Accès à la gestion des comptes utilisateurs', isParent: true, icon: 'users' },
    { id: 'user_security', name: 'Sécurité et audit', description: 'Gestion de la sécurité et surveillance', isParent: true, icon: 'shield' },

    // Actions CRUD sur les utilisateurs
    { id: 'view_users', name: 'Voir les utilisateurs', description: 'Consulter la liste et les profils utilisateurs', parent: 'user_management', level: 1, icon: 'eye' },
    { id: 'create_users', name: 'Créer des utilisateurs', description: 'Ajouter de nouveaux comptes utilisateurs', parent: 'user_management', level: 1, icon: 'user_plus' },
    { id: 'edit_users', name: 'Modifier les utilisateurs', description: 'Éditer les informations des utilisateurs', parent: 'user_management', level: 1, icon: 'user_edit' },
    { id: 'delete_users', name: 'Supprimer des utilisateurs', description: 'Supprimer des comptes utilisateurs', parent: 'user_management', level: 1, icon: 'user_x' },

    // Gestion des rôles et permissions
    { id: 'manage_roles', name: 'Gérer les rôles', description: 'Créer et modifier les rôles système', parent: 'user_management', level: 1, icon: 'user_cog' },
    { id: 'assign_roles', name: 'Assigner des rôles', description: 'Attribuer des rôles aux utilisateurs', parent: 'user_management', level: 1, icon: 'user_check' },

    // Sécurité et audit
    { id: 'view_audit_logs', name: 'Journaux d\'audit', description: 'Consulter l\'historique des actions', parent: 'user_security', level: 1, icon: 'eye' },
    { id: 'manage_sessions', name: 'Gérer les sessions', description: 'Contrôler les sessions utilisateurs actives', parent: 'user_security', level: 1, icon: 'settings' },
    { id: 'reset_passwords', name: 'Réinitialiser mots de passe', description: 'Réinitialiser les mots de passe utilisateurs', parent: 'user_security', level: 1, icon: 'lock' }
  ],

  // === MODULE FOURNISSEURS DE MODÈLES ===
  model_provider: [
    // Gestion principale des fournisseurs
    { id: 'provider_management', name: 'Gestion des fournisseurs', description: 'Configuration et gestion des fournisseurs d\'IA', isParent: true, icon: 'api' },
    { id: 'model_configuration', name: 'Configuration des modèles', description: 'Gestion des modèles et paramètres', isParent: true, icon: 'brain' },

    // Fournisseurs spécifiques
    { id: 'openai_provider', name: 'OpenAI', description: 'Accès aux modèles GPT-4, GPT-4o, o1, o3', parent: 'provider_management', level: 1, icon: 'openai_provider' },
    { id: 'claude_provider', name: 'Claude (Anthropic)', description: 'Accès aux modèles Claude Opus, Sonnet, Haiku', parent: 'provider_management', level: 1, icon: 'claude_provider' },
    { id: 'gemini_provider', name: 'Google Gemini', description: 'Accès aux modèles Gemini 2.5, 2.0, 1.5 Pro/Flash', parent: 'provider_management', level: 1, icon: 'gemini_provider' },
    { id: 'ollama_provider', name: 'Ollama (Local)', description: 'Modèles locaux via Ollama', parent: 'provider_management', level: 1, icon: 'ollama_provider' },
    { id: 'groq_provider', name: 'Groq', description: 'Modèles Llama 3.2 optimisés', parent: 'provider_management', level: 1, icon: 'groq_provider' },
    { id: 'deepseek_provider', name: 'DeepSeek', description: 'Modèles DeepSeek Chat, Coder, Reasoner', parent: 'provider_management', level: 1, icon: 'deepseek_provider' },
    { id: 'mistral_provider', name: 'Mistral AI', description: 'Modèles Mistral Large, Medium, Small', parent: 'provider_management', level: 1, icon: 'mistral_provider' },
    { id: 'openrouter_provider', name: 'OpenRouter', description: 'Accès à de multiples modèles via OpenRouter', parent: 'provider_management', level: 1, icon: 'openrouter_provider' },
    { id: 'custom_providers', name: 'Fournisseurs personnalisés', description: 'Configuration de fournisseurs personnalisés', parent: 'provider_management', level: 1, icon: 'custom_providers' },

    // Actions de gestion des fournisseurs
    { id: 'configure_providers', name: 'Configurer les fournisseurs', description: 'Modifier les paramètres des fournisseurs', parent: 'provider_management', level: 1, icon: 'configure_providers' },
    { id: 'manage_api_keys', name: 'Gérer les clés API', description: 'Ajouter et modifier les clés d\'authentification', parent: 'provider_management', level: 1, icon: 'manage_api_keys' },
    { id: 'enable_disable_providers', name: 'Activer/Désactiver', description: 'Contrôler l\'état des fournisseurs', parent: 'provider_management', level: 1, icon: 'enable_disable_providers' },

    // Gestion des modèles
    { id: 'view_models', name: 'Voir les modèles', description: 'Consulter la liste des modèles disponibles', parent: 'model_configuration', level: 1, icon: 'view_models' },
    { id: 'add_models', name: 'Ajouter des modèles', description: 'Ajouter de nouveaux modèles aux fournisseurs', parent: 'model_configuration', level: 1, icon: 'add_models' },
    { id: 'configure_models', name: 'Configurer les modèles', description: 'Modifier les paramètres des modèles', parent: 'model_configuration', level: 1, icon: 'configure_models' },
    { id: 'remove_models', name: 'Supprimer des modèles', description: 'Retirer des modèles de la configuration', parent: 'model_configuration', level: 1, icon: 'remove_models' }
  ],

  // === MODULE MODÈLES PAR DÉFAUT ===
  default_models: [
    // Configuration principale des modèles par défaut
    { id: 'default_models_config', name: 'Configuration des modèles', description: 'Sélection des modèles par défaut pour différentes tâches', isParent: true, icon: 'target' },
    { id: 'model_parameters', name: 'Paramètres des modèles', description: 'Configuration des paramètres de génération', isParent: true, icon: 'adjustments' },

    // Types de modèles par défaut
    { id: 'default_chat_model', name: 'Modèle de chat par défaut', description: 'Modèle utilisé pour les conversations générales', parent: 'default_models_config', level: 1, icon: 'default_chat_model' },
    { id: 'default_image_model', name: 'Modèle de génération d\'images', description: 'Modèle pour créer des images (DALL-E, etc.)', parent: 'default_models_config', level: 1, icon: 'default_image_model' },
    { id: 'thread_naming_model', name: 'Modèle de nommage des fils', description: 'Modèle pour générer les titres de conversations', parent: 'default_models_config', level: 1, icon: 'thread_naming_model' },
    { id: 'search_model', name: 'Modèle de recherche', description: 'Modèle pour construire les termes de recherche', parent: 'default_models_config', level: 1, icon: 'search_model' },

    // Paramètres de génération
    { id: 'temperature_settings', name: 'Température', description: 'Contrôle la créativité des réponses (0.0 - 2.0)', parent: 'model_parameters', level: 1, icon: 'temperature_settings' },
    { id: 'context_settings', name: 'Contexte maximum', description: 'Nombre maximum de messages dans le contexte', parent: 'model_parameters', level: 1, icon: 'context_settings' },
    { id: 'generation_settings', name: 'Paramètres de génération', description: 'TopP et autres paramètres avancés', parent: 'model_parameters', level: 1, icon: 'generation_settings' },
    { id: 'style_settings', name: 'Style d\'images', description: 'Style pour la génération d\'images (vivid/natural)', parent: 'model_parameters', level: 1, icon: 'style_settings' },
    { id: 'token_settings', name: 'Gestion des tokens', description: 'Affichage et comptage des tokens', parent: 'model_parameters', level: 1, icon: 'token_settings' },
    { id: 'timing_settings', name: 'Paramètres de timing', description: 'Latence et temps de réponse', parent: 'model_parameters', level: 1, icon: 'timing_settings' }
  ],

  // === MODULE RECHERCHE INTERNET ===
  web_search: [
    // Configuration principale de la recherche
    { id: 'search_configuration', name: 'Configuration de la recherche', description: 'Paramètres généraux de recherche web', isParent: true, icon: 'search_configuration' },
    { id: 'search_security', name: 'Sécurité et confidentialité', description: 'Paramètres de sécurité et protection des données', isParent: true, icon: 'search_security' },

    // Fournisseurs de recherche
    { id: 'chatbox_search', name: 'Chatbox Search', description: 'Moteur de recherche intégré (licence requise)', parent: 'search_configuration', level: 1, icon: 'chatbox_search' },
    { id: 'bing_search', name: 'Bing Search', description: 'Moteur de recherche Microsoft Bing', parent: 'search_configuration', level: 1, icon: 'bing_search' },
    { id: 'tavily_search', name: 'Tavily Search', description: 'API Tavily pour recherche optimisée IA', parent: 'search_configuration', level: 1, icon: 'tavily_search' },
    { id: 'duckduckgo_search', name: 'DuckDuckGo Search', description: 'Moteur de recherche axé sur la confidentialité', parent: 'search_configuration', level: 1, icon: 'duckduckgo_search' },
    { id: 'brave_search', name: 'Brave Search (MCP)', description: 'Intégration Brave Search via MCP', parent: 'search_configuration', level: 1, icon: 'brave_search' },

    // Configuration de recherche
    { id: 'search_providers', name: 'Gérer les fournisseurs', description: 'Activer/désactiver les moteurs de recherche', parent: 'search_configuration', level: 1, icon: 'search_providers' },
    { id: 'search_parameters', name: 'Paramètres de recherche', description: 'Nombre de résultats, profondeur, etc.', parent: 'search_configuration', level: 1, icon: 'search_parameters' },
    { id: 'search_filters', name: 'Filtres de contenu', description: 'Filtres par domaine et type de contenu', parent: 'search_configuration', level: 1, icon: 'search_filters' },
    { id: 'search_language', name: 'Paramètres de langue', description: 'Configuration des langues de recherche', parent: 'search_configuration', level: 1, icon: 'search_language' },

    // Sécurité et confidentialité
    { id: 'privacy_settings', name: 'Paramètres de confidentialité', description: 'Protection des données et anonymisation', parent: 'search_security', level: 1, icon: 'privacy_settings' },
    { id: 'content_filters', name: 'Filtres de sécurité', description: 'Filtrage du contenu inapproprié', parent: 'search_security', level: 1, icon: 'content_filters' },
    { id: 'search_limits', name: 'Limites de recherche', description: 'Quotas et limitations d\'utilisation', parent: 'search_security', level: 1, icon: 'search_limits' },
    { id: 'api_key_management', name: 'Gestion des clés API', description: 'Configuration des clés d\'authentification', parent: 'search_security', level: 1, icon: 'api_key_management' }
  ],

  // === MODULE BASE DE CONNAISSANCES ===
  knowledge_base: [
    // Gestion principale des documents
    { id: 'document_management', name: 'Gestion des documents', description: 'Création, modification et organisation des documents', isParent: true, icon: 'document_management' },
    { id: 'knowledge_security', name: 'Sécurité et accès', description: 'Contrôle d\'accès et partage des connaissances', isParent: true, icon: 'knowledge_security' },

    // Actions sur les documents
    { id: 'create_documents', name: 'Créer des documents', description: 'Ajouter de nouveaux documents et bases de connaissances', parent: 'document_management', level: 1, icon: 'create_documents' },
    { id: 'edit_documents', name: 'Modifier des documents', description: 'Éditer le contenu et les métadonnées', parent: 'document_management', level: 1, icon: 'edit_documents' },
    { id: 'delete_documents', name: 'Supprimer des documents', description: 'Supprimer des documents et collections', parent: 'document_management', level: 1, icon: 'delete_documents' },
    { id: 'organize_collections', name: 'Organiser les collections', description: 'Gérer les catégories et l\'organisation', parent: 'document_management', level: 1, icon: 'organize_collections' },

    // Types de documents supportés
    { id: 'upload_files', name: 'Upload de fichiers', description: 'Télécharger des fichiers (PDF, Word, texte)', parent: 'document_management', level: 1, icon: 'upload_files' },
    { id: 'text_documents', name: 'Documents texte', description: 'Gestion des documents texte brut', parent: 'document_management', level: 1, icon: 'text_documents' },
    { id: 'pdf_documents', name: 'Documents PDF', description: 'Support et indexation des fichiers PDF', parent: 'document_management', level: 1, icon: 'pdf_documents' },
    { id: 'word_documents', name: 'Documents Word', description: 'Support des fichiers Word et Office', parent: 'document_management', level: 1, icon: 'word_documents' },

    // Recherche et indexation
    { id: 'semantic_search', name: 'Recherche sémantique', description: 'Recherche intelligente dans les documents', parent: 'document_management', level: 1, icon: 'semantic_search' },
    { id: 'auto_indexing', name: 'Indexation automatique', description: 'Indexation automatique du contenu', parent: 'document_management', level: 1, icon: 'auto_indexing' },
    { id: 'search_parameters', name: 'Paramètres de recherche', description: 'Configuration des algorithmes de recherche', parent: 'document_management', level: 1, icon: 'search_parameters' },

    // Sécurité et partage
    { id: 'access_control', name: 'Contrôle d\'accès', description: 'Gestion des permissions sur les documents', parent: 'knowledge_security', level: 1, icon: 'access_control' },
    { id: 'document_sharing', name: 'Partage de documents', description: 'Partager des connaissances entre utilisateurs', parent: 'knowledge_security', level: 1, icon: 'document_sharing' },
    { id: 'category_management', name: 'Gestion des catégories', description: 'Créer et gérer les catégories de documents', parent: 'knowledge_security', level: 1, icon: 'category_management' },
    { id: 'tag_management', name: 'Gestion des tags', description: 'Système de tags et métadonnées', parent: 'knowledge_security', level: 1, icon: 'tag_management' }
  ],

  // === MODULE PARAMÈTRES DE DISCUSSION ===
  discussion_settings: [
    // Interface et affichage principal
    { id: 'chat_interface', name: 'Interface et affichage', description: 'Personnalisation de l\'interface de discussion', isParent: true, icon: 'chat_interface' },
    { id: 'conversation_behavior', name: 'Comportement des conversations', description: 'Paramètres de fonctionnement des discussions', isParent: true, icon: 'conversation_behavior' },

    // Paramètres d'affichage et interface
    { id: 'theme_settings', name: 'Thèmes et couleurs', description: 'Personnalisation des thèmes sombre/clair et couleurs', parent: 'chat_interface', level: 1, icon: 'theme_settings' },
    { id: 'font_settings', name: 'Police et taille', description: 'Configuration de la police et taille du texte', parent: 'chat_interface', level: 1, icon: 'font_settings' },
    { id: 'layout_settings', name: 'Disposition de l\'interface', description: 'Organisation des éléments de l\'interface', parent: 'chat_interface', level: 1, icon: 'layout_settings' },
    { id: 'color_settings', name: 'Couleurs personnalisées', description: 'Personnalisation des couleurs de l\'interface', parent: 'chat_interface', level: 1, icon: 'color_settings' },

    // Comportement et fonctionnalités
    { id: 'auto_save', name: 'Sauvegarde automatique', description: 'Sauvegarde automatique des conversations', parent: 'conversation_behavior', level: 1, icon: 'auto_save' },
    { id: 'message_history', name: 'Historique des messages', description: 'Gestion de l\'historique et du contexte', parent: 'conversation_behavior', level: 1, icon: 'message_history' },
    { id: 'notifications', name: 'Notifications', description: 'Alertes et notifications de discussion', parent: 'conversation_behavior', level: 1, icon: 'notifications' },
    { id: 'export_conversations', name: 'Export de conversations', description: 'Exporter les discussions (Markdown, TXT, HTML)', parent: 'conversation_behavior', level: 1, icon: 'export_conversations' },
    { id: 'import_conversations', name: 'Import de conversations', description: 'Importer des discussions existantes', parent: 'conversation_behavior', level: 1, icon: 'import_conversations' },
    { id: 'backup_settings', name: 'Paramètres de sauvegarde', description: 'Configuration des sauvegardes automatiques', parent: 'conversation_behavior', level: 1, icon: 'backup_settings' },

    // Paramètres avancés
    { id: 'advanced_chat', name: 'Paramètres avancés', description: 'Configuration avancée des discussions', parent: 'conversation_behavior', level: 1, icon: 'advanced_chat' },
    { id: 'performance_settings', name: 'Performance', description: 'Optimisation des performances de chat', parent: 'conversation_behavior', level: 1, icon: 'performance_settings' },
    { id: 'security_chat', name: 'Sécurité des discussions', description: 'Paramètres de sécurité et chiffrement', parent: 'conversation_behavior', level: 1, icon: 'security_chat' },
    { id: 'privacy_chat', name: 'Confidentialité', description: 'Protection de la vie privée dans les discussions', parent: 'conversation_behavior', level: 1, icon: 'privacy_chat' }
  ],

  // === MODULE PARAMÈTRES GÉNÉRAUX ===
  general_settings: [
    // Configuration système principale
    { id: 'system_configuration', name: 'Configuration système', description: 'Paramètres généraux de l\'application', isParent: true, icon: 'system_configuration' },
    { id: 'security_network', name: 'Sécurité et réseau', description: 'Paramètres de sécurité et de réseau', isParent: true, icon: 'security_network' },

    // Paramètres d'affichage et interface
    { id: 'language_settings', name: 'Langue et région', description: 'Configuration de la langue de l\'interface', parent: 'system_configuration', level: 1, icon: 'language_settings' },
    { id: 'theme_configuration', name: 'Thème et apparence', description: 'Mode sombre/clair et personnalisation', parent: 'system_configuration', level: 1, icon: 'theme_configuration' },
    { id: 'font_configuration', name: 'Police et taille', description: 'Configuration de la police et taille du texte', parent: 'system_configuration', level: 1, icon: 'font_configuration' },
    { id: 'startup_settings', name: 'Page de démarrage', description: 'Configuration de la page d\'accueil au démarrage', parent: 'system_configuration', level: 1, icon: 'startup_settings' },

    // Système et performance
    { id: 'auto_launch', name: 'Démarrage automatique', description: 'Lancer l\'application au démarrage du système', parent: 'system_configuration', level: 1, icon: 'auto_launch' },
    { id: 'auto_updates', name: 'Mises à jour automatiques', description: 'Vérification automatique des mises à jour', parent: 'system_configuration', level: 1, icon: 'auto_updates' },
    { id: 'beta_updates', name: 'Mises à jour bêta', description: 'Recevoir les versions bêta et préliminaires', parent: 'system_configuration', level: 1, icon: 'beta_updates' },
    { id: 'performance_general', name: 'Performance générale', description: 'Optimisation des performances de l\'application', parent: 'system_configuration', level: 1, icon: 'performance_general' },

    // Réseau et sécurité
    { id: 'proxy_settings', name: 'Paramètres proxy', description: 'Configuration du proxy réseau', parent: 'security_network', level: 1, icon: 'proxy_settings' },
    { id: 'security_general', name: 'Sécurité générale', description: 'Paramètres de sécurité globaux', parent: 'security_network', level: 1, icon: 'security_general' },
    { id: 'telemetry_settings', name: 'Télémétrie', description: 'Collecte de données d\'usage anonymes', parent: 'security_network', level: 1, icon: 'telemetry_settings' },
    { id: 'error_reporting', name: 'Rapport d\'erreurs', description: 'Envoi automatique des rapports d\'erreur', parent: 'security_network', level: 1, icon: 'error_reporting' },

    // Accessibilité et raccourcis
    { id: 'accessibility_settings', name: 'Accessibilité', description: 'Options d\'accessibilité et d\'aide', parent: 'system_configuration', level: 1, icon: 'accessibility_settings' },
    { id: 'keyboard_shortcuts', name: 'Raccourcis clavier', description: 'Configuration des raccourcis clavier', parent: 'system_configuration', level: 1, icon: 'keyboard_shortcuts' },
    { id: 'spell_check', name: 'Vérification orthographique', description: 'Correction automatique de l\'orthographe', parent: 'system_configuration', level: 1, icon: 'spell_check' },

    // Sauvegarde et données
    { id: 'data_backup', name: 'Sauvegarde des données', description: 'Sauvegarde automatique des paramètres', parent: 'security_network', level: 1, icon: 'data_backup' },
    { id: 'data_export', name: 'Export de données', description: 'Exporter les paramètres et données', parent: 'security_network', level: 1, icon: 'data_export' },
    { id: 'data_import', name: 'Import de données', description: 'Importer des paramètres et données', parent: 'security_network', level: 1, icon: 'data_import' }
  ],

  // === MODULE ADMINISTRATION ===
  administration: [
    // Gestion système principale
    { id: 'system_management', name: 'Gestion système', description: 'Administration générale du système', isParent: true, icon: 'system_management' },
    { id: 'security_audit', name: 'Sécurité et audit', description: 'Sécurité avancée et audit des actions', isParent: true, icon: 'security_audit' },

    // Monitoring et surveillance
    { id: 'system_monitoring', name: 'Monitoring système', description: 'Surveillance des performances et ressources', parent: 'system_management', level: 1, icon: 'system_monitoring' },
    { id: 'performance_monitoring', name: 'Monitoring performance', description: 'Surveillance des performances applicatives', parent: 'system_management', level: 1, icon: 'performance_monitoring' },
    { id: 'resource_monitoring', name: 'Monitoring ressources', description: 'Surveillance CPU, mémoire, disque', parent: 'system_management', level: 1, icon: 'resource_monitoring' },
    { id: 'log_monitoring', name: 'Monitoring des logs', description: 'Surveillance et analyse des journaux', parent: 'system_management', level: 1, icon: 'log_monitoring' },

    // Maintenance et diagnostic
    { id: 'system_maintenance', name: 'Maintenance système', description: 'Outils de maintenance et réparation', parent: 'system_management', level: 1, icon: 'system_maintenance' },
    { id: 'system_diagnostic', name: 'Diagnostic système', description: 'Outils de diagnostic et dépannage', parent: 'system_management', level: 1, icon: 'system_diagnostic' },
    { id: 'system_logs', name: 'Journaux système', description: 'Accès aux logs système et application', parent: 'system_management', level: 1, icon: 'system_logs' },
    { id: 'error_logs', name: 'Journaux d\'erreurs', description: 'Consultation des erreurs et exceptions', parent: 'system_management', level: 1, icon: 'error_logs' },

    // Sécurité et audit
    { id: 'audit_trail', name: 'Piste d\'audit', description: 'Historique complet des actions utilisateurs', parent: 'security_audit', level: 1, icon: 'audit_trail' },
    { id: 'security_settings', name: 'Paramètres de sécurité', description: 'Configuration de la sécurité avancée', parent: 'security_audit', level: 1, icon: 'security_settings' },
    { id: 'access_control_admin', name: 'Contrôle d\'accès', description: 'Gestion fine des droits d\'accès', parent: 'security_audit', level: 1, icon: 'access_control_admin' },
    { id: 'session_management', name: 'Gestion des sessions', description: 'Administration des sessions utilisateurs', parent: 'security_audit', level: 1, icon: 'session_management' },

    // Licence et déploiement
    { id: 'license_management', name: 'Gestion des licences', description: 'Administration des licences et activations', parent: 'system_management', level: 1, icon: 'license_management' },
    { id: 'system_updates', name: 'Mises à jour système', description: 'Gestion des mises à jour et déploiements', parent: 'system_management', level: 1, icon: 'system_updates' },
    { id: 'deployment_config', name: 'Configuration déploiement', description: 'Paramètres de déploiement et environnement', parent: 'system_management', level: 1, icon: 'deployment_config' },
    { id: 'backup_restore', name: 'Sauvegarde et restauration', description: 'Outils de sauvegarde et restauration système', parent: 'system_management', level: 1, icon: 'backup_restore' }
  ],

  // === MODULE RACCOURCIS CLAVIER ===
  keyboard_shortcuts: [
    // Catégories principales de raccourcis
    { id: 'navigation_shortcuts', name: 'Raccourcis de navigation', description: 'Raccourcis pour naviguer dans l\'application', isParent: true, icon: 'navigation_shortcuts' },
    { id: 'editing_shortcuts', name: 'Raccourcis d\'édition', description: 'Raccourcis pour l\'édition et la saisie', isParent: true, icon: 'editing_shortcuts' },

    // Raccourcis de navigation
    { id: 'window_shortcuts', name: 'Gestion des fenêtres', description: 'Raccourcis pour gérer les fenêtres et l\'affichage', parent: 'navigation_shortcuts', level: 1, icon: 'window_shortcuts' },
    { id: 'session_shortcuts', name: 'Navigation des sessions', description: 'Raccourcis pour naviguer entre les conversations', parent: 'navigation_shortcuts', level: 1, icon: 'session_shortcuts' },
    { id: 'search_shortcuts', name: 'Raccourcis de recherche', description: 'Raccourcis pour ouvrir et utiliser la recherche', parent: 'navigation_shortcuts', level: 1, icon: 'search_shortcuts' },
    { id: 'focus_shortcuts', name: 'Raccourcis de focus', description: 'Raccourcis pour déplacer le focus dans l\'interface', parent: 'navigation_shortcuts', level: 1, icon: 'focus_shortcuts' },

    // Raccourcis d'édition
    { id: 'input_shortcuts', name: 'Saisie et édition', description: 'Raccourcis pour la saisie de texte et l\'édition', parent: 'editing_shortcuts', level: 1, icon: 'input_shortcuts' },
    { id: 'message_shortcuts', name: 'Gestion des messages', description: 'Raccourcis pour envoyer et gérer les messages', parent: 'editing_shortcuts', level: 1, icon: 'message_shortcuts' },
    { id: 'formatting_shortcuts', name: 'Formatage du texte', description: 'Raccourcis pour le formatage et la mise en forme', parent: 'editing_shortcuts', level: 1, icon: 'formatting_shortcuts' },

    // Raccourcis spécifiques détaillés
    { id: 'quick_toggle', name: 'Basculement rapide', description: 'Afficher/masquer la fenêtre de l\'application (Alt+`)', parent: 'window_shortcuts', level: 2, icon: 'quick_toggle' },
    { id: 'new_chat_shortcut', name: 'Nouvelle conversation', description: 'Créer une nouvelle conversation (Cmd/Ctrl+N)', parent: 'session_shortcuts', level: 2, icon: 'new_chat_shortcut' },
    { id: 'send_message_shortcut', name: 'Envoyer un message', description: 'Envoyer le message en cours (Entrée)', parent: 'message_shortcuts', level: 2, icon: 'send_message_shortcut' },
    { id: 'navigation_arrows', name: 'Navigation par flèches', description: 'Navigation avec les flèches dans les options', parent: 'search_shortcuts', level: 2, icon: 'navigation_arrows' }
  ],

  // === MODULE ZONE DE SAISIE ===
  input_zone: [
    // Catégories principales
    { id: 'import_features', name: 'Import et fichiers', description: 'Fonctionnalités d\'import et de gestion de fichiers', isParent: true, icon: 'import_features' },
    { id: 'creative_tools', name: 'Outils créatifs', description: 'Outils de création et de génération de contenu', isParent: true, icon: 'creative_tools' },

    // Import et fichiers
    { id: 'file_import', name: 'Import de fichiers', description: 'Importer des fichiers locaux (documents, PDF, etc.)', parent: 'import_features', level: 1, icon: 'file_import' },
    { id: 'drive_integration', name: 'Intégration Drive', description: 'Ajouter des fichiers depuis Google Drive, OneDrive', parent: 'import_features', level: 1, icon: 'drive_integration' },
    { id: 'code_import', name: 'Import de code', description: 'Importer du code source et des snippets', parent: 'import_features', level: 1, icon: 'code_import' },
    { id: 'link_attachment', name: 'Ajout de liens', description: 'Attacher des liens web et des URLs', parent: 'import_features', level: 1, icon: 'link_attachment' },

    // Outils créatifs
    { id: 'canvas_tool', name: 'Canvas', description: 'Outil de création et d\'édition graphique', parent: 'creative_tools', level: 1, icon: 'canvas_tool' },
    { id: 'image_generation', name: 'Génération d\'images', description: 'Créer et importer des images', parent: 'creative_tools', level: 1, icon: 'image_generation' },
    { id: 'video_features', name: 'Fonctionnalités vidéo', description: 'Traitement et analyse de contenu vidéo', parent: 'creative_tools', level: 1, icon: 'video_features' },

    // Recherche et analyse
    { id: 'web_browsing', name: 'Navigation web', description: 'Mode de navigation et recherche web intégrée', parent: 'creative_tools', level: 1, icon: 'web_browsing' },
    { id: 'deep_research', name: 'Recherche approfondie', description: 'Outils de recherche et d\'analyse avancés', parent: 'creative_tools', level: 1, icon: 'deep_research' },

    // Contrôles du menu
    { id: 'plus_menu', name: 'Menu principal (+)', description: 'Accès au menu principal de la zone de saisie', parent: 'import_features', level: 1, icon: 'plus_menu' },
    { id: 'input_tools', name: 'Outils de saisie', description: 'Outils et fonctionnalités avancées de saisie', parent: 'creative_tools', level: 1, icon: 'input_tools' }
  ]
}

// Permissions par rôle (exemple de configuration)
const ROLE_PERMISSIONS = {
  admin: ['users', 'copilots', 'model_provider', 'default_models', 'web_search', 'knowledge_base', 'discussion_settings', 'keyboard_shortcuts', 'general_settings', 'administration', 'input_zone'],
  moderator: ['users', 'copilots', 'default_models', 'web_search', 'knowledge_base', 'discussion_settings', 'keyboard_shortcuts', 'general_settings', 'administration', 'input_zone'],
  user: ['copilots', 'web_search', 'knowledge_base', 'discussion_settings', 'keyboard_shortcuts', 'general_settings', 'input_zone'],
  guest: ['web_search', 'keyboard_shortcuts', 'input_zone']
}

// Sous-permissions par rôle pour les modules
const ROLE_SUB_PERMISSIONS = {
  admin: {
    // === COPILOTES ===
    copilots: [
      // Accès complet à toutes les catégories
      'categories', 'category_general', 'category_juridique', 'category_marketing', 'category_finance',
      // Gestion complète des catégories
      'manage_categories', 'create_categories', 'edit_categories', 'delete_categories'
    ],
    // === UTILISATEURS ===
    users: [
      // Gestion complète des utilisateurs
      'user_management', 'view_users', 'create_users', 'edit_users', 'delete_users',
      'manage_roles', 'assign_roles',
      // Sécurité et audit complets
      'user_security', 'view_audit_logs', 'manage_sessions', 'reset_passwords'
    ],
    // === FOURNISSEURS DE MODÈLES ===
    model_provider: [
      // Gestion complète des fournisseurs
      'provider_management', 'openai_provider', 'claude_provider', 'gemini_provider',
      'ollama_provider', 'groq_provider', 'deepseek_provider', 'mistral_provider',
      'openrouter_provider', 'custom_providers',
      'configure_providers', 'manage_api_keys', 'enable_disable_providers',
      // Configuration complète des modèles
      'model_configuration', 'view_models', 'add_models', 'configure_models', 'remove_models'
    ],
    // === MODÈLES PAR DÉFAUT ===
    default_models: [
      // Configuration complète des modèles par défaut
      'default_models_config', 'default_chat_model', 'default_image_model',
      'thread_naming_model', 'search_model',
      // Paramètres complets
      'model_parameters', 'temperature_settings', 'context_settings',
      'generation_settings', 'style_settings', 'token_settings', 'timing_settings'
    ],
    // === RECHERCHE INTERNET ===
    web_search: [
      // Configuration complète de la recherche
      'search_configuration', 'chatbox_search', 'bing_search', 'tavily_search',
      'duckduckgo_search', 'brave_search',
      'search_providers', 'search_parameters', 'search_filters', 'search_language',
      // Sécurité complète
      'search_security', 'privacy_settings', 'content_filters', 'search_limits', 'api_key_management'
    ],
    // === BASE DE CONNAISSANCES ===
    knowledge_base: [
      // Gestion complète des documents
      'document_management', 'create_documents', 'edit_documents', 'delete_documents', 'organize_collections',
      'upload_files', 'text_documents', 'pdf_documents', 'word_documents',
      'semantic_search', 'auto_indexing', 'search_parameters',
      // Sécurité complète
      'knowledge_security', 'access_control', 'document_sharing', 'category_management', 'tag_management'
    ],
    // === PARAMÈTRES DE DISCUSSION ===
    discussion_settings: [
      // Interface complète
      'chat_interface', 'theme_settings', 'font_settings', 'layout_settings', 'color_settings',
      // Comportement complet
      'conversation_behavior', 'auto_save', 'message_history', 'notifications',
      'export_conversations', 'import_conversations', 'backup_settings',
      // Paramètres avancés complets
      'advanced_chat', 'performance_settings', 'security_chat', 'privacy_chat'
    ],
    // === PARAMÈTRES GÉNÉRAUX ===
    general_settings: [
      // Configuration système complète
      'system_configuration', 'language_settings', 'theme_configuration', 'font_configuration', 'startup_settings',
      'auto_launch', 'auto_updates', 'beta_updates', 'performance_general',
      'accessibility_settings', 'keyboard_shortcuts', 'spell_check',
      // Sécurité et réseau complets
      'security_network', 'proxy_settings', 'security_general', 'telemetry_settings', 'error_reporting',
      'data_backup', 'data_export', 'data_import'
    ],
    // === ADMINISTRATION ===
    administration: [
      // Gestion système complète
      'system_management', 'system_monitoring', 'performance_monitoring', 'resource_monitoring', 'log_monitoring',
      'system_maintenance', 'system_diagnostic', 'system_logs', 'error_logs',
      'license_management', 'system_updates', 'deployment_config', 'backup_restore',
      // Sécurité et audit complets
      'security_audit', 'audit_trail', 'security_settings', 'access_control_admin', 'session_management'
    ],
    // === RACCOURCIS CLAVIER ===
    keyboard_shortcuts: [
      // Navigation complète
      'navigation_shortcuts', 'window_shortcuts', 'session_shortcuts', 'search_shortcuts', 'focus_shortcuts',
      'quick_toggle', 'new_chat_shortcut', 'navigation_arrows',
      // Édition complète
      'editing_shortcuts', 'input_shortcuts', 'message_shortcuts', 'formatting_shortcuts',
      'send_message_shortcut'
    ],
    // === ZONE DE SAISIE ===
    input_zone: [
      // Import complet
      'import_features', 'file_import', 'drive_integration', 'code_import', 'link_attachment', 'plus_menu',
      // Outils créatifs complets
      'creative_tools', 'canvas_tool', 'image_generation', 'video_features', 'web_browsing', 'deep_research', 'input_tools'
    ]
  },
  moderator: {
    // === COPILOTES ===
    copilots: [
      // Accès à toutes les catégories
      'categories', 'category_general', 'category_juridique', 'category_marketing', 'category_finance',
      // Gestion limitée (pas de suppression)
      'manage_categories', 'create_categories', 'edit_categories'
    ],
    // === UTILISATEURS ===
    users: [
      // Gestion limitée des utilisateurs (pas de suppression)
      'user_management', 'view_users', 'create_users', 'edit_users', 'assign_roles',
      // Sécurité limitée (pas de gestion des sessions)
      'user_security', 'view_audit_logs', 'reset_passwords'
    ],
    // === FOURNISSEURS DE MODÈLES ===
    model_provider: [
      // Accès aux fournisseurs principaux (pas de fournisseurs personnalisés)
      'provider_management', 'openai_provider', 'claude_provider', 'gemini_provider',
      'ollama_provider', 'groq_provider', 'deepseek_provider',
      'configure_providers', 'manage_api_keys', 'enable_disable_providers',
      // Configuration limitée des modèles (pas de suppression)
      'model_configuration', 'view_models', 'add_models', 'configure_models'
    ],
    // === MODÈLES PAR DÉFAUT ===
    default_models: [
      // Configuration limitée des modèles par défaut
      'default_models_config', 'default_chat_model', 'default_image_model', 'thread_naming_model',
      // Paramètres de base (pas de paramètres avancés)
      'model_parameters', 'temperature_settings', 'context_settings', 'style_settings'
    ],
    // === RECHERCHE INTERNET ===
    web_search: [
      // Accès aux moteurs principaux (pas de fournisseurs avancés)
      'search_configuration', 'chatbox_search', 'bing_search', 'tavily_search',
      'search_providers', 'search_parameters', 'search_filters',
      // Sécurité limitée (pas de gestion des clés API)
      'search_security', 'privacy_settings', 'content_filters', 'search_limits'
    ],
    // === BASE DE CONNAISSANCES ===
    knowledge_base: [
      // Gestion limitée des documents (pas de suppression)
      'document_management', 'create_documents', 'edit_documents', 'organize_collections',
      'upload_files', 'text_documents', 'pdf_documents', 'word_documents',
      'semantic_search', 'auto_indexing',
      // Sécurité limitée (pas de contrôle d'accès avancé)
      'knowledge_security', 'document_sharing', 'category_management', 'tag_management'
    ],
    // === PARAMÈTRES DE DISCUSSION ===
    discussion_settings: [
      // Interface limitée (pas de couleurs personnalisées)
      'chat_interface', 'theme_settings', 'font_settings', 'layout_settings',
      // Comportement limité (pas de paramètres avancés)
      'conversation_behavior', 'auto_save', 'message_history', 'notifications',
      'export_conversations', 'import_conversations', 'backup_settings'
    ],
    // === PARAMÈTRES GÉNÉRAUX ===
    general_settings: [
      // Configuration système limitée (pas de mises à jour bêta)
      'system_configuration', 'language_settings', 'theme_configuration', 'font_configuration', 'startup_settings',
      'auto_launch', 'auto_updates', 'accessibility_settings', 'keyboard_shortcuts', 'spell_check',
      // Sécurité limitée (pas de paramètres avancés)
      'security_network', 'telemetry_settings', 'error_reporting', 'data_export', 'data_import'
    ],
    // === ADMINISTRATION ===
    administration: [
      // Monitoring limité (pas de maintenance système)
      'system_management', 'system_monitoring', 'performance_monitoring', 'log_monitoring',
      'system_logs', 'error_logs',
      // Audit limité (pas de contrôle d'accès avancé)
      'security_audit', 'audit_trail', 'session_management'
    ],
    // === RACCOURCIS CLAVIER ===
    keyboard_shortcuts: [
      // Navigation complète
      'navigation_shortcuts', 'window_shortcuts', 'session_shortcuts', 'search_shortcuts', 'focus_shortcuts',
      'quick_toggle', 'new_chat_shortcut', 'navigation_arrows',
      // Édition complète
      'editing_shortcuts', 'input_shortcuts', 'message_shortcuts', 'formatting_shortcuts',
      'send_message_shortcut'
    ],
    // === ZONE DE SAISIE ===
    input_zone: [
      // Import complet
      'import_features', 'file_import', 'drive_integration', 'code_import', 'link_attachment', 'plus_menu',
      // Outils créatifs complets
      'creative_tools', 'canvas_tool', 'image_generation', 'video_features', 'web_browsing', 'deep_research', 'input_tools'
    ]
  },
  user: {
    // === COPILOTES ===
    copilots: [
      // Accès en lecture aux catégories spécifiques selon le profil
      'categories', 'category_general', 'category_marketing'
    ],
    // === UTILISATEURS ===
    users: [
      // Accès très limité (consultation uniquement)
      'user_management', 'view_users'
    ],
    // === FOURNISSEURS DE MODÈLES ===
    model_provider: [
      // Accès en lecture seule aux fournisseurs de base
      'provider_management', 'openai_provider', 'claude_provider', 'gemini_provider',
      // Consultation des modèles uniquement
      'model_configuration', 'view_models'
    ],
    // === MODÈLES PAR DÉFAUT ===
    default_models: [
      // Accès en lecture seule aux modèles par défaut
      'default_models_config', 'default_chat_model', 'default_image_model',
      // Consultation des paramètres uniquement
      'model_parameters', 'temperature_settings', 'context_settings'
    ],
    // === RECHERCHE INTERNET ===
    web_search: [
      // Accès en lecture seule aux moteurs de base
      'search_configuration', 'chatbox_search', 'bing_search',
      // Consultation des paramètres uniquement
      'search_security', 'privacy_settings'
    ],
    // === BASE DE CONNAISSANCES ===
    knowledge_base: [
      // Accès en lecture seule aux documents
      'document_management', 'text_documents', 'pdf_documents',
      'semantic_search',
      // Consultation des paramètres uniquement
      'knowledge_security', 'category_management'
    ],
    // === PARAMÈTRES DE DISCUSSION ===
    discussion_settings: [
      // Interface de base uniquement
      'chat_interface', 'theme_settings', 'font_settings',
      // Comportement de base
      'conversation_behavior', 'message_history', 'export_conversations'
    ],
    // === PARAMÈTRES GÉNÉRAUX ===
    general_settings: [
      // Configuration de base uniquement
      'system_configuration', 'language_settings', 'theme_configuration', 'font_configuration',
      // Sécurité de base
      'security_network', 'telemetry_settings'
    ],
    // === ADMINISTRATION ===
    administration: [
      // Aucun accès à l'administration pour les utilisateurs normaux
    ],
    // === RACCOURCIS CLAVIER ===
    keyboard_shortcuts: [
      // Navigation de base
      'navigation_shortcuts', 'window_shortcuts', 'session_shortcuts', 'search_shortcuts',
      'quick_toggle', 'new_chat_shortcut', 'navigation_arrows',
      // Édition de base
      'editing_shortcuts', 'input_shortcuts', 'message_shortcuts',
      'send_message_shortcut'
    ],
    // === ZONE DE SAISIE ===
    input_zone: [
      // Import de base
      'import_features', 'file_import', 'link_attachment', 'plus_menu',
      // Outils créatifs de base
      'creative_tools', 'image_generation', 'web_browsing', 'input_tools'
    ]
  },
  guest: {
    // === COPILOTES ===
    copilots: [
      // Accès limité à la catégorie générale uniquement
      'categories', 'category_general'
    ],
    // === UTILISATEURS ===
    users: [
      // Aucun accès aux utilisateurs pour les invités
    ],
    // === FOURNISSEURS DE MODÈLES ===
    model_provider: [
      // Aucun accès aux fournisseurs pour les invités
    ],
    // === MODÈLES PAR DÉFAUT ===
    default_models: [
      // Aucun accès aux modèles par défaut pour les invités
    ],
    // === RECHERCHE INTERNET ===
    web_search: [
      // Aucun accès à la recherche Internet pour les invités
    ],
    // === BASE DE CONNAISSANCES ===
    knowledge_base: [
      // Aucun accès à la base de connaissances pour les invités
    ],
    // === PARAMÈTRES DE DISCUSSION ===
    discussion_settings: [
      // Aucun accès aux paramètres de discussion pour les invités
    ],
    // === PARAMÈTRES GÉNÉRAUX ===
    general_settings: [
      // Aucun accès aux paramètres généraux pour les invités
    ],
    // === ADMINISTRATION ===
    administration: [
      // Aucun accès à l'administration pour les invités
    ],
    // === RACCOURCIS CLAVIER ===
    keyboard_shortcuts: [
      // Raccourcis de base uniquement
      'navigation_shortcuts', 'window_shortcuts', 'session_shortcuts',
      'quick_toggle', 'new_chat_shortcut'
    ],
    // === ZONE DE SAISIE ===
    input_zone: [
      // Import très limité
      'import_features', 'file_import', 'plus_menu',
      // Outils de base uniquement
      'creative_tools', 'image_generation'
    ]
  }
}

function RouteComponent() {
  const { t } = useTranslation()
  const [selectedRole, setSelectedRole] = useState<string>('moderator')

  // Utiliser le service centralisé des rôles
  const { roles, getRolesForSelect, addRole, updateRole, deleteRole } = useRoles()

  // État pour les modals et formulaires
  const [addModalOpened, setAddModalOpened] = useState(false)
  const [editingRole, setEditingRole] = useState<string | null>(null)
  const [newRoleName, setNewRoleName] = useState('')
  const [newRoleDisplayName, setNewRoleDisplayName] = useState('')
  const [deleteModalOpened, setDeleteModalOpened] = useState(false)
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null)

  // État pour la vue de permissions détaillées
  const [permissionDetailOpened, setPermissionDetailOpened] = useState(false)
  const [selectedPermission, setSelectedPermission] = useState<string | null>(null)
  const [tempSubPermissions, setTempSubPermissions] = useState<string[]>([])
  const [tempPermissionEnabled, setTempPermissionEnabled] = useState(true)

  // Obtenir les rôles modifiables (exclure admin)
  const getModifiableRoles = () => {
    return roles.filter(role => role.name !== 'admin')
  }

  // Obtenir les rôles modifiables pour le sélecteur
  const getModifiableRolesForSelect = () => {
    return getModifiableRoles().map(role => ({
      value: role.name,
      label: role.displayName
    }))
  }

  // S'assurer que le rôle sélectionné existe toujours et n'est pas admin
  useEffect(() => {
    const modifiableRoles = getModifiableRoles()
    if (modifiableRoles.length > 0 && (!modifiableRoles.find(role => role.name === selectedRole) || selectedRole === 'admin')) {
      setSelectedRole(modifiableRoles[0].name)
    }
  }, [roles, selectedRole])

  // Obtenir les permissions pour un rôle donné
  const getRolePermissions = (roleName: string): string[] => {
    // Charger les permissions personnalisées ou utiliser les permissions par défaut
    const saved = loadRolePermissions(roleName)
    if (saved && saved.permissions) {
      return saved.permissions
    }
    return ROLE_PERMISSIONS[roleName as keyof typeof ROLE_PERMISSIONS] || []
  }

  // Vérifier si une permission est active pour le rôle sélectionné
  const isPermissionActive = (permissionId: string): boolean => {
    const rolePermissions = getRolePermissions(selectedRole)
    return rolePermissions.includes(permissionId)
  }

  // Obtenir les sous-permissions pour un rôle et un module
  const getRoleSubPermissions = (roleName: string, moduleId: string): string[] => {
    // Charger les sous-permissions personnalisées ou utiliser les sous-permissions par défaut
    const saved = loadRolePermissions(roleName)
    if (saved && saved.subPermissions && saved.subPermissions[moduleId]) {
      return saved.subPermissions[moduleId]
    }
    return ROLE_SUB_PERMISSIONS[roleName as keyof typeof ROLE_SUB_PERMISSIONS]?.[moduleId as keyof typeof ROLE_SUB_PERMISSIONS.admin] || []
  }

  // Ouvrir la vue de permissions détaillées
  const openPermissionDetail = (permissionId: string) => {
    setSelectedPermission(permissionId)
    setTempPermissionEnabled(isPermissionActive(permissionId))
    setTempSubPermissions(getRoleSubPermissions(selectedRole, permissionId))
    setPermissionDetailOpened(true)
  }

  // Fermer la vue sans sauvegarder
  const closePermissionDetail = () => {
    setPermissionDetailOpened(false)
    setSelectedPermission(null)
    setTempSubPermissions([])
    setTempPermissionEnabled(true)
  }

  // Sauvegarder les permissions dans localStorage
  const saveRolePermissions = (roleName: string, permissions: string[], subPermissions: Record<string, string[]>) => {
    try {
      const key = `datatec_role_permissions_${roleName}`
      const data = {
        permissions,
        subPermissions,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des permissions:', error)
    }
  }

  // Charger les permissions depuis localStorage
  const loadRolePermissions = (roleName: string) => {
    try {
      const key = `datatec_role_permissions_${roleName}`
      const saved = localStorage.getItem(key)
      if (saved) {
        return JSON.parse(saved)
      }
    } catch (error) {
      console.error('Erreur lors du chargement des permissions:', error)
    }
    return null
  }

  // Sauvegarder les permissions détaillées
  const savePermissionDetails = () => {
    if (!selectedPermission) return

    // Mettre à jour les permissions principales
    let newPermissions = [...getRolePermissions(selectedRole)]
    if (tempPermissionEnabled && !newPermissions.includes(selectedPermission)) {
      newPermissions.push(selectedPermission)
    } else if (!tempPermissionEnabled && newPermissions.includes(selectedPermission)) {
      newPermissions = newPermissions.filter(p => p !== selectedPermission)
    }

    // Mettre à jour les sous-permissions
    const currentSubPerms = loadRolePermissions(selectedRole)?.subPermissions || {}
    const newSubPermissions = {
      ...currentSubPerms,
      [selectedPermission]: tempPermissionEnabled ? tempSubPermissions : []
    }

    // Sauvegarder
    saveRolePermissions(selectedRole, newPermissions, newSubPermissions)

    notifications.show({
      title: 'Succès',
      message: 'Permissions mises à jour avec succès',
      color: 'green',
      icon: <IconCheck size={16} />
    })

    closePermissionDetail()
  }

  // Composant d'étiquette de permission
  const PermissionTag = ({ permission }: { permission: typeof DEFAULT_PERMISSIONS[0] }) => {
    const isActive = isPermissionActive(permission.id)
    const hasSubPermissions = SUB_PERMISSIONS[permission.id as keyof typeof SUB_PERMISSIONS]

    return (
      <Card
        withBorder
        padding="md"
        radius="md"
        style={{
          backgroundColor: 'var(--mantine-color-dark-7)',
          borderColor: 'var(--mantine-color-dark-5)',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
        onClick={() => {
          if (hasSubPermissions) {
            openPermissionDetail(permission.id)
          }
        }}
      >
        <Group justify="space-between" align="center">
          <Group gap="sm">
            <Avatar
              size={40}
              radius="sm"
              color={permission.color}
              style={{
                backgroundColor: `var(--mantine-color-${permission.color}-6)`
              }}
            >
              {permission.icon}
            </Avatar>
            <div>
              <Text fw={600} size="sm" c="white">
                {permission.name}
              </Text>
            </div>
          </Group>

          {/* Indicateur d'état */}
          <div
            style={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: isActive ? '#4CAF50' : '#f44336',
              marginTop: 4,
            }}
            title={isActive ? 'Actif' : 'Inactif'}
          />
        </Group>
      </Card>
    )
  }

  // Vue de permissions détaillées
  const PermissionDetailView = () => {
    if (!selectedPermission) return null

    const permission = DEFAULT_PERMISSIONS.find(p => p.id === selectedPermission)
    const subPermissions = SUB_PERMISSIONS[selectedPermission as keyof typeof SUB_PERMISSIONS] || []

    // Obtenir les enfants d'une permission (générique pour tous les modules)
    const getChildPermissions = (parentId: string): string[] => {
      const currentModulePerms = SUB_PERMISSIONS[selectedPermission as keyof typeof SUB_PERMISSIONS] || []
      return currentModulePerms
        .filter(perm => perm.parent === parentId)
        .map(perm => perm.id)
    }

    // Obtenir le parent d'une permission (générique pour tous les modules)
    const getParentPermission = (childId: string): string | null => {
      const currentModulePerms = SUB_PERMISSIONS[selectedPermission as keyof typeof SUB_PERMISSIONS] || []
      const childPerm = currentModulePerms.find(perm => perm.id === childId)
      return childPerm?.parent || null
    }

    // Vérifier si tous les enfants sont sélectionnés
    const areAllChildrenSelected = (parentId: string): boolean => {
      const children = getChildPermissions(parentId)
      return children.length > 0 && children.every(childId => tempSubPermissions.includes(childId))
    }

    const handleSubPermissionChange = (subPermissionId: string, checked: boolean) => {
      // Obtenir les permissions du module actuel (générique)
      const currentModulePerms = SUB_PERMISSIONS[selectedPermission as keyof typeof SUB_PERMISSIONS] || []
      const permission = currentModulePerms.find(perm => perm.id === subPermissionId)

      if (!permission) return

      let newSubPermissions = [...tempSubPermissions]

      if (permission.isParent) {
        // Si c'est un parent, gérer tous les enfants
        const children = getChildPermissions(subPermissionId)

        if (checked) {
          // Cocher le parent et tous ses enfants
          if (!newSubPermissions.includes(subPermissionId)) {
            newSubPermissions.push(subPermissionId)
          }
          children.forEach(childId => {
            if (!newSubPermissions.includes(childId)) {
              newSubPermissions.push(childId)
            }
          })
        } else {
          // Décocher le parent et tous ses enfants
          newSubPermissions = newSubPermissions.filter(id =>
            id !== subPermissionId && !children.includes(id)
          )
        }
      } else {
        // Si c'est un enfant
        if (checked) {
          // Cocher l'enfant
          if (!newSubPermissions.includes(subPermissionId)) {
            newSubPermissions.push(subPermissionId)
          }

          // Vérifier si tous les enfants du parent sont maintenant cochés
          const parentId = getParentPermission(subPermissionId)
          if (parentId) {
            const siblings = getChildPermissions(parentId)
            const allSiblingsSelected = siblings.every(siblingId =>
              newSubPermissions.includes(siblingId)
            )
            if (allSiblingsSelected && !newSubPermissions.includes(parentId)) {
              newSubPermissions.push(parentId)
            }
          }
        } else {
          // Décocher l'enfant
          newSubPermissions = newSubPermissions.filter(id => id !== subPermissionId)

          // Décocher automatiquement le parent si nécessaire
          const parentId = getParentPermission(subPermissionId)
          if (parentId && newSubPermissions.includes(parentId)) {
            newSubPermissions = newSubPermissions.filter(id => id !== parentId)
          }
        }
      }

      setTempSubPermissions(newSubPermissions)
    }

    const handleSave = () => {
      savePermissionDetails()
    }

    const handleCancel = () => {
      closePermissionDetail()
    }

    return (
      <Box h="100%" px="md" py="sm">
        <Stack gap="md" h="100%">
          {/* Header avec breadcrumb navigation style Odoo */}
          <Box>
            <Group justify="space-between" align="center" mb="sm">
              <Breadcrumbs
                separator={<Text size="xs" c="dimmed">/</Text>}
                separatorMargin={4}
                styles={{
                  root: {
                    fontSize: '0.875rem'
                  }
                }}
              >
                <Anchor
                  size="sm"
                  c="blue.4"
                  onClick={handleCancel}
                  style={{
                    cursor: 'pointer',
                    textDecoration: 'none',
                    fontSize: '0.875rem'
                  }}
                  __hover={{
                    textDecoration: 'underline'
                  }}
                >
                  Droits d'accès
                </Anchor>
                <Anchor
                  size="sm"
                  c="blue.4"
                  onClick={handleCancel}
                  style={{
                    cursor: 'pointer',
                    textDecoration: 'none',
                    fontSize: '0.875rem'
                  }}
                  __hover={{
                    textDecoration: 'underline'
                  }}
                >
                  {selectedRole}
                </Anchor>
                <Text size="sm" fw={500} c="white">
                  {permission?.name}
                </Text>
              </Breadcrumbs>

              {/* Actions contextuelles style Odoo */}
              <Group gap="xs">
                <Button
                  variant="default"
                  size="xs"
                  leftSection={<IconPrinter size={12} />}
                  styles={{
                    root: {
                      border: '1px solid var(--mantine-color-dark-4)',
                      backgroundColor: 'var(--mantine-color-dark-6)',
                      fontSize: '0.75rem'
                    }
                  }}
                >
                  Imprimer
                </Button>

                <Menu shadow="md" width={200}>
                  <Menu.Target>
                    <Button
                      variant="default"
                      size="xs"
                      rightSection={<IconChevronRight size={10} />}
                      styles={{
                        root: {
                          border: '1px solid var(--mantine-color-dark-4)',
                          backgroundColor: 'var(--mantine-color-dark-6)',
                          fontSize: '0.75rem'
                        }
                      }}
                    >
                      Action
                    </Button>
                  </Menu.Target>
                  <Menu.Dropdown>
                    <Menu.Item
                      leftSection={<IconSettings size={14} />}
                    >
                      Configurer les permissions
                    </Menu.Item>
                    <Menu.Item
                      leftSection={<IconCopy size={14} />}
                    >
                      Dupliquer vers un autre rôle
                    </Menu.Item>
                    <Menu.Divider />
                    <Menu.Item
                      leftSection={<IconDownload size={14} />}
                    >
                      Exporter la configuration
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>

                <Group gap={4}>
                  <Text size="xs" c="dimmed">
                    1/1
                  </Text>
                  <ActionIcon variant="subtle" size="xs" color="gray">
                    <IconChevronRight size={10} />
                  </ActionIcon>
                </Group>

                <Button
                  size="xs"
                  variant="filled"
                  color="blue"
                  styles={{
                    root: {
                      fontSize: '0.75rem'
                    }
                  }}
                  onClick={(event) => {
                    event.preventDefault()
                    setTempPermissionEnabled(!tempPermissionEnabled)
                  }}
                >
                  {tempPermissionEnabled ? 'Désactiver' : 'Activer'}
                </Button>
              </Group>
            </Group>
          </Box>

          <Divider mx={0} />

          {/* Formulaire style Odoo - Aligné avec le header */}
          <Box flex={1} className="overflow-auto">
            <Stack gap="md" px={0} py="sm">

              {/* Permissions détaillées - Style Odoo */}
              {tempPermissionEnabled && subPermissions.length > 0 && (
                <Paper p="md" radius="md" withBorder>
                  <Text size="sm" fw={400} c="dimmed" mb="md">
                    Permissions détaillées :
                  </Text>
                  <Stack gap="sm">
                    {subPermissions.map((subPerm) => {
                      const isParent = subPerm.isParent
                      const level = subPerm.level || 0
                      const isIndented = level > 0

                      return (
                        <Box
                          key={subPerm.id}
                          style={{
                            marginLeft: isIndented ? `${level * 24}px` : '0px',
                            borderLeft: isIndented ? '2px solid var(--mantine-color-dark-4)' : 'none',
                            paddingLeft: isIndented ? '12px' : '0px'
                          }}
                        >
                          <Group gap="xs" align="flex-start">
                            <Checkbox
                              checked={tempSubPermissions.includes(subPerm.id)}
                              onChange={(event) => handleSubPermissionChange(subPerm.id, event.currentTarget.checked)}
                              size="sm"
                              color="blue"
                              styles={{
                                input: {
                                  marginTop: '2px'
                                }
                              }}
                            />
                            <Box style={{ flex: 1 }}>
                              <Group gap="xs" align="center" mb={2}>
                                {(() => {
                                  const IconComponent = getPermissionIcon(subPerm.id)
                                  return (
                                    <IconComponent
                                      size={16}
                                      style={{
                                        color: isParent ? 'var(--mantine-color-blue-4)' : 'var(--mantine-color-gray-5)',
                                        marginTop: '1px'
                                      }}
                                    />
                                  )
                                })()}
                                <Text
                                  size={isParent ? 'sm' : 'xs'}
                                  fw={isParent ? 600 : 500}
                                  c={isParent ? 'white' : 'gray.2'}
                                  style={{ lineHeight: 1.4 }}
                                >
                                  {subPerm.name}
                                </Text>
                              </Group>
                              <Text
                                size="xs"
                                c="dimmed"
                                style={{ lineHeight: 1.3 }}
                              >
                                {subPerm.description}
                              </Text>
                            </Box>
                          </Group>
                        </Box>
                      )
                    })}
                  </Stack>
                </Paper>
              )}

              {/* Boutons d'action - Style Odoo */}
              <Group justify="flex-end" gap="sm" mt="auto" pt="md">
                <Button
                  variant="default"
                  onClick={handleCancel}
                  size="sm"
                  styles={{
                    root: {
                      fontSize: '0.75rem',
                      border: '1px solid var(--mantine-color-dark-4)',
                      backgroundColor: 'var(--mantine-color-dark-6)'
                    }
                  }}
                >
                  Annuler
                </Button>
                <Button
                  onClick={handleSave}
                  color="blue"
                  size="sm"
                  styles={{
                    root: {
                      fontSize: '0.75rem'
                    }
                  }}
                >
                  Mettre à jour
                </Button>
              </Group>
            </Stack>
          </Box>
        </Stack>
      </Box>
    )
  }

  // Fonctions de gestion des rôles
  const handleAddRole = () => {
    if (!newRoleName.trim() || !newRoleDisplayName.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Veuillez remplir tous les champs',
        color: 'red',
        icon: <IconX size={16} />
      })
      return
    }

    const success = addRole(newRoleName.trim(), newRoleDisplayName.trim())

    if (success) {
      setNewRoleName('')
      setNewRoleDisplayName('')
      setAddModalOpened(false)

      notifications.show({
        title: 'Succès',
        message: 'Rôle ajouté avec succès',
        color: 'green',
        icon: <IconCheck size={16} />
      })
    } else {
      notifications.show({
        title: 'Erreur',
        message: 'Ce rôle existe déjà',
        color: 'red',
        icon: <IconX size={16} />
      })
    }
  }

  const handleEditRole = (roleId: string) => {
    const role = roles.find(r => r.id === roleId)
    if (role && !role.isProtected) {
      setEditingRole(roleId)
      setNewRoleDisplayName(role.displayName)
    }
  }

  const handleSaveEdit = () => {
    if (!newRoleDisplayName.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le nom d\'affichage ne peut pas être vide',
        color: 'red',
        icon: <IconX size={16} />
      })
      return
    }

    if (editingRole) {
      const success = updateRole(editingRole, newRoleDisplayName.trim())

      if (success) {
        setEditingRole(null)
        setNewRoleDisplayName('')

        notifications.show({
          title: 'Succès',
          message: 'Rôle modifié avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
      } else {
        notifications.show({
          title: 'Erreur',
          message: 'Impossible de modifier ce rôle',
          color: 'red',
          icon: <IconX size={16} />
        })
      }
    }
  }

  const handleDeleteRole = (roleId: string) => {
    const role = roles.find(r => r.id === roleId)
    if (role && !role.isProtected) {
      setRoleToDelete(roleId)
      setDeleteModalOpened(true)
    }
  }

  const confirmDeleteRole = () => {
    if (roleToDelete) {
      const success = deleteRole(roleToDelete)

      if (success) {
        setDeleteModalOpened(false)
        setRoleToDelete(null)

        notifications.show({
          title: 'Succès',
          message: 'Rôle supprimé avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
      } else {
        notifications.show({
          title: 'Erreur',
          message: 'Impossible de supprimer ce rôle',
          color: 'red',
          icon: <IconX size={16} />
        })
      }
    }
  }

  return (
    <ProtectedRoute
      requiredRole={UserRole.ADMIN}
    >
      <Stack p="md" gap="xl">
        <Title order={3}>Administration</Title>
        
        <Tabs defaultValue="dashboard" variant="outline">
          <Tabs.List>
            <Tabs.Tab value="dashboard">
              Tableau de bord
            </Tabs.Tab>
            <Tabs.Tab value="users">
              Utilisateurs
            </Tabs.Tab>
            <Tabs.Tab value="roles">
              Rôles
            </Tabs.Tab>
            <Tabs.Tab value="permissions">
              Droits d'accès
            </Tabs.Tab>
            <Tabs.Tab value="audit">
              Journal d'activité
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="dashboard" pt="md">
            <AdminDashboard />
          </Tabs.Panel>

          <Tabs.Panel value="users" pt="md">
            <UserManagement />
          </Tabs.Panel>

          <Tabs.Panel value="roles" pt="md">
            <Stack gap="xl">
              <Title order={4}>Gestion des Rôles</Title>

              {/* Section Technical */}
              <Paper p="md" radius="md" withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={5}>Technical</Title>
                  <Button
                    leftSection={<IconPlus size={16} />}
                    variant="filled"
                    size="sm"
                    onClick={() => setAddModalOpened(true)}
                  >
                    Ajouter un rôle
                  </Button>
                </Group>

                {/* Liste des rôles */}
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Nom technique</Table.Th>
                      <Table.Th>Nom d'affichage</Table.Th>
                      <Table.Th>Type</Table.Th>
                      <Table.Th>Actions</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {roles.map((role) => (
                      <Table.Tr key={role.id}>
                        <Table.Td>
                          <Text ff="monospace" size="sm">{role.name}</Text>
                        </Table.Td>
                        <Table.Td>
                          {editingRole === role.id ? (
                            <TextInput
                              value={newRoleDisplayName}
                              onChange={(e) => setNewRoleDisplayName(e.target.value)}
                              size="sm"
                            />
                          ) : (
                            role.displayName
                          )}
                        </Table.Td>
                        <Table.Td>
                          <Badge
                            color={role.isSystem ? 'blue' : 'green'}
                            variant="light"
                            size="sm"
                          >
                            {role.isSystem ? 'SYSTÈME' : 'PERSONNALISÉ'}
                          </Badge>
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            {editingRole === role.id ? (
                              <>
                                <ActionIcon
                                  color="green"
                                  variant="light"
                                  size="sm"
                                  onClick={handleSaveEdit}
                                >
                                  <IconCheck size={14} />
                                </ActionIcon>
                                <ActionIcon
                                  color="gray"
                                  variant="light"
                                  size="sm"
                                  onClick={() => {
                                    setEditingRole(null)
                                    setNewRoleDisplayName('')
                                  }}
                                >
                                  <IconX size={14} />
                                </ActionIcon>
                              </>
                            ) : (
                              <>
                                <ActionIcon
                                  color="blue"
                                  variant="light"
                                  size="sm"
                                  onClick={() => handleEditRole(role.id)}
                                  disabled={role.isProtected}
                                  style={{
                                    opacity: role.isProtected ? 0.5 : 1,
                                    cursor: role.isProtected ? 'not-allowed' : 'pointer'
                                  }}
                                >
                                  <IconEdit size={14} />
                                </ActionIcon>
                                <ActionIcon
                                  color="red"
                                  variant="light"
                                  size="sm"
                                  onClick={() => handleDeleteRole(role.id)}
                                  disabled={role.isProtected}
                                  style={{
                                    opacity: role.isProtected ? 0.5 : 1,
                                    cursor: role.isProtected ? 'not-allowed' : 'pointer'
                                  }}
                                >
                                  <IconTrash size={14} />
                                </ActionIcon>
                              </>
                            )}
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </Paper>


            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="permissions" pt="md">
            <Stack gap="xl">
              <Title order={4}>Droits d'accès</Title>

              {/* Section Droits d'accès - Cachée en vue détail */}
              <Transition
                mounted={!permissionDetailOpened}
                transition="fade"
                duration={250}
                timingFunction="ease-in-out"
              >
                {(styles) => (
                  <Paper p="md" radius="md" withBorder style={styles}>
                    <Stack gap="md">
                      <Select
                        label="Sélectionner un rôle"
                        placeholder="Choisir un rôle à configurer"
                        data={getModifiableRolesForSelect()}
                        value={selectedRole}
                        onChange={(value) => {
                          const modifiableRoles = getModifiableRoles()
                          setSelectedRole(value || modifiableRoles[0]?.name || 'moderator')
                        }}
                        comboboxProps={{
                          withinPortal: true,
                          zIndex: 10000,
                          position: 'bottom-start',
                          offset: 5
                        }}
                      />

                      {/* Zone de configuration des droits */}
                      <Box>
                        <Title order={6} mb="md" c="dimmed" ta="center">
                          Configuration des droits pour le rôle : {selectedRole}
                        </Title>

                        {selectedRole && (
                          <Text size="xs" c="dimmed" ta="center" mb="md">
                            Note: Le rôle "Administrateur" a tous les droits par défaut et n'est pas modifiable
                          </Text>
                        )}

                        <Grid>
                          {DEFAULT_PERMISSIONS.map((permission) => (
                            <Grid.Col key={permission.id} span={{ base: 12, sm: 6, md: 4 }}>
                              <PermissionTag permission={permission} />
                            </Grid.Col>
                          ))}
                        </Grid>
                      </Box>
                    </Stack>
                  </Paper>
                )}
              </Transition>

              {/* Vue détail des permissions - Remplace toute la section */}
              <Transition
                mounted={permissionDetailOpened}
                transition="fade"
                duration={250}
                timingFunction="ease-in-out"
              >
                {(styles) => (
                  <Box style={styles}>
                    <PermissionDetailView />
                  </Box>
                )}
              </Transition>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="audit" pt="md">
            <AuditLogComponent />
          </Tabs.Panel>
        </Tabs>

        {/* Modal d'ajout de rôle */}
        <Modal
          opened={addModalOpened}
          onClose={() => {
            setAddModalOpened(false)
            setNewRoleName('')
            setNewRoleDisplayName('')
          }}
          title="Ajouter un nouveau rôle"
          centered
          size="md"
          overlayProps={{
            backgroundOpacity: 0.55,
            blur: 3,
          }}
          styles={{
            modal: {
              position: 'fixed',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
            }
          }}
        >
          <Stack gap="md">
            <TextInput
              label="Nom du rôle (technique)"
              placeholder="ex: manager"
              value={newRoleName}
              onChange={(e) => setNewRoleName(e.target.value)}
              required
              description="Nom technique utilisé en interne (minuscules, sans espaces)"
            />
            <TextInput
              label="Nom d'affichage"
              placeholder="ex: Gestionnaire"
              value={newRoleDisplayName}
              onChange={(e) => setNewRoleDisplayName(e.target.value)}
              required
              description="Nom affiché dans l'interface utilisateur"
            />
            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                onClick={() => {
                  setAddModalOpened(false)
                  setNewRoleName('')
                  setNewRoleDisplayName('')
                }}
              >
                Annuler
              </Button>
              <Button
                leftSection={<IconCheck size={16} />}
                onClick={handleAddRole}
              >
                Ajouter
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Modal de confirmation de suppression */}
        <Modal
          opened={deleteModalOpened}
          onClose={() => setDeleteModalOpened(false)}
          title="Confirmer la suppression"
          centered
          size="sm"
          overlayProps={{
            backgroundOpacity: 0.55,
            blur: 3,
          }}
          styles={{
            modal: {
              position: 'fixed',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
            }
          }}
        >
          <Stack gap="md">
            <Text>
              Êtes-vous sûr de vouloir supprimer ce rôle ? Cette action est irréversible.
            </Text>
            <Group justify="flex-end">
              <Button
                variant="outline"
                onClick={() => setDeleteModalOpened(false)}
              >
                Annuler
              </Button>
              <Button
                color="red"
                onClick={confirmDeleteRole}
              >
                Supprimer
              </Button>
            </Group>
          </Stack>
        </Modal>


      </Stack>
    </ProtectedRoute>
  )
}
