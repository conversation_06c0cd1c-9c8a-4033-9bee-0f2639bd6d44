import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState } from 'react'
import LoginScreen from '@/components/LoginScreen'
import { useAtom } from 'jotai'
import { authAtom, splashCompletedAtom, loginAtom } from '@/stores/atoms'

export const Route = createFileRoute('/login')({
  component: LoginPage,
})

function LoginPage() {
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [, setAuth] = useAtom(authAtom)
  const [, setSplashCompleted] = useAtom(splashCompletedAtom)

  const [, login] = useAtom(loginAtom) // ✅ Utiliser le vrai service d'authentification

  const handleLogin = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true)
    setError('')

    try {
      // ✅ CORRECTION : Utiliser le vrai service d'authentification au lieu du hardcodé
      const result = await login({ username, password, rememberMe: false })

      if (result.success) {
        // L'authentification est déjà gérée par loginAtom
        // Redirection vers la page d'accueil
        navigate({ to: '/', replace: true })
        return true
      } else {
        // Authentification échouée
        setError(result.error || 'Nom d\'utilisateur ou mot de passe incorrect')
        return false
      }
    } catch (err) {
      console.error('Erreur de connexion:', err)
      setError('Erreur de connexion. Veuillez réessayer.')
      return false
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <LoginScreen
      onLogin={handleLogin}
      isLoading={isLoading}
      error={error}
    />
  )
}
