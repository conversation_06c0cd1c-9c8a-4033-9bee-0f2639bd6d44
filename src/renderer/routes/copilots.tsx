import Page from '@/components/Page'
import CopilotsCategoryInterface from '@/components/CopilotsCategoryInterface'
import { createFileRoute } from '@tanstack/react-router'
import React from 'react'
import { useTranslation } from 'react-i18next'

export const Route = createFileRoute('/copilots')({
  component: Copilots,
})

function Copilots() {
  const { t } = useTranslation()

  const handleSelectKnowledgeBase = (kb: any) => {
    // Action optionnelle lors du clic sur une base de connaissances
    console.log('Base de connaissances sélectionnée:', kb)
  }

  return (
    <Page title={t('My Copilots')}>
      <div style={{ height: 'calc(100vh - 60px)', width: '100%' }}>
        <CopilotsCategoryInterface
          onSelect={handleSelectKnowledgeBase}
        />
      </div>
    </Page>
  )
}
