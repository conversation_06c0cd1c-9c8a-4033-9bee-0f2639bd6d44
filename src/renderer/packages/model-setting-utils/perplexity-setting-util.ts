import { ModelProvider, ModelProviderEnum, ProviderSettings, SessionType } from 'src/shared/types'
import BaseConfig from './base-config'
import { ModelSettingUtil } from './interface'

export default class PerplexitySettingUtil extends BaseConfig implements ModelSettingUtil {
  public provider: ModelProvider = ModelProviderEnum.Perplexity
  async getCurrentModelDisplayName(
    model: string,
    sessionType: SessionType,
    providerSettings?: ProviderSettings
  ): Promise<string> {
    return `Perplexity API (${providerSettings?.models?.find((m) => m.modelId === model)?.nickname || model})`
  }


  protected async listProviderModels(settings: ProviderSettings) {
    return []
  }
}
