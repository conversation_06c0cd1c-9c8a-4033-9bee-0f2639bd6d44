import { ModelProvider, ModelProviderEnum, ProviderSettings, SessionType } from 'src/shared/types'
import BaseConfig from './base-config'
import { ModelSettingUtil } from './interface'
import { fetchRemoteModels } from '../models/openai-compatible'

export default class MistralSettingUtil extends BaseConfig implements ModelSettingUtil {
  public provider: ModelProvider = ModelProviderEnum.Mistral

  async getCurrentModelDisplayName(
    model: string,
    sessionType: SessionType,
    providerSettings?: ProviderSettings
  ): Promise<string> {
    if (sessionType === 'picture') {
      // Mistral ne supporte pas encore la génération d'images
      return `Mistral AI (${model})`
    } else {
      const modelInfo = providerSettings?.models?.find((m) => m.modelId === model)
      const displayName = modelInfo?.nickname || model
      return `Mistral AI (${displayName})`
    }
  }

  protected async listProviderModels() {
    const providerSettings = this.getProviderSettings()
    if (!providerSettings?.apiKey) {
      return []
    }

    try {
      return await fetchRemoteModels({
        apiHost: providerSettings.apiHost || 'https://api.mistral.ai/v1',
        apiKey: providerSettings.apiKey,
        useProxy: providerSettings.useProxy,
      })
    } catch (error) {
      console.error('Failed to fetch Mistral models:', error)
      return []
    }
  }
}
