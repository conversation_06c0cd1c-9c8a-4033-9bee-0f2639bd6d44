import { ProviderModelInfo } from 'src/shared/types'
import OpenAICompatible from './openai-compatible'

interface Options {
  openrouterAPIKey: string
  model: ProviderModelInfo
  temperature?: number
  topP?: number
}

export default class OpenRouter extends OpenAICompatible {
  public name = 'OpenRouter'

  constructor(public options: Options) {
    super({
      apiKey: options.openrouterAPIKey,
      apiHost: 'https://openrouter.ai/api/v1',
      model: options.model,
      temperature: options.temperature,
      topP: options.topP,
    })
  }

  protected getCallSettings() {
    return {
      temperature: this.options.temperature,
      topP: this.options.topP,
      // Limiter les tokens pour éviter les erreurs de crédit
      maxTokens: 50, // Limite très basse pour les tests et économiser les crédits
    }
  }

  protected getChatModel() {
    const { createOpenAI } = require('@ai-sdk/openai')
    const provider = createOpenAI({
      apiKey: this.options.openrouterAPIKey,
      baseURL: 'https://openrouter.ai/api/v1',
      headers: {
        'HTTP-Referer': 'https://datatec.ai',
        'X-Title': 'DataTec AI',
      },
    })
    return provider.languageModel(this.options.model.modelId)
  }

  isSupportToolUse(scope?: 'web-browsing') {
    // La plupart des modèles sur OpenRouter supportent les function calls
    if (scope === 'web-browsing') {
      return true
    }
    return super.isSupportToolUse()
  }

  isSupportVision() {
    // Détecter les modèles avec support vision par leur nom
    const visionModels = [
      'openai/gpt-4o',
      'openai/gpt-4o-mini',
      'anthropic/claude-3.5-sonnet',
      'anthropic/claude-3.5-haiku',
      'google/gemini-2.0-flash-exp',
      'google/gemini-1.5-pro',
      'meta-llama/llama-3.2-90b-vision-instruct',
    ]

    return visionModels.some(model => this.options.model.modelId.includes(model)) ||
           this.options.model.capabilities?.includes('vision') || false
  }

  isSupportReasoning() {
    // Détecter les modèles de raisonnement par leur nom
    const reasoningModels = [
      'openai/o1',
      'deepseek/deepseek-r1',
    ]

    return reasoningModels.some(model => this.options.model.modelId.includes(model)) ||
           this.options.model.capabilities?.includes('reasoning') || false
  }

  public async listModels(): Promise<string[]> {
    try {
      // Récupérer la liste des modèles disponibles depuis l'API OpenRouter
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${this.options.openrouterAPIKey}`,
          'HTTP-Referer': 'https://datatec.ai',
          'X-Title': 'DataTec AI',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.statusText}`)
      }

      const data = await response.json()

      // Retourner la liste des IDs de modèles
      return data.data.map((model: any) => model.id)
    } catch (error) {
      console.error('Failed to fetch OpenRouter models:', error)
      // Retourner les modèles par défaut en cas d'erreur (priorité aux gratuits)
      return [
        'deepseek/deepseek-chat-v3-0324:free',
        'meta-llama/llama-3.2-3b-instruct:free',
        'meta-llama/llama-3.2-1b-instruct:free',
        'microsoft/phi-3-mini-128k-instruct:free',
        'google/gemma-2-9b-it:free',
        'openai/gpt-4o-mini',
        'anthropic/claude-3.5-haiku',
        'google/gemini-2.0-flash-exp',
        'deepseek/deepseek-chat',
      ]
    }
  }
}
