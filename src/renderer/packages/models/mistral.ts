import { ProviderModelInfo } from 'src/shared/types'
import OpenAICompatible from './openai-compatible'

interface Options {
  mistralAPIKey: string
  model: ProviderModelInfo
  temperature?: number
  topP?: number
}

export default class Mistral extends OpenAICompatible {
  public name = 'Mistral'

  constructor(public options: Options) {
    super({
      apiKey: options.mistralAPIKey,
      apiHost: 'https://api.mistral.ai/v1',
      model: options.model,
      temperature: options.temperature,
      topP: options.topP,
    })
  }

  isSupportToolUse(scope?: 'web-browsing') {
    // Mistral supporte les function calls pour la plupart des modèles
    if (scope === 'web-browsing') {
      return true
    }
    return super.isSupportToolUse()
  }

  isSupportVision() {
    // Seuls certains modèles Mistral supportent la vision
    const visionModels = [
      'pixtral-large-2411',
      'pixtral-12b-2409',
      'mistral-small-2503', // Mistral Small 3.1 avec vision
    ]
    return visionModels.includes(this.options.model.modelId)
  }
}
