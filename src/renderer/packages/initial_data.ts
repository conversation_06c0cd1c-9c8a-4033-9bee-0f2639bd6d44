import { migrateMessage } from '@/utils/message'
import { ModelProviderEnum, Session } from '../../shared/types'

// Sessions d'exemple supprimées - DataTec Workspace démarre avec une liste vide
// L'utilisateur peut créer ses propres sessions via les boutons "New Chat" et "New Images"

export const defaultSessionsForEN: Session[] = []

export const defaultSessionsForCN: Session[] = []

export const imageCreatorSessionForEN: Session | null = null

export const imageCreatorSessionForCN: Session | null = null

export const artifactSessionEN: Session | null = null

export const artifactSessionCN: Session | null = null

export const mermaidSessionEN: Session | null = null

export const mermaidSessionCN: Session | null = null