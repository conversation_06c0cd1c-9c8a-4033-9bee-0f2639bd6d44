<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <!-- <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' *.example.com"
    /> -->
    <meta
      name="viewport"
      content="height=device-height, width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="chatbox" />
    <meta name="theme-color" content="#007bff" />
    <meta name="description" content="DataTec Workspace" />
    <meta name="msapplication-TileColor" content="#007bff" />
    <!-- Favicon ABSOLUTE PATH refresh: 2025-07-19T01:00:00.000Z -->
    <!-- Force favicon refresh with ABSOLUTE PATHS - SOLUTION FINALE -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg?bust=1752886800000&v=3000&absolute=1" media="(prefers-color-scheme: light)" />
    <link rel="icon" type="image/svg+xml" href="/favicon-dark.svg?bust=1752886800000&v=3000&absolute=1" media="(prefers-color-scheme: dark)" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg?bust=1752886800000&v=3000&absolute=1" />
    <link rel="apple-touch-icon" href="/favicon.svg?bust=1752886800000&v=3000&absolute=1" />
    <!-- Meta to force favicon reload -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="favicon-force-reload" content="1752886800000" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <!-- <link rel="manifest" href="%PUBLIC_URL%/manifest.json" /> -->
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>DataTec Workspace</title>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-B365F44W6E"></script>
    <script defer data-domain="app.chatboxai.app" src="https://plausible.midway.run/js/script.local.hash.js"></script>
    <script>
      window.plausible =
        window.plausible ||
        function () {
          ;(window.plausible.q = window.plausible.q || []).push(arguments)
        }
    </script>

    <script>
      window.dataLayer = window.dataLayer || []
      function gtag() {
        dataLayer.push(arguments)
      }
      gtag('js', new Date())

      // gtag('config', 'G-B365F44W6E');
    </script>
    <script>
      var initialTheme = localStorage.getItem('initial-theme')
      if (initialTheme === 'light' || initialTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', initialTheme)
        document.documentElement.setAttribute('data-mantine-color-scheme', initialTheme)
      }
    </script>
    <style>
      /* Styles de base pour l'application */
      body {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }

      #root {
        width: 100vw;
        height: 100vh;
      }


    </style>
  </head>

  <body>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
