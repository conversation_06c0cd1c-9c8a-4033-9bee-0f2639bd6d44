// Tests et validation complète du nouveau système d'authentification
// Tests automatisés pour desktop et web

import { userManagementService } from '../services/UserManagementService'
import { migrationService } from '../services/MigrationService'
import { userDatabase } from '../database/DexieUserDatabase'
import { databaseConfig } from '../database/DatabaseConfig'
import { CreateUserRequest, LoginRequest } from '../../shared/types/database'

export interface TestResult {
  name: string
  success: boolean
  duration: number
  error?: string
  details?: any
}

export interface ValidationReport {
  timestamp: string
  platform: string
  totalTests: number
  passedTests: number
  failedTests: number
  duration: number
  results: TestResult[]
  summary: {
    database: boolean
    authentication: boolean
    permissions: boolean
    migration: boolean
    performance: boolean
  }
}

export class SystemValidation {
  private static instance: SystemValidation

  private constructor() {}

  public static getInstance(): SystemValidation {
    if (!SystemValidation.instance) {
      SystemValidation.instance = new SystemValidation()
    }
    return SystemValidation.instance
  }

  // Exécuter tous les tests
  public async runFullValidation(): Promise<ValidationReport> {
    const startTime = Date.now()
    const results: TestResult[] = []

    console.log('🧪 Début de la validation complète du système...')

    // Tests de base de données
    results.push(...await this.runDatabaseTests())
    
    // Tests d'authentification
    results.push(...await this.runAuthenticationTests())
    
    // Tests de permissions
    results.push(...await this.runPermissionTests())
    
    // Tests de migration
    results.push(...await this.runMigrationTests())
    
    // Tests de performance
    results.push(...await this.runPerformanceTests())

    const endTime = Date.now()
    const duration = endTime - startTime

    const passedTests = results.filter(r => r.success).length
    const failedTests = results.filter(r => !r.success).length

    const report: ValidationReport = {
      timestamp: new Date().toISOString(),
      platform: typeof window !== 'undefined' ? 'web' : 'desktop',
      totalTests: results.length,
      passedTests,
      failedTests,
      duration,
      results,
      summary: {
        database: this.checkCategorySuccess(results, 'Database'),
        authentication: this.checkCategorySuccess(results, 'Authentication'),
        permissions: this.checkCategorySuccess(results, 'Permission'),
        migration: this.checkCategorySuccess(results, 'Migration'),
        performance: this.checkCategorySuccess(results, 'Performance')
      }
    }

    console.log(`🎯 Validation terminée: ${passedTests}/${results.length} tests réussis en ${duration}ms`)
    
    return report
  }

  // Tests de base de données
  private async runDatabaseTests(): Promise<TestResult[]> {
    const tests: TestResult[] = []

    // Test 1: Connexion à la base de données
    tests.push(await this.runTest('Database Connection', async () => {
      await userDatabase.open()
      const isOpen = userDatabase.isOpen()
      if (!isOpen) throw new Error('Base de données non ouverte')
      return { isOpen }
    }))

    // Test 2: Création des tables
    tests.push(await this.runTest('Database Tables', async () => {
      const tables = userDatabase.tables.map(t => t.name)
      const expectedTables = ['users', 'credentials', 'sessions', 'roles', 'permissions', 'userRoles', 'auditLogs']
      
      for (const table of expectedTables) {
        if (!tables.includes(table)) {
          throw new Error(`Table manquante: ${table}`)
        }
      }
      
      return { tables }
    }))

    // Test 3: Données par défaut
    tests.push(await this.runTest('Database Default Data', async () => {
      await userDatabase.initializeDefaultData()
      const stats = await userDatabase.getStats()
      
      if (stats.users === 0) throw new Error('Aucun utilisateur par défaut')
      if (stats.roles === 0) throw new Error('Aucun rôle par défaut')
      
      return stats
    }))

    // Test 4: Configuration
    tests.push(await this.runTest('Database Configuration', async () => {
      const config = await databaseConfig.loadConfig()
      const validation = databaseConfig.validateConfig(config)
      
      if (!validation.valid) {
        throw new Error(`Configuration invalide: ${validation.errors.join(', ')}`)
      }
      
      return { config, validation }
    }))

    return tests
  }

  // Tests d'authentification
  private async runAuthenticationTests(): Promise<TestResult[]> {
    const tests: TestResult[] = []

    // Test 1: Initialisation du service
    tests.push(await this.runTest('Authentication Service Init', async () => {
      await userManagementService.initialize()
      const isInitialized = userManagementService.isInitialized()
      
      if (!isInitialized) throw new Error('Service non initialisé')
      
      return { isInitialized }
    }))

    // Test 2: Connexion admin
    tests.push(await this.runTest('Authentication Admin Login', async () => {
      const credentials: LoginRequest = {
        username: 'admin',
        password: 'admin123'
      }
      
      const result = await userManagementService.login(credentials)
      
      if (!result.success) {
        throw new Error(`Connexion échouée: ${result.error}`)
      }
      
      if (!result.user || !result.token) {
        throw new Error('Données de connexion manquantes')
      }
      
      return { user: result.user.username, hasToken: !!result.token }
    }))

    // Test 3: Validation de session
    tests.push(await this.runTest('Authentication Session Validation', async () => {
      // D'abord se connecter
      const loginResult = await userManagementService.login({
        username: 'admin',
        password: 'admin123'
      })
      
      if (!loginResult.success || !loginResult.token) {
        throw new Error('Impossible de créer une session de test')
      }
      
      // Valider la session
      const user = await userManagementService.validateSession(loginResult.token)
      
      if (!user) {
        throw new Error('Session invalide')
      }
      
      // Déconnexion
      await userManagementService.logout(loginResult.token)
      
      return { username: user.username }
    }))

    // Test 4: Création d'utilisateur
    tests.push(await this.runTest('Authentication User Creation', async () => {
      const userData: CreateUserRequest = {
        username: `test_${Date.now()}`,
        email: `test_${Date.now()}@test.com`,
        password: 'test123456',
        displayName: 'Test User',
        role: 'user'
      }
      
      const result = await userManagementService.createUser(userData)
      
      if (!result.success) {
        throw new Error(`Création échouée: ${result.error}`)
      }
      
      return { username: userData.username }
    }))

    // Test 5: Changement de mot de passe
    tests.push(await this.runTest('Authentication Password Change', async () => {
      // Créer un utilisateur de test
      const userData: CreateUserRequest = {
        username: `pwd_test_${Date.now()}`,
        email: `pwd_test_${Date.now()}@test.com`,
        password: 'oldpassword123',
        displayName: 'Password Test User',
        role: 'user'
      }
      
      const createResult = await userManagementService.createUser(userData)
      if (!createResult.success || !createResult.user) {
        throw new Error('Impossible de créer l\'utilisateur de test')
      }
      
      // Changer le mot de passe
      const changeResult = await userManagementService.changePassword(
        createResult.user.id!,
        'oldpassword123',
        'newpassword123'
      )
      
      if (!changeResult) {
        throw new Error('Changement de mot de passe échoué')
      }
      
      return { userId: createResult.user.id }
    }))

    return tests
  }

  // Tests de permissions
  private async runPermissionTests(): Promise<TestResult[]> {
    const tests: TestResult[] = []

    // Test 1: Vérification des permissions admin
    tests.push(await this.runTest('Permission Admin Check', async () => {
      const adminUser = await userManagementService.getUserByUsername('admin')
      if (!adminUser) throw new Error('Utilisateur admin non trouvé')
      
      const hasPermission = await userManagementService.checkPermission(
        adminUser.id!,
        'user',
        'create'
      )
      
      if (!hasPermission) {
        throw new Error('Admin devrait avoir toutes les permissions')
      }
      
      return { userId: adminUser.id, hasPermission }
    }))

    // Test 2: Permissions utilisateur standard
    tests.push(await this.runTest('Permission User Check', async () => {
      const user = await userManagementService.getUserByUsername('user')
      if (!user) throw new Error('Utilisateur standard non trouvé')
      
      const hasCreatePermission = await userManagementService.checkPermission(
        user.id!,
        'user',
        'create'
      )
      
      const hasReadPermission = await userManagementService.checkPermission(
        user.id!,
        'user',
        'read'
      )
      
      return { 
        userId: user.id, 
        hasCreatePermission, 
        hasReadPermission 
      }
    }))

    // Test 3: Obtenir les permissions d'un utilisateur
    tests.push(await this.runTest('Permission User Permissions', async () => {
      const adminUser = await userManagementService.getUserByUsername('admin')
      if (!adminUser) throw new Error('Utilisateur admin non trouvé')
      
      const permissions = await userManagementService.getUserPermissions(adminUser.id!)
      
      if (permissions.length === 0) {
        throw new Error('Admin devrait avoir des permissions')
      }
      
      return { permissionsCount: permissions.length }
    }))

    return tests
  }

  // Tests de migration
  private async runMigrationTests(): Promise<TestResult[]> {
    const tests: TestResult[] = []

    // Test 1: Statut de migration
    tests.push(await this.runTest('Migration Status Check', async () => {
      const status = await migrationService.getMigrationStatus()
      return { status }
    }))

    // Test 2: Test de migration (simulation)
    tests.push(await this.runTest('Migration Test', async () => {
      const testResult = await migrationService.testMigration()
      return testResult
    }))

    return tests
  }

  // Tests de performance
  private async runPerformanceTests(): Promise<TestResult[]> {
    const tests: TestResult[] = []

    // Test 1: Performance de connexion
    tests.push(await this.runTest('Performance Login Speed', async () => {
      const startTime = Date.now()
      
      const result = await userManagementService.login({
        username: 'admin',
        password: 'admin123'
      })
      
      const duration = Date.now() - startTime
      
      if (!result.success) {
        throw new Error('Connexion échouée')
      }
      
      if (duration > 1000) {
        throw new Error(`Connexion trop lente: ${duration}ms`)
      }
      
      // Déconnexion
      if (result.token) {
        await userManagementService.logout(result.token)
      }
      
      return { duration }
    }))

    // Test 2: Performance de requête
    tests.push(await this.runTest('Performance Database Query', async () => {
      const startTime = Date.now()
      
      const users = await userManagementService.listUsers({ limit: 100 })
      
      const duration = Date.now() - startTime
      
      if (duration > 500) {
        throw new Error(`Requête trop lente: ${duration}ms`)
      }
      
      return { duration, usersCount: users.length }
    }))

    // Test 3: Performance des permissions
    tests.push(await this.runTest('Performance Permission Check', async () => {
      const adminUser = await userManagementService.getUserByUsername('admin')
      if (!adminUser) throw new Error('Admin non trouvé')
      
      const startTime = Date.now()
      
      // Vérifier plusieurs permissions
      const checks = await Promise.all([
        userManagementService.checkPermission(adminUser.id!, 'user', 'create'),
        userManagementService.checkPermission(adminUser.id!, 'user', 'read'),
        userManagementService.checkPermission(adminUser.id!, 'user', 'update'),
        userManagementService.checkPermission(adminUser.id!, 'user', 'delete'),
        userManagementService.checkPermission(adminUser.id!, 'role', 'create')
      ])
      
      const duration = Date.now() - startTime
      
      if (duration > 200) {
        throw new Error(`Vérifications de permissions trop lentes: ${duration}ms`)
      }
      
      return { duration, checksCount: checks.length }
    }))

    return tests
  }

  // Utilitaire pour exécuter un test
  private async runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
    const startTime = Date.now()
    
    try {
      const details = await testFn()
      const duration = Date.now() - startTime
      
      console.log(`✅ ${name} (${duration}ms)`)
      
      return {
        name,
        success: true,
        duration,
        details
      }
    } catch (error) {
      const duration = Date.now() - startTime
      
      console.error(`❌ ${name} (${duration}ms):`, error)
      
      return {
        name,
        success: false,
        duration,
        error: `${error}`
      }
    }
  }

  // Vérifier le succès d'une catégorie de tests
  private checkCategorySuccess(results: TestResult[], category: string): boolean {
    const categoryTests = results.filter(r => r.name.includes(category))
    return categoryTests.length > 0 && categoryTests.every(r => r.success)
  }

  // Générer un rapport détaillé
  public generateReport(report: ValidationReport): string {
    const lines: string[] = []
    
    lines.push('# 📊 RAPPORT DE VALIDATION DU SYSTÈME')
    lines.push('')
    lines.push(`**Date:** ${new Date(report.timestamp).toLocaleString()}`)
    lines.push(`**Plateforme:** ${report.platform}`)
    lines.push(`**Durée totale:** ${report.duration}ms`)
    lines.push('')
    
    lines.push('## 📈 Résumé')
    lines.push(`- **Tests totaux:** ${report.totalTests}`)
    lines.push(`- **Tests réussis:** ${report.passedTests} ✅`)
    lines.push(`- **Tests échoués:** ${report.failedTests} ${report.failedTests > 0 ? '❌' : ''}`)
    lines.push(`- **Taux de réussite:** ${Math.round((report.passedTests / report.totalTests) * 100)}%`)
    lines.push('')
    
    lines.push('## 🎯 Résumé par Catégorie')
    lines.push(`- **Base de données:** ${report.summary.database ? '✅' : '❌'}`)
    lines.push(`- **Authentification:** ${report.summary.authentication ? '✅' : '❌'}`)
    lines.push(`- **Permissions:** ${report.summary.permissions ? '✅' : '❌'}`)
    lines.push(`- **Migration:** ${report.summary.migration ? '✅' : '❌'}`)
    lines.push(`- **Performance:** ${report.summary.performance ? '✅' : '❌'}`)
    lines.push('')
    
    lines.push('## 📋 Détail des Tests')
    for (const result of report.results) {
      const status = result.success ? '✅' : '❌'
      lines.push(`- ${status} **${result.name}** (${result.duration}ms)`)
      
      if (!result.success && result.error) {
        lines.push(`  - Erreur: ${result.error}`)
      }
    }
    
    if (report.failedTests > 0) {
      lines.push('')
      lines.push('## ⚠️ Tests Échoués')
      const failedTests = report.results.filter(r => !r.success)
      for (const test of failedTests) {
        lines.push(`### ${test.name}`)
        lines.push(`**Erreur:** ${test.error}`)
        lines.push('')
      }
    }
    
    return lines.join('\n')
  }
}

// Instance singleton
export const systemValidation = SystemValidation.getInstance()

// Export par défaut
export default systemValidation

// Fonction utilitaire pour les tests depuis la console
if (typeof window !== 'undefined') {
  (window as any).runSystemValidation = async () => {
    const report = await systemValidation.runFullValidation()
    console.log(systemValidation.generateReport(report))
    return report
  }
}
