// Tests d'unification du système d'authentification
// Vérification qu'il n'y a qu'un seul système actif

import { authService } from '../services/AuthenticationService'
import { userManagementService } from '../services/UserManagementService'
import { userDatabase } from '../database/DexieUserDatabase'

export interface UnificationTestResult {
  name: string
  success: boolean
  error?: string
  details?: any
}

export class AuthUnificationTests {
  
  // Test 1: Vérifier qu'AuthService.ts n'existe plus
  async testAuthServiceRemoved(): Promise<UnificationTestResult> {
    try {
      // Tenter d'importer l'ancien AuthService
      try {
        await import('../services/AuthService')
        return {
          name: 'AuthService Removal',
          success: false,
          error: 'AuthService.ts existe encore - doit être supprimé'
        }
      } catch {
        return {
          name: 'AuthService Removal',
          success: true,
          details: 'AuthService.ts correctement supprimé'
        }
      }
    } catch (error) {
      return {
        name: 'AuthService Removal',
        success: false,
        error: `Erreur lors du test: ${error}`
      }
    }
  }

  // Test 2: Vérifier qu'UserService.ts n'existe plus
  async testUserServiceRemoved(): Promise<UnificationTestResult> {
    try {
      try {
        await import('../services/UserService')
        return {
          name: 'UserService Removal',
          success: false,
          error: 'UserService.ts existe encore - doit être supprimé'
        }
      } catch {
        return {
          name: 'UserService Removal',
          success: true,
          details: 'UserService.ts correctement supprimé'
        }
      }
    } catch (error) {
      return {
        name: 'UserService Removal',
        success: false,
        error: `Erreur lors du test: ${error}`
      }
    }
  }

  // Test 3: Vérifier que UserManagementService est un proxy
  async testUserManagementServiceIsProxy(): Promise<UnificationTestResult> {
    try {
      // Vérifier que les méthodes délèguent bien à AuthenticationService
      const isInitialized = userManagementService.isInitialized()
      const authIsInitialized = authService.isInitialized()
      
      if (isInitialized !== authIsInitialized) {
        return {
          name: 'UserManagementService Proxy',
          success: false,
          error: 'UserManagementService ne délègue pas correctement à AuthenticationService'
        }
      }

      return {
        name: 'UserManagementService Proxy',
        success: true,
        details: 'UserManagementService fonctionne comme proxy vers AuthenticationService'
      }
    } catch (error) {
      return {
        name: 'UserManagementService Proxy',
        success: false,
        error: `Erreur lors du test: ${error}`
      }
    }
  }

  // Test 4: Vérifier qu'une seule base de données est utilisée
  async testSingleDatabaseUsage(): Promise<UnificationTestResult> {
    try {
      await authService.initialize()
      
      // Vérifier que la base de données Dexie est utilisée
      const dbName = userDatabase.name
      if (dbName !== 'DataTecUserDB') {
        return {
          name: 'Single Database Usage',
          success: false,
          error: `Base de données incorrecte: ${dbName}`
        }
      }

      // Vérifier qu'il n'y a pas de données hardcodées
      const users = await userDatabase.users.toArray()
      const hasOnlyDbUsers = users.every(user => user.id && user.createdAt)
      
      if (!hasOnlyDbUsers) {
        return {
          name: 'Single Database Usage',
          success: false,
          error: 'Des utilisateurs hardcodés sont encore présents'
        }
      }

      return {
        name: 'Single Database Usage',
        success: true,
        details: `Base de données unique: ${dbName}, ${users.length} utilisateurs`
      }
    } catch (error) {
      return {
        name: 'Single Database Usage',
        success: false,
        error: `Erreur lors du test: ${error}`
      }
    }
  }

  // Test 5: Vérifier l'authentification unifiée
  async testUnifiedAuthentication(): Promise<UnificationTestResult> {
    try {
      await authService.initialize()
      
      // Créer un utilisateur de test
      const testUser = {
        username: 'test_unification',
        email: '<EMAIL>',
        password: 'test123',
        displayName: 'Test Unification',
        role: 'user' as const
      }

      // Test via AuthenticationService
      const authResult = await authService.createUser(testUser)
      if (!authResult.success) {
        return {
          name: 'Unified Authentication',
          success: false,
          error: 'Échec de création via AuthenticationService'
        }
      }

      // Test via UserManagementService (proxy)
      const proxyResult = await userManagementService.createUser({
        ...testUser,
        username: 'test_unification_proxy'
      })
      if (!proxyResult.success) {
        return {
          name: 'Unified Authentication',
          success: false,
          error: 'Échec de création via UserManagementService proxy'
        }
      }

      // Nettoyer
      await userDatabase.users.where('username').anyOf(['test_unification', 'test_unification_proxy']).delete()

      return {
        name: 'Unified Authentication',
        success: true,
        details: 'Authentification unifiée fonctionnelle'
      }
    } catch (error) {
      return {
        name: 'Unified Authentication',
        success: false,
        error: `Erreur lors du test: ${error}`
      }
    }
  }

  // Test 6: Vérifier qu'aucun utilisateur hardcodé n'est utilisé en production
  async testNoHardcodedUsers(): Promise<UnificationTestResult> {
    try {
      // Vérifier qu'il n'y a pas de comptes hardcodés dans le code
      const testCredentials = [
        { username: 'admin', password: 'admin123' },
        { username: 'user', password: 'user123' },
        { username: 'demo', password: 'demo123' }
      ]

      for (const cred of testCredentials) {
        const result = await authService.login(cred)
        if (result.success) {
          // Vérifier que l'utilisateur vient bien de la base de données
          const user = await userDatabase.users.where('username').equals(cred.username).first()
          if (!user || !user.createdAt) {
            return {
              name: 'No Hardcoded Users',
              success: false,
              error: `Utilisateur hardcodé détecté: ${cred.username}`
            }
          }
        }
      }

      return {
        name: 'No Hardcoded Users',
        success: true,
        details: 'Aucun utilisateur hardcodé détecté'
      }
    } catch (error) {
      return {
        name: 'No Hardcoded Users',
        success: false,
        error: `Erreur lors du test: ${error}`
      }
    }
  }

  // Exécuter tous les tests
  async runAllTests(): Promise<{
    success: boolean
    results: UnificationTestResult[]
    summary: {
      total: number
      passed: number
      failed: number
    }
  }> {
    const tests = [
      this.testAuthServiceRemoved(),
      this.testUserServiceRemoved(),
      this.testUserManagementServiceIsProxy(),
      this.testSingleDatabaseUsage(),
      this.testUnifiedAuthentication(),
      this.testNoHardcodedUsers()
    ]

    const results = await Promise.all(tests)
    const passed = results.filter(r => r.success).length
    const failed = results.length - passed

    return {
      success: failed === 0,
      results,
      summary: {
        total: results.length,
        passed,
        failed
      }
    }
  }
}

// Instance singleton
export const authUnificationTests = new AuthUnificationTests()
export default authUnificationTests
