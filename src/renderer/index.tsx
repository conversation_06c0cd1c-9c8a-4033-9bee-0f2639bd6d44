import '@mantine/core/styles.css'
import '@mantine/notifications/styles.css'
import * as Sentry from '@sentry/react'
import { RouterProvider } from '@tanstack/react-router'
import { useAtomValue } from 'jotai'
import { StrictMode, useState, useEffect } from 'react'
import ReactDOM from 'react-dom/client'
import { ErrorBoundary } from './components/ErrorBoundary'
import './i18n'
import { cn, getLogger } from './lib/utils'
import reportWebVitals from './reportWebVitals'
import { router } from './router'
import { initData } from './setup/init_data'
import './static/globals.css'
import './static/index.css'
import { initLogAtom, migrationProcessAtom } from './stores/atoms/utilAtoms'
import * as migration from './stores/migration'
import { CHATBOX_BUILD_PLATFORM, CHATBOX_BUILD_TARGET } from './variables'
import '@mantine/spotlight/styles.css'

const log = getLogger('index')

// 按需加载 polyfill
import './setup/load_polyfill'

// Sentry 初始化
import './setup/sentry_init'

// 全局错误处理
import './setup/global_error_handler'

// GA4 初始化
import './setup/ga_init'

// 引入保护代码
import './setup/protect'

// 开发环境下引入错误测试工具
// if (process.env.NODE_ENV === 'development') {
//   import('./utils/error-testing')
// }

// 引入移动端安全区域代码，主要为了解决异形屏幕的问题
if (CHATBOX_BUILD_TARGET === 'mobile_app' && CHATBOX_BUILD_PLATFORM === 'ios') {
  import('./setup/mobile_safe_area')
}

// ==========执行初始化==============
async function initializeApp() {
  log.info('initializeApp')

  try {
    // 数据迁移
    await migration.migrate()
    log.info('migrate done')
  } catch (e) {
    log.error('migrate error', e)
    Sentry.captureException(e as Error)
  }

  try {
    // migration 中没有写入 Demo session了，可以在 migration 之后初始化
    // 初始化数据
    await initData()
    log.info('init data done')
  } catch (e) {
    log.error('init data error', e)
    Sentry.captureException(e as Error)
  }

  // Système de récupération des données perdues - temporairement désactivé
  if (false) {
    try {
      const { StorageRecovery } = await import('./utils/storageRecovery')

      // Nettoyer les données corrompues
      await StorageRecovery.cleanupCorruptedData()

      // Tenter de récupérer les données perdues
      const recoveryResult = await StorageRecovery.recoverLostData()

      if (recoveryResult.recovered.length > 0) {
        log.info('Storage recovery successful:', recoveryResult.recovered)
      }

      if (recoveryResult.errors.length > 0) {
        log.warn('Storage recovery errors:', recoveryResult.errors)
      }

    } catch (e) {
      log.error('storage recovery error', e)
      Sentry.captureException(e as Error)
    }
  }

  // 最后执行 storage 清理，清理不 block 进入UI
  import('./setup/storage_clear')

  // 启动mcp服务器
  import('./setup/mcp_bootstrap')
}

// ==========渲染节点==============

function InitPage() {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      backgroundColor: 'var(--background-color, #282828)',
      color: 'var(--text-color, #F8F9FA)',
      fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
    }}>
      <div style={{
        textAlign: 'center',
        opacity: 0.7
      }}>
        <div style={{ fontSize: '18px', fontWeight: 500 }}>
          DataTec Workspace - Initialisation...
        </div>
      </div>
    </div>
  )
}

// Délai minimal pour le démarrage de React
const QUICK_START_DELAY = 100 // Délai minimal pour s'assurer que le DOM est prêt

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement)

// Function to start React after splash animation
function startReactApp() {
  console.log('Starting React app after splash animation...')

  // First render the InitPage
  root.render(
    <StrictMode>
      <ErrorBoundary>
        <InitPage />
      </ErrorBoundary>
    </StrictMode>
  )

  // Then initialize the app and render the main interface
  initializeApp()
    .catch((e) => {
      // 初始化中的各个步骤已经捕获了错误，这里防止未来添加未捕获的逻辑
      Sentry.captureException(e)
      log.error('initializeApp error', e)
    })
    .finally(() => {
      // 初始化完成，可以开始渲染
      console.log('App initialization complete, rendering main interface...')
      root.render(
        <StrictMode>
          <ErrorBoundary>
            <RouterProvider router={router} />
          </ErrorBoundary>
        </StrictMode>
      )
    })
}

// Import SplashService for refresh detection
import { SplashService } from './services/SplashService'

// Check if splash animation is complete, otherwise wait for the full duration
function checkAndStartReact() {
  // Toujours démarrer React immédiatement
  // (Il n'y a pas de splash HTML initial à attendre)
  console.log('🚀 Démarrage immédiat de React')
  setTimeout(startReactApp, QUICK_START_DELAY)
}

// Start checking immediately
checkAndStartReact()

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals()
