import storage, { StorageKey } from '@/storage'
import { StorageKeyGenerator } from '@/storage/StoreStorage'
import { getLogger } from '@/lib/utils'

const log = getLogger('clearExampleSessions')

// IDs des sessions d'exemple à supprimer
const EXAMPLE_SESSION_IDS = [
  // Sessions EN
  'justchat-b612-406a-985b-3ab4d2c482ff',
  '6dafa15e-c72f-4036-ac89-33c09e875bdc',
  'e22ab364-4681-4e24-aaba-461ed0fccfd3',
  'chatbox-chat-demo-image-creator',
  'chatbox-chat-demo-artifact-1-en',
  'mermaid-demo-1-en',

  // Sessions CN
  '81cfc426-48b4-4a13-ad42-bfcfc4544299',
  '3e091ac6-ebfa-42c9-b125-c67ac2d45ee1',
  'chatbox-chat-demo-artifact-1-cn',
  'mermaid-demo-1-cn',

  // Autres sessions d'exemple possibles
  'software-developer-example',
  'social-media-influencer-example',
  'translator-example',
  'travel-guide-example',
]

// Mots-clés pour identifier les sessions d'exemple par leur nom
const EXAMPLE_SESSION_KEYWORDS = [
  'example',
  'Example',
  'EXAMPLE',
  '(Example)',
  '(example)',
  'demo',
  'Demo',
  'DEMO',
  'sample',
  'Sample',
  'SAMPLE',
  'test',
  'Test',
  'TEST',
  'Software Developer',
  'Translator',
  'Social Media Influencer',
  'Travel Guide',
  'Image Creator',
  'Just chat',
  'Markdown 101',
  'ChartWhiz',
  'Snake Game',
  'Artifact',
  '示例',
  '演示',
  '样例',
  '测试',
  '小红书文案生成器',
  '翻译助手',
  '贪吃蛇',
  '做图表',
]

/**
 * Vérifie si une session est une session d'exemple basée sur son nom ou ID
 */
function isExampleSession(session: { id: string; name?: string }): boolean {
  // Vérifier par ID
  if (EXAMPLE_SESSION_IDS.includes(session.id)) {
    return true
  }

  // Vérifier par nom si disponible
  if (session.name) {
    return EXAMPLE_SESSION_KEYWORDS.some(keyword =>
      session.name!.includes(keyword)
    )
  }

  return false
}

/**
 * Supprime toutes les sessions d'exemple du stockage
 */
export async function clearExampleSessions() {
  try {
    log.info('Début de la suppression des sessions d\'exemple')

    // 1. Récupérer la liste des sessions pour identifier les sessions d'exemple
    const sessionsList = await storage.getItem(StorageKey.ChatSessionsList, [])
    const exampleSessions = sessionsList.filter(isExampleSession)

    log.info(`Trouvé ${exampleSessions.length} sessions d'exemple à supprimer`)

    // 2. Supprimer les sessions individuelles (par ID connu + détectées)
    const allSessionIdsToRemove = [
      ...EXAMPLE_SESSION_IDS,
      ...exampleSessions.map(session => session.id)
    ]

    for (const sessionId of allSessionIdsToRemove) {
      try {
        await storage.removeItem(StorageKeyGenerator.session(sessionId))
        log.info(`Session supprimée: ${sessionId}`)
      } catch (error) {
        // Ignorer les erreurs si la session n'existe pas
        log.debug(`Session non trouvée (ignoré): ${sessionId}`)
      }
    }

    // 3. Nettoyer la liste des sessions en utilisant la détection intelligente
    const filteredSessions = sessionsList.filter(session => !isExampleSession(session))

    if (filteredSessions.length !== sessionsList.length) {
      await storage.setItem(StorageKey.ChatSessionsList, filteredSessions)
      log.info(`Liste des sessions nettoyée: ${sessionsList.length} -> ${filteredSessions.length}`)
    }

    // 4. Nettoyer les sessions favorites
    try {
      const starredSessions = await storage.getItem('starred-sessions', [])
      if (Array.isArray(starredSessions)) {
        const filteredStarred = starredSessions.filter(
          (sessionId) => !allSessionIdsToRemove.includes(sessionId)
        )
        if (filteredStarred.length !== starredSessions.length) {
          await storage.setItem('starred-sessions', filteredStarred)
          log.info(`Sessions favorites nettoyées`)
        }
      }
    } catch (error) {
      log.debug('Pas de sessions favorites à nettoyer')
    }

    // 5. Forcer la mise à jour des atoms pour refléter les changements immédiatement
    try {
      const { getDefaultStore } = await import('jotai')
      const atoms = await import('../stores/atoms')
      const store = getDefaultStore()
      store.set(atoms.sessionsListAtom, filteredSessions)
      log.info('Atoms mis à jour avec la liste nettoyée')
    } catch (error) {
      log.debug('Erreur lors de la mise à jour des atoms:', error)
    }

    log.info('Suppression des sessions d\'exemple terminée avec succès')
    return true
  } catch (error) {
    log.error('Erreur lors de la suppression des sessions d\'exemple:', error)
    return false
  }
}

/**
 * Vérifie si des sessions d'exemple existent encore
 */
export async function hasExampleSessions(): Promise<boolean> {
  try {
    const sessionsList = await storage.getItem(StorageKey.ChatSessionsList, [])
    return sessionsList.some(isExampleSession)
  } catch (error) {
    log.error('Erreur lors de la vérification des sessions d\'exemple:', error)
    return false
  }
}
