/**
 * Utilitaire pour tracer l'ordre d'exécution des useEffect et identifier les problèmes de timing
 */

interface EffectExecution {
  id: string
  component: string
  timestamp: number
  dependencies: any[]
  phase: 'mount' | 'update' | 'cleanup'
}

class EffectTracker {
  private executions: EffectExecution[] = []
  private isEnabled = process.env.NODE_ENV === 'development'

  /**
   * Enregistre l'exécution d'un effet
   */
  track(id: string, component: string, dependencies: any[], phase: 'mount' | 'update' | 'cleanup' = 'update') {
    if (!this.isEnabled) return

    const execution: EffectExecution = {
      id,
      component,
      timestamp: Date.now(),
      dependencies: [...dependencies],
      phase
    }

    this.executions.push(execution)

    console.log(`🔄 [EFFECT] ${component}.${id} (${phase}):`, {
      timestamp: new Date(execution.timestamp).toISOString(),
      dependencies: dependencies.map(dep => typeof dep === 'object' ? JSON.stringify(dep) : dep)
    })

    // Garder seulement les 100 dernières exécutions
    if (this.executions.length > 100) {
      this.executions = this.executions.slice(-100)
    }
  }

  /**
   * Analyse l'ordre d'exécution et détecte les problèmes
   */
  analyze() {
    if (!this.isEnabled) return

    const analysis = {
      totalExecutions: this.executions.length,
      components: new Set(this.executions.map(e => e.component)).size,
      timeline: this.executions.slice(-20), // 20 dernières exécutions
      issues: [] as string[]
    }

    // Détecter les exécutions trop rapprochées (possible race condition)
    for (let i = 1; i < this.executions.length; i++) {
      const current = this.executions[i]
      const previous = this.executions[i - 1]
      
      if (current.timestamp - previous.timestamp < 10 && 
          current.component === previous.component &&
          current.id === previous.id) {
        analysis.issues.push(`Exécutions rapprochées détectées: ${current.component}.${current.id}`)
      }
    }

    // Détecter les cycles d'exécution
    const recentExecutions = this.executions.slice(-10)
    const executionCounts = new Map<string, number>()
    
    recentExecutions.forEach(exec => {
      const key = `${exec.component}.${exec.id}`
      executionCounts.set(key, (executionCounts.get(key) || 0) + 1)
    })

    executionCounts.forEach((count, key) => {
      if (count > 3) {
        analysis.issues.push(`Cycle d'exécution détecté: ${key} (${count} fois)`)
      }
    })

    console.log('📊 [EFFECT ANALYSIS]', analysis)
    return analysis
  }

  /**
   * Réinitialise le tracker
   */
  reset() {
    this.executions = []
    console.log('🔄 [EFFECT] Tracker réinitialisé')
  }

  /**
   * Obtient les exécutions récentes
   */
  getRecentExecutions(count = 10) {
    return this.executions.slice(-count)
  }
}

export const effectTracker = new EffectTracker()

/**
 * Hook personnalisé pour tracer les useEffect
 */
export const useTrackedEffect = (
  effect: React.EffectCallback,
  deps: React.DependencyList | undefined,
  id: string,
  component: string
) => {
  const { useEffect } = require('react')
  
  useEffect(() => {
    effectTracker.track(id, component, deps || [], 'mount')
    
    const cleanup = effect()
    
    return () => {
      effectTracker.track(id, component, deps || [], 'cleanup')
      if (cleanup) cleanup()
    }
  }, deps)
}

/**
 * Utilitaire pour créer des logs de timing détaillés
 */
export class TimingLogger {
  private startTime = Date.now()
  private checkpoints: Array<{ name: string; time: number; delta: number }> = []

  checkpoint(name: string) {
    const now = Date.now()
    const delta = now - (this.checkpoints.length > 0 ? this.checkpoints[this.checkpoints.length - 1].time : this.startTime)
    
    this.checkpoints.push({
      name,
      time: now,
      delta
    })

    console.log(`⏱️ [TIMING] ${name}: +${delta}ms (total: ${now - this.startTime}ms)`)
  }

  summary() {
    const total = Date.now() - this.startTime
    console.log('⏱️ [TIMING SUMMARY]', {
      total: `${total}ms`,
      checkpoints: this.checkpoints.map(cp => ({
        name: cp.name,
        delta: `+${cp.delta}ms`
      }))
    })
    return this.checkpoints
  }

  reset() {
    this.startTime = Date.now()
    this.checkpoints = []
  }
}

/**
 * Logger global pour l'initialisation de l'application
 */
export const appInitTimer = new TimingLogger()

/**
 * Utilitaire pour détecter les problèmes de hydratation
 */
export class HydrationDetector {
  private static isHydrating = true
  private static hydrationIssues: string[] = []

  static markHydrationComplete() {
    this.isHydrating = false
    console.log('💧 [HYDRATION] Hydratation terminée')
    
    if (this.hydrationIssues.length > 0) {
      console.warn('⚠️ [HYDRATION] Problèmes détectés:', this.hydrationIssues)
    }
  }

  static detectIssue(component: string, issue: string) {
    if (this.isHydrating) {
      this.hydrationIssues.push(`${component}: ${issue}`)
      console.warn(`⚠️ [HYDRATION] ${component}: ${issue}`)
    }
  }

  static isStillHydrating() {
    return this.isHydrating
  }

  static getIssues() {
    return [...this.hydrationIssues]
  }
}

// Marquer la fin de l'hydratation après un délai
if (typeof window !== 'undefined') {
  setTimeout(() => {
    HydrationDetector.markHydrationComplete()
  }, 2000)
}
