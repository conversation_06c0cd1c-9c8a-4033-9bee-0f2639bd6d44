import { getDefaultStore } from 'jotai'
import * as atoms from '../stores/atoms'
import * as defaults from '../../shared/defaults'
import storage, { StorageKey } from '../storage'
import { StorageKeyGenerator } from '../storage/StoreStorage'
import { Session, SessionMeta } from '../../shared/types'

/**
 * Nettoie le prompt par défaut stocké dans les paramètres utilisateur
 * pour s'assurer qu'il utilise la nouvelle valeur vide
 */
export async function clearDefaultPrompt() {
  const store = getDefaultStore()
  const settings = store.get(atoms.settingsAtom)
  
  // Si le prompt par défaut contient l'ancien message, le vider
  if (settings.defaultPrompt === 'You are a helpful assistant.' || 
      settings.defaultPrompt === 'You are a helpful assistant') {
    
    console.log('Clearing old default prompt:', settings.defaultPrompt)
    
    // Mettre à jour avec le nouveau prompt vide
    store.set(atoms.settingsAtom, {
      ...settings,
      defaultPrompt: defaults.getDefaultPrompt() // Maintenant vide
    })
    
    console.log('Default prompt cleared successfully')
  }
}

/**
 * Force la réinitialisation du prompt par défaut à vide
 */
export async function forceResetDefaultPrompt() {
  const store = getDefaultStore()
  const settings = store.get(atoms.settingsAtom)

  console.log('Force resetting default prompt from:', settings.defaultPrompt)

  // Forcer la mise à jour avec le nouveau prompt vide
  store.set(atoms.settingsAtom, {
    ...settings,
    defaultPrompt: '' // Forcer à vide
  })

  console.log('Default prompt force reset to empty')
}

/**
 * Nettoie les sessions existantes qui contiennent l'ancien message système
 */
export async function cleanExistingSessionsWithOldPrompt() {
  try {
    const store = getDefaultStore()
    const sessionsList = store.get(atoms.sessionsListAtom)

    console.log('Checking', sessionsList.length, 'sessions for old system messages')

    let cleanedCount = 0

    for (const sessionMeta of sessionsList) {
      const session = await storage.getItem<Session | null>(
        StorageKeyGenerator.session(sessionMeta.id),
        null
      )

      if (!session) continue

      // Vérifier si la session contient l'ancien message système
      const hasOldSystemMessage = session.messages.some(msg =>
        msg.role === 'system' &&
        (msg.contentParts?.[0]?.type === 'text') &&
        (msg.contentParts[0].text === 'You are a helpful assistant.' ||
         msg.contentParts[0].text === 'You are a helpful assistant')
      )

      if (hasOldSystemMessage) {
        console.log('Cleaning session:', session.name, 'with old system message')

        // Supprimer les messages système avec l'ancien prompt
        const cleanedMessages = session.messages.filter(msg =>
          !(msg.role === 'system' &&
            msg.contentParts?.[0]?.type === 'text' &&
            (msg.contentParts[0].text === 'You are a helpful assistant.' ||
             msg.contentParts[0].text === 'You are a helpful assistant'))
        )

        // Sauvegarder la session nettoyée
        const cleanedSession = {
          ...session,
          messages: cleanedMessages
        }

        await storage.setItemNow(StorageKeyGenerator.session(session.id), cleanedSession)
        cleanedCount++
      }
    }

    console.log('Cleaned', cleanedCount, 'sessions with old system messages')

  } catch (error) {
    console.error('Error cleaning existing sessions:', error)
  }
}
