import { countWord } from '@/packages/word-count'
import { assign, cloneDeep, omit } from 'lodash'
import type { Message, MessageContentParts, MessagePicture, SearchResultItem } from 'src/shared/types'
import storage from '@/storage'

export function getMessageText(message: Message, includeImagePlaceHolder = true): string {
  if (message.contentParts && message.contentParts.length > 0) {
    return message.contentParts
      .map((c) => {
        if (c.type === 'text') {
          return c.text
        }
        if (c.type === 'image') {
          return includeImagePlaceHolder ? '[image]' : null
        }
        return ''
      })
      .filter((c) => c !== null)
      .join('\n')
  }
  return ''
}

// 只有这里可以访问 message 的 content / webBrowsing 字段，迁移到 contentParts 字段
export function migrateMessage(
  message: Omit<Message, 'contentParts'> & { contentParts?: MessageContentParts }
): Message {
  const result: Message = {
    id: message.id || '',
    role: message.role || 'user',
    contentParts: message.contentParts || [],
  }
  // 还是保留原始content字段，删除webBrowsing字段
  assign(result, omit(message, 'webBrowsing'))

  // 如果 contentParts 不存在，或者 contentParts 为空，或者 contentParts 的内容为 '...'(placeholder)，则使用 content 的值
  if (
    (!result.contentParts?.length || getMessageText(result) === '...' || !getMessageText(result)) &&
    'content' in message
  ) {
    const imageParts = (message as Message & { pictures?: MessagePicture[] }).pictures
      ?.filter((pic) => pic.storageKey || pic.url)
      .map((pic) => ({ type: 'image' as const, storageKey: pic.storageKey!, url: pic.url }))
    result.contentParts = [{ type: 'text', text: String(message.content ?? '') }, ...(imageParts || [])]
  }

  if ('webBrowsing' in message) {
    const webBrowsing = message.webBrowsing as {
      query: string[]
      links: { title: string; url: string }[]
    }
    result.contentParts.unshift({
      type: 'tool-call',
      state: 'result',
      toolCallId: `web_search_${message.id}`,
      toolName: 'web_search',
      args: {
        query: webBrowsing.query.join(', '),
      },
      result: {
        query: webBrowsing.query.join(', '),
        searchResults: webBrowsing.links.map((link) => ({
          title: link.title,
          link: link.url,
          snippet: link.title,
        })) satisfies SearchResultItem[],
      },
    })
  }

  return result
}

export function cloneMessage(message: Message): Message {
  return cloneDeep(message)
}

export function isEmptyMessage(message: Message): boolean {
  return getMessageText(message).length === 0
}

export function countMessageWords(message: Message): number {
  return countWord(getMessageText(message))
}

export function mergeMessages(a: Message, b: Message): Message {
  const ret = cloneMessage(a)
  // Merge contentParts
  ret.contentParts = [...(ret.contentParts || []), ...(b.contentParts || [])]

  return ret
}

export function fixMessageRoleSequence(messages: Message[]): Message[] {
  let result: Message[] = []
  if (messages.length <= 1) {
    result = messages
  } else {
    let currentMessage = cloneMessage(messages[0]) // 复制，避免后续修改导致的引用问题

    for (let i = 1; i < messages.length; i++) {
      const message = cloneMessage(messages[i]) // 复制消息避免修改原对象

      if (message.role === currentMessage.role) {
        currentMessage = mergeMessages(currentMessage, message)
      } else {
        result.push(currentMessage)
        currentMessage = message
      }
    }
    result.push(currentMessage)
  }
  // 如果顺序中的第一条 assistant 消息前面不是 user 消息，则插入一个 user 消息
  const firstAssistantIndex = result.findIndex((m) => m.role === 'assistant')
  if (firstAssistantIndex !== -1 && result[firstAssistantIndex - 1]?.role !== 'user') {
    result = [
      ...result.slice(0, firstAssistantIndex),
      { role: 'user', contentParts: [{ type: 'text', text: 'OK.' }], id: 'user_before_assistant_id' },
      ...result.slice(firstAssistantIndex),
    ]
  }
  return result
}

/**
 * SequenceMessages organizes and orders messages to follow the sequence: system -> user -> assistant -> user -> etc.
 * 这个方法只能用于 llm 接口请求前的参数构造，因为会过滤掉消息中的无关字段，所以不适用于其他消息存储的场景
 * 这个方法本质上是 golang API 服务中方法的 TypeScript 实现
 * @param msgs
 * @returns
 */
export async function sequenceMessages(msgs: Message[]): Promise<Message[]> {
  // Merge all system messages first
  let system: Message = {
    id: '',
    role: 'system',
    contentParts: [],
  }
  for (let msg of msgs) {
    if (msg.role === 'system') {
      system = mergeMessages(system, msg)
    }
  }

  // Traiter les bases de connaissances des messages utilisateur
  // et créer un message système avec les instructions
  for (let msg of msgs) {
    if (msg.role === 'user' && msg.knowledgeBase) {
      console.log('🔍 DEBUG - sequenceMessages: Traitement base de connaissances:', msg.knowledgeBase.name)

      let kbContent = `[Base de connaissances: ${msg.knowledgeBase.name}]\n\n`

      // Ajouter la description/instructions
      if (msg.knowledgeBase.description) {
        kbContent += `INSTRUCTIONS SYSTÈME PRIORITAIRES:\n${msg.knowledgeBase.description}\n\n`
        kbContent += `IMPORTANT: Ces instructions sont OBLIGATOIRES et doivent être suivies à la lettre. Elles prennent priorité sur toute autre considération.\n\n`
      }

      // Ajouter les informations supplémentaires
      if (msg.knowledgeBase.additionalInfo) {
        kbContent += `Informations supplémentaires: ${msg.knowledgeBase.additionalInfo}\n\n`
      }

      // Ajouter le contenu des fichiers
      if (msg.knowledgeBase.files && msg.knowledgeBase.files.length > 0) {
        console.log('🔍 DEBUG - sequenceMessages: Fichiers de la base de connaissances:', msg.knowledgeBase.files.length)
        kbContent += `Documents de référence:\n\n`
        msg.knowledgeBase.files.forEach((file: any, index: number) => {
          console.log('🔍 DEBUG - sequenceMessages: Fichier', index + 1, ':', {
            name: file?.name,
            hasContent: !!file?.content,
            contentLength: file?.content?.length || 0,
            contentPreview: file?.content?.substring(0, 100) || 'AUCUN CONTENU'
          })

          if (file && file.name && file.content) {
            // Vérifier si le contenu est vide ou invalide
            if (!file.content.trim()) {
              console.log('⚠️ ATTENTION - sequenceMessages: Contenu vide pour le fichier:', file.name)
              kbContent += `--- Document ${index + 1}: ${file.name} ---\n`
              kbContent += `[ERREUR: Le fichier est vide ou n'a pas pu être lu correctement.]\n\n`
              return
            }

            // Nettoyer le contenu RTF si nécessaire
            let cleanContent = file.content
            if (file.content.startsWith('{\\rtf1') || file.content.includes('\\rtf1')) {
              console.log('🔍 DEBUG - sequenceMessages: Nettoyage RTF détecté pour:', file.name)
              console.log('🔍 DEBUG - sequenceMessages: Longueur originale:', file.content.length)

              // Nettoyage RTF amélioré
              cleanContent = file.content
                // Supprimer l'en-tête RTF
                .replace(/\{\\rtf1[^}]*\}/g, '')
                // Supprimer les tables de polices
                .replace(/\{\\fonttbl[^}]*\}/g, '')
                .replace(/\{\\colortbl[^}]*\}/g, '')
                // Supprimer les commandes de formatage
                .replace(/\\[a-z]+\d*\s*/gi, ' ')
                .replace(/\\[^a-z\s]/gi, '')
                // Supprimer les accolades restantes
                .replace(/[{}]/gi, '')
                // Nettoyer les caractères d'échappement
                .replace(/\\\\/g, '\\')
                .replace(/\\'/g, "'")
                .replace(/\\\"/g, '"')
                .replace(/\\n/g, '\n')
                .replace(/\\r/g, '\r')
                .replace(/\\t/g, '\t')
                // Nettoyer les espaces multiples
                .replace(/\s+/g, ' ')
                // Supprimer les caractères de contrôle
                .replace(/[\x00-\x1F\x7F]/g, '')
                // Nettoyer les lignes vides multiples
                .replace(/\n\s*\n\s*\n/g, '\n\n')
                .trim()

              console.log('🔍 DEBUG - sequenceMessages: Longueur après nettoyage:', cleanContent.length)
              console.log('🔍 DEBUG - sequenceMessages: Aperçu du contenu nettoyé:', cleanContent.substring(0, 200))

              // Vérification plus permissive du contenu nettoyé
              if (cleanContent.length < 500) {
                console.log('❌ ERREUR - sequenceMessages: Contenu RTF insuffisant après nettoyage pour:', file.name)
                cleanContent = `[ERREUR: Le fichier RTF n'a pas pu être correctement converti. Contenu original de ${file.content.length} caractères réduit à ${cleanContent.length} caractères.

SOLUTION RECOMMANDÉE:
1. Ouvrez votre document original
2. Sélectionnez tout le texte (Ctrl+A)
3. Copiez le texte (Ctrl+C)
4. Créez un nouveau fichier .txt
5. Collez le texte (Ctrl+V)
6. Sauvegardez et re-uploadez le fichier .txt

Contenu partiellement récupéré: ${cleanContent.substring(0, 300)}...]`
              } else {
                console.log('✅ SUCCESS - sequenceMessages: Nettoyage RTF réussi pour:', file.name)
              }
            }

            console.log('✅ SUCCESS - sequenceMessages: Contenu ajouté pour:', file.name, 'Longueur:', cleanContent.length)
            kbContent += `--- Document ${index + 1}: ${file.name} ---\n`
            kbContent += `${cleanContent}\n\n`
          } else {
            console.log('❌ ERREUR - sequenceMessages: Fichier invalide:', {
              hasFile: !!file,
              hasName: !!file?.name,
              hasContent: !!file?.content,
              file: file
            })
            kbContent += `--- Document ${index + 1}: [Fichier non disponible] ---\n\n`
          }
        })
      } else {
        console.log('ℹ️ INFO - sequenceMessages: Aucun fichier dans la base de connaissances')
      }

      // Ajouter les tags de personnalité
      if (msg.knowledgeBase.personalityTags && msg.knowledgeBase.personalityTags.length > 0) {
        kbContent += `Style de réponse: ${msg.knowledgeBase.personalityTags.join(', ')}\n\n`
      }

      // Créer un message système avec les instructions de la base de connaissances
      const kbSystemMessage: Message = {
        id: `kb_system_${msg.id}`,
        role: 'system',
        contentParts: [{ type: 'text', text: kbContent }],
      }

      // Fusionner avec le message système existant
      system = mergeMessages(system, kbSystemMessage)

      console.log('🔍 DEBUG - sequenceMessages: Instructions KB ajoutées au message système')
    }
  }

  // Traiter les fichiers attachés des messages utilisateur
  // et ajouter leur contenu au message système
  for (let msg of msgs) {
    if (msg.role === 'user' && msg.files && msg.files.length > 0) {
      console.log('🔍 DEBUG - sequenceMessages: Traitement des fichiers attachés:', msg.files.length, 'fichiers')

      for (const [fileIndex, file] of msg.files.entries()) {
        if (file.storageKey) {
          console.log('🔍 DEBUG - sequenceMessages: Lecture du fichier:', file.name, 'avec storageKey:', file.storageKey)

          try {
            const content = await storage.getBlob(file.storageKey)
            if (content) {
              let attachment = `\n\n<ATTACHMENT_FILE>\n`
              attachment += `<FILE_INDEX>File ${fileIndex + 1}</FILE_INDEX>\n`
              attachment += `<FILE_NAME>${file.name}</FILE_NAME>\n`
              attachment += '<FILE_CONTENT>\n'
              attachment += `${content}\n`
              attachment += '</FILE_CONTENT>\n'
              attachment += `</ATTACHMENT_FILE>\n`

              // Créer un message système avec le contenu du fichier
              const fileSystemMessage: Message = {
                id: `file_system_${msg.id}_${fileIndex}`,
                role: 'system',
                contentParts: [{ type: 'text', text: attachment }],
              }

              // Fusionner avec le message système existant
              system = mergeMessages(system, fileSystemMessage)

              console.log('🔍 DEBUG - sequenceMessages: Contenu du fichier ajouté au message système')
            } else {
              console.log('⚠️ ATTENTION - sequenceMessages: Contenu du fichier vide pour:', file.name)
            }
          } catch (error) {
            console.error('❌ ERREUR - sequenceMessages: Impossible de lire le fichier:', file.name, error)
          }
        } else {
          console.log('⚠️ ATTENTION - sequenceMessages: Fichier sans storageKey:', file.name)
        }
      }
    }
  }

  // Initialize the result array with the non-empty system message, if present
  let ret: Message[] = system.contentParts.length > 0 ? [system] : []
  let next: Message = {
    id: '',
    role: 'user',
    contentParts: [],
  }
  let isFirstUserMsg = true // Special handling for the first user message
  for (let msg of msgs) {
    // Skip the already processed system messages or empty messages
    if (msg.role === 'system' || isEmptyMessage(msg)) {
      continue
    }

    // Nettoyer les messages utilisateur qui ont des bases de connaissances
    // pour ne garder que la question de l'utilisateur
    if (msg.role === 'user' && msg.knowledgeBase) {
      console.log('🔍 DEBUG - sequenceMessages: Nettoyage du message utilisateur avec KB')
      // Créer une copie propre du message sans les métadonnées KB
      msg = {
        ...msg,
        knowledgeBase: undefined, // Supprimer les métadonnées KB du message final
      }
      console.log('🔍 DEBUG - sequenceMessages: Message utilisateur nettoyé, contenu:', getMessageText(msg))
    }

    // Merge consecutive messages from the same role
    if (msg.role === next.role) {
      next = mergeMessages(next, msg)
      continue
    }
    // Merge all assistant messages as a quote block if constructing the first user message
    if (isEmptyMessage(next) && isFirstUserMsg && msg.role === 'assistant') {
      let quote =
        getMessageText(msg)
          .split('\n')
          .map((line) => `> ${line}`)
          .join('\n') + '\n'
      msg.contentParts = [{ type: 'text', text: quote }]
      next = mergeMessages(next, msg)
      continue
    }
    // If not the first user message, add the current message to the result and start a new one
    if (!isEmptyMessage(next)) {
      ret.push(next)
      isFirstUserMsg = false
    }
    next = msg
  }
  // Add the last message if it's not empty
  if (!isEmptyMessage(next)) {
    ret.push(next)
  }
  // If there's only one system message, convert it to a user message
  if (ret.length === 1 && ret[0].role === 'system') {
    ret[0].role = 'user'
  }
  return ret
}
