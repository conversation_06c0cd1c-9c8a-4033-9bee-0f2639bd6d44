import { getDefaultStore } from 'jotai'
import storage, { StorageKey } from '@/storage'
import { authAtom } from '@/stores/atoms/authAtoms'
import { newAuth<PERSON>tom } from '@/stores/atoms/newAuthAtoms'
import { sessionsListAtom } from '@/stores/atoms/sessionAtoms'
import { settingsAtom } from '@/stores/atoms/settingsAtoms'
import { authService } from '@/services/AuthenticationService'

interface RecoveryResult {
  success: boolean
  recovered: string[]
  errors: string[]
  fallbacksUsed: string[]
}

/**
 * Système de récupération et fallback pour les données perdues
 */
export class StorageRecovery {
  private static store = getDefaultStore()

  /**
   * Tente de récupérer les données perdues après un rafraîchissement
   */
  static async recoverLostData(): Promise<RecoveryResult> {
    console.log('🔧 [RECOVERY] Début de la récupération des données...')
    
    const result: RecoveryResult = {
      success: false,
      recovered: [],
      errors: [],
      fallbacksUsed: []
    }

    try {
      // 1. Vérifier et récupérer l'authentification
      await this.recoverAuthentication(result)
      
      // 2. Vérifier et récupérer les sessions
      await this.recoverSessions(result)
      
      // 3. Vérifier et récupérer les paramètres
      await this.recoverSettings(result)
      
      // 4. Synchroniser les systèmes d'authentification
      await this.syncAuthSystems(result)

      result.success = result.errors.length === 0
      
      console.log('🔧 [RECOVERY] Récupération terminée:', result)
      return result

    } catch (error) {
      console.error('❌ [RECOVERY] Erreur critique:', error)
      result.errors.push(`Erreur critique: ${error}`)
      return result
    }
  }

  /**
   * Récupère les données d'authentification
   */
  private static async recoverAuthentication(result: RecoveryResult) {
    try {
      console.log('🔧 [RECOVERY] Récupération de l\'authentification...')
      
      // Vérifier l'état actuel des atoms
      const authState = this.store.get(authAtom)
      const newAuthState = this.store.get(newAuthAtom)
      
      console.log('🔧 [RECOVERY] États actuels:', { authState, newAuthState })

      // Si aucun système n'est authentifié, essayer de récupérer depuis localStorage
      if (!authState.isAuthenticated && !newAuthState.isAuthenticated) {
        console.log('🔧 [RECOVERY] Aucune authentification active, tentative de récupération...')
        
        // Vérifier les tokens dans localStorage
        const datatecSession = localStorage.getItem('datatec_session')
        const authData = localStorage.getItem('auth')
        const newAuthData = localStorage.getItem('new-auth')
        
        if (datatecSession) {
          try {
            const session = JSON.parse(datatecSession)
            if (session && new Date(session.expiresAt) > new Date()) {
              console.log('🔧 [RECOVERY] Session valide trouvée dans localStorage')
              
              // Initialiser le service d'authentification
              if (!authService.isInitialized()) {
                await authService.initialize()
              }
              
              // Récupérer l'utilisateur actuel
              const currentUser = await authService.getCurrentUser()
              if (currentUser) {
                // Restaurer l'état d'authentification
                this.store.set(authAtom, {
                  isAuthenticated: true,
                  user: {
                    id: currentUser.id!.toString(),
                    username: currentUser.username,
                    displayName: currentUser.displayName,
                    email: currentUser.email,
                    role: currentUser.role === 'admin' ? 'admin' : 'user'
                  },
                  token: session.token
                })
                
                result.recovered.push('authentication')
                console.log('✅ [RECOVERY] Authentification récupérée')
              }
            }
          } catch (error) {
            console.error('❌ [RECOVERY] Erreur lors de la récupération de la session:', error)
            result.errors.push(`Erreur session: ${error}`)
          }
        }
        
        // Fallback: utiliser les données des atoms stockés
        if (!result.recovered.includes('authentication')) {
          if (authData) {
            try {
              const parsedAuth = JSON.parse(authData)
              if (parsedAuth.isAuthenticated) {
                this.store.set(authAtom, parsedAuth)
                result.fallbacksUsed.push('localStorage auth atom')
                console.log('🔧 [RECOVERY] Fallback: auth atom restauré depuis localStorage')
              }
            } catch (error) {
              console.error('❌ [RECOVERY] Erreur fallback auth:', error)
            }
          }
          
          if (newAuthData) {
            try {
              const parsedNewAuth = JSON.parse(newAuthData)
              if (parsedNewAuth.isAuthenticated) {
                this.store.set(newAuthAtom, parsedNewAuth)
                result.fallbacksUsed.push('localStorage new-auth atom')
                console.log('🔧 [RECOVERY] Fallback: new-auth atom restauré depuis localStorage')
              }
            } catch (error) {
              console.error('❌ [RECOVERY] Erreur fallback new-auth:', error)
            }
          }
        }
      } else {
        console.log('✅ [RECOVERY] Authentification déjà active')
      }

    } catch (error) {
      console.error('❌ [RECOVERY] Erreur récupération auth:', error)
      result.errors.push(`Auth recovery: ${error}`)
    }
  }

  /**
   * Récupère les sessions
   */
  private static async recoverSessions(result: RecoveryResult) {
    try {
      console.log('🔧 [RECOVERY] Récupération des sessions...')
      
      const currentSessions = this.store.get(sessionsListAtom)
      
      if (currentSessions.length === 0) {
        console.log('🔧 [RECOVERY] Aucune session trouvée, tentative de récupération...')
        
        // Essayer de récupérer depuis le storage personnalisé
        const storedSessions = await storage.getItem(StorageKey.ChatSessionsList, [])
        
        if (storedSessions.length > 0) {
          this.store.set(sessionsListAtom, storedSessions)
          result.recovered.push('sessions')
          console.log(`✅ [RECOVERY] ${storedSessions.length} sessions récupérées`)
        } else {
          result.fallbacksUsed.push('empty sessions list')
          console.log('🔧 [RECOVERY] Aucune session à récupérer')
        }
      } else {
        console.log(`✅ [RECOVERY] ${currentSessions.length} sessions déjà chargées`)
      }

    } catch (error) {
      console.error('❌ [RECOVERY] Erreur récupération sessions:', error)
      result.errors.push(`Sessions recovery: ${error}`)
    }
  }

  /**
   * Récupère les paramètres
   */
  private static async recoverSettings(result: RecoveryResult) {
    try {
      console.log('🔧 [RECOVERY] Récupération des paramètres...')
      
      const currentSettings = this.store.get(settingsAtom)
      
      // Vérifier si les paramètres semblent corrects
      if (!currentSettings || !currentSettings.theme) {
        console.log('🔧 [RECOVERY] Paramètres manquants, tentative de récupération...')
        
        const storedSettings = await storage.getItem(StorageKey.Settings, {})
        
        if (Object.keys(storedSettings).length > 0) {
          // Fusionner avec les paramètres par défaut
          const defaultSettings = await import('@/../shared/defaults').then(m => m.settings())
          const mergedSettings = { ...defaultSettings, ...storedSettings }
          
          this.store.set(settingsAtom, mergedSettings)
          result.recovered.push('settings')
          console.log('✅ [RECOVERY] Paramètres récupérés')
        } else {
          result.fallbacksUsed.push('default settings')
          console.log('🔧 [RECOVERY] Utilisation des paramètres par défaut')
        }
      } else {
        console.log('✅ [RECOVERY] Paramètres déjà chargés')
      }

    } catch (error) {
      console.error('❌ [RECOVERY] Erreur récupération settings:', error)
      result.errors.push(`Settings recovery: ${error}`)
    }
  }

  /**
   * Synchronise les deux systèmes d'authentification
   */
  private static async syncAuthSystems(result: RecoveryResult) {
    try {
      console.log('🔧 [RECOVERY] Synchronisation des systèmes d\'auth...')
      
      const authState = this.store.get(authAtom)
      const newAuthState = this.store.get(newAuthAtom)
      
      // Si un seul système est authentifié, synchroniser l'autre
      if (authState.isAuthenticated && !newAuthState.isAuthenticated) {
        this.store.set(newAuthAtom, {
          ...newAuthState,
          isAuthenticated: true,
          user: authState.user,
          token: authState.token,
          initialized: true
        })
        result.recovered.push('sync new-auth from auth')
        console.log('✅ [RECOVERY] new-auth synchronisé depuis auth')
        
      } else if (newAuthState.isAuthenticated && !authState.isAuthenticated) {
        this.store.set(authAtom, {
          isAuthenticated: true,
          user: newAuthState.user,
          token: newAuthState.token
        })
        result.recovered.push('sync auth from new-auth')
        console.log('✅ [RECOVERY] auth synchronisé depuis new-auth')
      }

    } catch (error) {
      console.error('❌ [RECOVERY] Erreur synchronisation:', error)
      result.errors.push(`Sync error: ${error}`)
    }
  }

  /**
   * Nettoie les données corrompues ou en conflit
   */
  static async cleanupCorruptedData(): Promise<void> {
    console.log('🧹 [RECOVERY] Nettoyage des données corrompues...')
    
    try {
      // Supprimer les clés en double ou corrompues
      const keysToCheck = ['auth', 'new-auth', 'splash_completed', 'datatec_session']
      
      for (const key of keysToCheck) {
        const value = localStorage.getItem(key)
        if (value) {
          try {
            JSON.parse(value)
          } catch {
            console.log(`🧹 [RECOVERY] Suppression de la clé corrompue: ${key}`)
            localStorage.removeItem(key)
          }
        }
      }
      
    } catch (error) {
      console.error('❌ [RECOVERY] Erreur nettoyage:', error)
    }
  }
}
