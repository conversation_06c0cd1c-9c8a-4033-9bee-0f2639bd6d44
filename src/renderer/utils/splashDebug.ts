/**
 * Utilitaires de débogage pour le système de splash screen
 * Utilisez ces fonctions dans la console du navigateur pour tester
 */

import { SplashService } from '../services/SplashService'

// Exposer les fonctions de débogage dans window pour les tests
declare global {
  interface Window {
    splashDebug: {
      shouldShow: (isNewLogin?: boolean) => boolean
      markShown: () => void
      markSessionStart: () => void
      clearSession: () => void
      reset: () => void
      hasActiveSession: () => boolean
      getStatus: () => object
    }
  }
}

// Fonctions de débogage
export const splashDebug = {
  /**
   * Vérifie si le splash doit être affiché
   */
  shouldShow: (isNewLogin: boolean = false) => {
    const result = SplashService.shouldShowSplash(isNewLogin)
    console.log(`🔍 shouldShowSplash(${isNewLogin}) =`, result)
    return result
  },

  /**
   * Marque le splash comme affiché
   */
  markShown: () => {
    SplashService.markSplashShown()
    console.log('✅ Splash marqué comme affiché')
  },

  /**
   * Marque le début d'une session
   */
  markSessionStart: () => {
    SplashService.markSessionStart()
    console.log('🚀 Session marquée comme démarrée')
  },

  /**
   * Nettoie la session
   */
  clearSession: () => {
    SplashService.clearSession()
    console.log('🧹 Session nettoyée')
  },

  /**
   * Réinitialise complètement l'état
   */
  reset: () => {
    SplashService.reset()
    console.log('🔄 État du splash réinitialisé')
  },

  /**
   * Vérifie si une session est active
   */
  hasActiveSession: () => {
    const result = SplashService.hasActiveSession()
    console.log('📊 Session active:', result)
    return result
  },

  /**
   * Affiche le statut complet
   */
  getStatus: () => {
    const now = Date.now()
    const lastSplashShown = localStorage.getItem('datatec_splash_shown')
    const sessionStart = localStorage.getItem('datatec_session_start')

    const status = {
      now: new Date(now).toLocaleString(),
      lastSplashShown: lastSplashShown ? new Date(parseInt(lastSplashShown)).toLocaleString() : 'Jamais',
      sessionStart: sessionStart ? new Date(parseInt(sessionStart)).toLocaleString() : 'Aucune',
      timeSinceLastSplash: lastSplashShown ? Math.round((now - parseInt(lastSplashShown)) / 1000 / 60) + ' minutes' : 'N/A',
      timeSinceSessionStart: sessionStart ? Math.round((now - parseInt(sessionStart)) / 1000 / 60) + ' minutes' : 'N/A',
      shouldShowSplash: {
        newLogin: SplashService.shouldShowSplash(true),
        refresh: SplashService.shouldShowSplash(false)
      },
      hasActiveSession: SplashService.hasActiveSession(),
      isLikelyPageRefresh: SplashService.isLikelyPageRefresh(),
      isPageRefresh: SplashService.isPageRefresh(),
      performanceNavigation: typeof window !== 'undefined' && window.performance ?
        (window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming)?.type : 'N/A'
    }

    console.table(status)
    return status
  },

  /**
   * Force l'affichage du splash pour une nouvelle connexion
   */
  forceNewLogin: () => {
    SplashService.forceShowSplashForNewLogin()
    console.log('🚀 Splash forcé pour nouvelle connexion')
  }
}

// Exposer dans window pour les tests en console
if (typeof window !== 'undefined') {
  window.splashDebug = splashDebug
}

/**
 * Instructions d'utilisation dans la console :
 * 
 * // Voir le statut complet
 * window.splashDebug.getStatus()
 * 
 * // Tester si le splash doit s'afficher
 * window.splashDebug.shouldShow(true)  // nouvelle connexion
 * window.splashDebug.shouldShow(false) // rafraîchissement
 * 
 * // Simuler des actions
 * window.splashDebug.markShown()       // marquer splash affiché
 * window.splashDebug.markSessionStart() // démarrer session
 * window.splashDebug.clearSession()    // nettoyer session
 * window.splashDebug.reset()           // tout réinitialiser
 */
