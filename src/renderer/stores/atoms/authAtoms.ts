import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import { authService } from '../../services/AuthenticationService'
import { SplashService } from '../../services/SplashService'
import { User, LoginRequest } from '../../../shared/types/database'

// Interface de compatibilité avec l'ancien système
export interface LegacyUser {
  id: string
  username: string
  displayName: string
  email: string
  role: 'admin' | 'user' | 'guest'
  avatar?: string
}

// Fonction de conversion entre les types
const convertToLegacyUser = (user: User): LegacyUser => ({
  id: user.id!.toString(),
  username: user.username,
  displayName: user.displayName,
  email: user.email,
  role: user.role === 'admin' ? 'admin' :
        user.role === 'moderator' ? 'user' :
        user.role === 'user' ? 'user' : 'guest',
  avatar: user.avatar
})

export interface AuthState {
  isAuthenticated: boolean
  user: LegacyUser | null
  token: string | null
}

// État d'authentification persistant dans le localStorage
const _authAtom = atomWithStorage<AuthState>('auth', {
  isAuthenticated: authService.isAuthenticated(),
  user: null, // Sera chargé de manière asynchrone
  token: authService.getToken(),
})

// Atom avec logs pour tracer les changements
export const authAtom = atom(
  (get) => {
    const state = get(_authAtom)
    console.log('🔍 [AUTH] Lecture authAtom:', {
      isAuthenticated: state.isAuthenticated,
      hasUser: !!state.user,
      hasToken: !!state.token,
      timestamp: new Date().toISOString()
    })
    return state
  },
  (get, set, update: AuthState) => {
    console.log('🔍 [AUTH] Écriture authAtom:', {
      previous: get(_authAtom),
      new: update,
      timestamp: new Date().toISOString()
    })
    set(_authAtom, update)
  }
)

// État pour gérer l'affichage du splash screen après connexion
// Utilise le localStorage pour persister l'état entre les sessions
// Initialisé à true pour éviter l'affichage par défaut (sera géré par la logique)
export const splashCompletedAtom = atomWithStorage('splash_completed', true)

// Atom pour initialiser l'authentification au démarrage
export const initAuthAtom = atom(
  null,
  async (get, set) => {
    console.log('🔍 [AUTH] Début initAuthAtom...')
    
    try {
      // Vérifier l'état initial
      const initialState = get(_authAtom)
      console.log('🔍 [AUTH] État initial authAtom:', initialState)
      
      // Vérifier le localStorage directement
      const localStorageAuth = localStorage.getItem('auth')
      console.log('🔍 [AUTH] localStorage auth:', localStorageAuth)
      
      // Vérifier les tokens dans localStorage
      const datatecSession = localStorage.getItem('datatec_session')
      console.log('🔍 [AUTH] datatec_session:', datatecSession)
      
      // Initialiser le service si nécessaire
      if (!authService.isInitialized()) {
        console.log('🔍 [AUTH] Initialisation du service...')
        await authService.initialize()
      }
      
      const currentUser = await authService.getCurrentUser()
      console.log('🔍 [AUTH] getCurrentUser result:', currentUser ? 'User found' : 'No user')
      
      if (currentUser) {
        const newState = {
          isAuthenticated: true,
          user: convertToLegacyUser(currentUser),
          token: authService.getToken()
        }
        console.log('🔍 [AUTH] Setting authenticated state:', newState)
        set(_authAtom, newState)
      } else {
        const newState = {
          isAuthenticated: false,
          user: null,
          token: null
        }
        console.log('🔍 [AUTH] Setting unauthenticated state:', newState)
        set(_authAtom, newState)
      }
    } catch (error) {
      console.error('❌ [AUTH] Erreur lors de l\'initialisation de l\'authentification:', error)
      const errorState = {
        isAuthenticated: false,
        user: null,
        token: null
      }
      console.log('🔍 [AUTH] Setting error state:', errorState)
      set(_authAtom, errorState)
    }
    
    console.log('🔍 [AUTH] Fin initAuthAtom')
  }
)

// Atom pour gérer la connexion
export const loginAtom = atom(
  null,
  async (get, set, loginData: LoginRequest) => {
    try {
      const result = await authService.login(loginData)
      
      if (result.success && result.user) {
        set(_authAtom, {
          isAuthenticated: true,
          user: convertToLegacyUser(result.user),
          token: result.token || null
        })

        // Forcer l'affichage du splash APRÈS avoir défini l'authentification
        set(splashCompletedAtom, false)

        return result
      } else {
        set(_authAtom, {
          isAuthenticated: false,
          user: null,
          token: null
        })
        return result
      }
    } catch (error) {
      console.error('Erreur lors de la connexion:', error)
      set(_authAtom, {
        isAuthenticated: false,
        user: null,
        token: null
      })
      throw error
    }
  }
)

// Atom pour gérer la déconnexion
export const logoutAtom = atom(
  null,
  async (get, set) => {
    await authService.logout()
    
    set(_authAtom, {
      isAuthenticated: false,
      user: null,
      token: null,
    })
    
    // Réinitialiser le splash
    set(splashCompletedAtom, true)
  }
)

// Atom dérivé pour vérifier si l'utilisateur est connecté
export const isAuthenticatedAtom = atom(
  (get) => get(authAtom).isAuthenticated
)

// Atom dérivé pour obtenir l'utilisateur actuel
export const currentUserAtom = atom(
  (get) => get(authAtom).user
)
