// Nouveaux atoms d'authentification utilisant Dexie.js
// Remplacement progressif des anciens authAtoms.ts

import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import { userManagementService } from '../../services/UserManagementService'
import { migrationService } from '../../services/MigrationService'
import { SplashService } from '../../services/SplashService'
import { User, LoginRequest, AuthResult } from '../../../shared/types/database'

// Interface pour l'état d'authentification
export interface NewAuthState {
  isAuthenticated: boolean
  user: User | null
  token: string | null
  refreshToken: string | null
  isLoading: boolean
  error: string | null
  initialized: boolean
}

// État initial
const initialAuthState: NewAuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  refreshToken: null,
  isLoading: false,
  error: null,
  initialized: false
}

// Atom principal d'authentification (persistant)
export const newAuthAtom = atomWithStorage<NewAuthState>('new-auth', initialAuthState)

// Atom pour l'initialisation du système
export const authInitializationAtom = atom(
  (get) => get(newAuthAtom).initialized,
  async (get, set) => {
    const currentState = get(newAuthAtom)
    
    if (currentState.initialized) return

    set(newAuthAtom, { ...currentState, isLoading: true })

    try {
      // Vérifier si la migration est nécessaire
      const migrationCompleted = await migrationService.isMigrationCompleted()
      
      if (!migrationCompleted) {
        console.log('🔄 Migration nécessaire, démarrage automatique...')
        const migrationResult = await migrationService.migrateFromOldSystem()
        
        if (!migrationResult.success) {
          throw new Error(`Migration échouée: ${migrationResult.errors.join(', ')}`)
        }
        
        console.log(`✅ Migration réussie: ${migrationResult.migratedUsers} utilisateurs migrés`)
      }

      // Initialiser le système de gestion des utilisateurs
      await userManagementService.initialize()

      // Vérifier s'il y a un token valide en storage
      const storedAuth = get(newAuthAtom)
      if (storedAuth.token) {
        const user = await userManagementService.validateSession(storedAuth.token)
        if (user) {
          set(newAuthAtom, {
            ...storedAuth,
            isAuthenticated: true,
            user,
            isLoading: false,
            error: null,
            initialized: true
          })
          return
        }
      }

      // Pas de session valide
      set(newAuthAtom, {
        ...initialAuthState,
        initialized: true,
        isLoading: false
      })

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error)
      set(newAuthAtom, {
        ...initialAuthState,
        initialized: true,
        isLoading: false,
        error: `Erreur d'initialisation: ${error}`
      })
    }
  }
)

// Atom pour la connexion
export const loginAtom = atom(
  null,
  async (get, set, credentials: LoginRequest): Promise<AuthResult> => {
    const currentState = get(newAuthAtom)
    
    set(newAuthAtom, { ...currentState, isLoading: true, error: null })

    try {
      const result = await userManagementService.login(credentials)

      if (result.success && result.user && result.token) {
        // Préparer et forcer l'affichage du splash pour cette nouvelle connexion
        SplashService.forceShowSplashForNewLogin()

        set(newAuthAtom, {
          isAuthenticated: true,
          user: result.user,
          token: result.token,
          refreshToken: result.refreshToken || null,
          isLoading: false,
          error: null,
          initialized: true
        })

        // Forcer l'affichage du splash APRÈS avoir défini l'authentification
        set(splashCompletedAtom, false)
      } else {
        set(newAuthAtom, {
          ...currentState,
          isLoading: false,
          error: result.error || 'Erreur de connexion'
        })
      }

      return result

    } catch (error) {
      const errorMessage = `Erreur de connexion: ${error}`
      set(newAuthAtom, {
        ...currentState,
        isLoading: false,
        error: errorMessage
      })

      return {
        success: false,
        error: errorMessage,
        errorCode: 'LOGIN_ERROR'
      }
    }
  }
)

// Atom pour la déconnexion
export const logoutAtom = atom(
  null,
  async (get, set): Promise<boolean> => {
    const currentState = get(newAuthAtom)
    
    if (!currentState.token) {
      set(newAuthAtom, initialAuthState)
      return true
    }

    set(newAuthAtom, { ...currentState, isLoading: true })

    try {
      const success = await userManagementService.logout(currentState.token)
      
      // Réinitialiser l'état dans tous les cas
      set(newAuthAtom, {
        ...initialAuthState,
        initialized: true
      })

      // Nettoyer la session et réinitialiser l'état du splash
      SplashService.clearSession()
      set(splashCompletedAtom, false)

      return success

    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error)

      // Réinitialiser l'état même en cas d'erreur
      set(newAuthAtom, {
        ...initialAuthState,
        initialized: true
      })

      // Nettoyer la session et réinitialiser l'état du splash même en cas d'erreur
      SplashService.clearSession()
      set(splashCompletedAtom, false)

      return false
    }
  }
)

// Atom pour vérifier les permissions
export const checkPermissionAtom = atom(
  null,
  async (get, set, params: { resource: string; action: string }): Promise<boolean> => {
    const currentState = get(newAuthAtom)
    
    if (!currentState.isAuthenticated || !currentState.user) {
      return false
    }

    try {
      return await userManagementService.checkPermission(
        currentState.user.id!,
        params.resource,
        params.action
      )
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error)
      return false
    }
  }
)

// Atom pour obtenir les permissions de l'utilisateur
export const userPermissionsAtom = atom(async (get) => {
  const currentState = get(newAuthAtom)
  
  if (!currentState.isAuthenticated || !currentState.user) {
    return []
  }

  try {
    return await userManagementService.getUserPermissions(currentState.user.id!)
  } catch (error) {
    console.error('Erreur lors de la récupération des permissions:', error)
    return []
  }
})

// Atom pour obtenir les rôles de l'utilisateur
export const userRolesAtom = atom(async (get) => {
  const currentState = get(newAuthAtom)
  
  if (!currentState.isAuthenticated || !currentState.user) {
    return []
  }

  try {
    return await userManagementService.getUserRoles(currentState.user.id!)
  } catch (error) {
    console.error('Erreur lors de la récupération des rôles:', error)
    return []
  }
})

// Atoms dérivés pour la compatibilité avec l'ancien système
export const isAuthenticatedAtom = atom((get) => get(newAuthAtom).isAuthenticated)
export const currentUserAtom = atom((get) => get(newAuthAtom).user)
export const tokenAtom = atom((get) => get(newAuthAtom).token)
export const authLoadingAtom = atom((get) => get(newAuthAtom).isLoading)
export const authErrorAtom = atom((get) => get(newAuthAtom).error)
export const authInitializedAtom = atom((get) => get(newAuthAtom).initialized)

// Atom pour les statistiques utilisateur (admin seulement)
export const userStatsAtom = atom(async (get) => {
  const currentState = get(newAuthAtom)
  
  if (!currentState.isAuthenticated || !currentState.user || currentState.user.role !== 'admin') {
    return null
  }

  try {
    return await userManagementService.getSystemStats()
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error)
    return null
  }
})

// Atom pour la gestion du splash screen (compatibilité)
// Utilise le localStorage pour persister l'état entre les sessions
// Initialisé à true pour éviter l'affichage par défaut (sera géré par la logique)
export const splashCompletedAtom = atomWithStorage('splash_completed', true)

// Atom pour réinitialiser l'état d'authentification
export const resetAuthAtom = atom(
  null,
  (get, set) => {
    set(newAuthAtom, {
      ...initialAuthState,
      initialized: get(newAuthAtom).initialized
    })
    set(splashCompletedAtom, false)
  }
)

// Atom pour forcer la réinitialisation complète
export const forceResetAtom = atom(
  null,
  (get, set) => {
    set(newAuthAtom, initialAuthState)
    set(splashCompletedAtom, false)
  }
)

// Utilitaires pour la migration progressive

// Atom pour vérifier si on utilise le nouveau système
export const usingNewAuthSystemAtom = atom(true) // Toujours true pour le nouveau système

// Atom pour obtenir des informations de debug
export const authDebugInfoAtom = atom(async (get) => {
  const currentState = get(newAuthAtom)
  
  try {
    const systemInfo = await userManagementService.getSystemInfo()
    const migrationStatus = await migrationService.getMigrationStatus()
    
    return {
      authState: currentState,
      systemInfo,
      migrationStatus,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      authState: currentState,
      error: `Erreur debug: ${error}`,
      timestamp: new Date().toISOString()
    }
  }
})

// Fonction utilitaire pour basculer vers le nouveau système
export const switchToNewAuthSystem = async () => {
  console.log('🔄 Basculement vers le nouveau système d\'authentification...')
  
  try {
    // Vérifier si la migration est nécessaire
    const migrationCompleted = await migrationService.isMigrationCompleted()
    
    if (!migrationCompleted) {
      console.log('⚠️ Migration nécessaire avant le basculement')
      return false
    }

    console.log('✅ Nouveau système d\'authentification activé')
    return true

  } catch (error) {
    console.error('❌ Erreur lors du basculement:', error)
    return false
  }
}

// Export des types pour la compatibilité
export type { NewAuthState as AuthState, User }
