// Interface commune pour la gestion de base de données
// Compatible Desktop (Electron) et Web

import {
  User,
  UserCredentials,
  Session,
  Role,
  Permission,
  UserRole,
  AuditLog,
  CreateUserRequest,
  LoginRequest,
  LoginResponse,
  AuthResult,
  DatabaseConfig,
  UserStats
} from '../../shared/types/database'

export interface IDatabaseManager {
  // Initialisation
  initialize(config?: DatabaseConfig): Promise<void>
  close(): Promise<void>
  
  // Gestion des utilisateurs
  createUser(userData: CreateUserRequest): Promise<User>
  getUserById(id: number): Promise<User | null>
  getUserByUsername(username: string): Promise<User | null>
  getUserByEmail(email: string): Promise<User | null>
  updateUser(id: number, updates: Partial<User>): Promise<User>
  deleteUser(id: number): Promise<boolean>
  listUsers(filters?: UserFilters): Promise<User[]>
  getUserStats(): Promise<UserStats>
  
  // Authentification
  login(credentials: LoginRequest): Promise<AuthResult>
  logout(sessionId: number): Promise<boolean>
  validateSession(token: string): Promise<Session | null>
  refreshSession(refreshToken: string): Promise<LoginResponse | null>
  
  // Gestion des mots de passe
  changePassword(userId: number, oldPassword: string, newPassword: string): Promise<boolean>
  resetPassword(userId: number, newPassword: string): Promise<boolean>
  validatePassword(userId: number, password: string): Promise<boolean>
  
  // Gestion des sessions
  createSession(userId: number, sessionData: Partial<Session>): Promise<Session>
  getSession(sessionId: number): Promise<Session | null>
  getSessionByToken(token: string): Promise<Session | null>
  getUserSessions(userId: number): Promise<Session[]>
  deleteSession(sessionId: number): Promise<boolean>
  deleteUserSessions(userId: number): Promise<number>
  cleanExpiredSessions(): Promise<number>
  
  // Gestion des rôles
  createRole(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role>
  getRole(roleId: number): Promise<Role | null>
  getRoleByName(name: string): Promise<Role | null>
  updateRole(roleId: number, updates: Partial<Role>): Promise<Role>
  deleteRole(roleId: number): Promise<boolean>
  listRoles(): Promise<Role[]>
  
  // Gestion des permissions
  createPermission(permissionData: Omit<Permission, 'id'>): Promise<Permission>
  getPermission(permissionId: number): Promise<Permission | null>
  updatePermission(permissionId: number, updates: Partial<Permission>): Promise<Permission>
  deletePermission(permissionId: number): Promise<boolean>
  listPermissions(): Promise<Permission[]>
  
  // Association utilisateur-rôle
  assignRole(userId: number, roleId: number, assignedBy: number): Promise<UserRole>
  removeRole(userId: number, roleId: number): Promise<boolean>
  getUserRoles(userId: number): Promise<Role[]>
  getRoleUsers(roleId: number): Promise<User[]>
  
  // Vérification des permissions
  checkPermission(userId: number, resource: string, action: string): Promise<boolean>
  getUserPermissions(userId: number): Promise<Permission[]>
  
  // Audit et logs
  logAction(auditData: Omit<AuditLog, 'id' | 'timestamp'>): Promise<AuditLog>
  getAuditLogs(filters?: AuditFilters): Promise<AuditLog[]>
  cleanOldAuditLogs(olderThanDays: number): Promise<number>
  
  // Maintenance
  backup(): Promise<string>
  restore(backupData: string): Promise<boolean>
  vacuum(): Promise<void>
  getDbSize(): Promise<number>
}

// Filtres pour les requêtes
export interface UserFilters {
  role?: string
  isActive?: boolean
  createdAfter?: Date
  createdBefore?: Date
  lastLoginAfter?: Date
  search?: string
  limit?: number
  offset?: number
}

export interface AuditFilters {
  userId?: number
  action?: string
  resource?: string
  success?: boolean
  fromDate?: Date
  toDate?: Date
  limit?: number
  offset?: number
}

// Configuration spécifique par plateforme
export interface PlatformConfig {
  platform: 'desktop' | 'web'
  encryptionEnabled: boolean
  backupPath?: string
  maxBackups?: number
  compressionEnabled?: boolean
}

// Événements de base de données
export interface DatabaseEvents {
  userCreated: (user: User) => void
  userUpdated: (user: User) => void
  userDeleted: (userId: number) => void
  userLoggedIn: (user: User, session: Session) => void
  userLoggedOut: (userId: number, sessionId: number) => void
  sessionExpired: (sessionId: number) => void
  permissionDenied: (userId: number, resource: string, action: string) => void
  auditLogCreated: (log: AuditLog) => void
}

// Erreurs personnalisées
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message)
    this.name = 'DatabaseError'
  }
}

export class AuthenticationError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'AUTH_ERROR', details)
    this.name = 'AuthenticationError'
  }
}

export class PermissionError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'PERMISSION_ERROR', details)
    this.name = 'PermissionError'
  }
}

export class ValidationError extends DatabaseError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details)
    this.name = 'ValidationError'
  }
}

// Utilitaires de validation
export const ValidationUtils = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },
  
  isValidUsername: (username: string): boolean => {
    const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/
    return usernameRegex.test(username)
  },
  
  isValidPassword: (password: string): boolean => {
    // Au moins 8 caractères, une majuscule, une minuscule, un chiffre
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
    return passwordRegex.test(password)
  },
  
  sanitizeInput: (input: string): string => {
    return input.trim().replace(/[<>]/g, '')
  }
}

// Configuration par défaut
export const DEFAULT_DATABASE_CONFIG: DatabaseConfig = {
  name: 'datatec-users',
  version: 1,
  backupEnabled: true,
  auditEnabled: true,
  sessionTimeout: 1440, // 24 heures
  maxLoginAttempts: 5,
  lockoutDuration: 30 // 30 minutes
}
