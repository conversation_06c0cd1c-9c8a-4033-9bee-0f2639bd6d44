// Test de la base de données Dexie.js
// Ce fichier sera supprimé après validation

import { userDatabase } from './DexieUserDatabase'

export class DatabaseTest {
  static async runTests(): Promise<boolean> {
    console.log('🧪 Début des tests de base de données...')
    
    try {
      // Test 1: Initialisation de la base de données
      console.log('📊 Test 1: Initialisation de la base de données')
      await userDatabase.open()
      console.log('✅ Base de données ouverte avec succès')

      // Test 2: Vérification des tables
      console.log('📊 Test 2: Vérification des tables')
      const tables = userDatabase.tables.map(table => table.name)
      const expectedTables = ['users', 'credentials', 'sessions', 'roles', 'permissions', 'userRoles', 'auditLogs']
      
      for (const expectedTable of expectedTables) {
        if (!tables.includes(expectedTable)) {
          throw new Error(`Table manquante: ${expectedTable}`)
        }
      }
      console.log('✅ Toutes les tables sont présentes:', tables)

      // Test 3: Initialisation des données par défaut
      console.log('📊 Test 3: Initialisation des données par défaut')
      await userDatabase.initializeDefaultData()
      console.log('✅ Données par défaut initialisées')

      // Test 4: Vérification des données créées
      console.log('📊 Test 4: Vérification des données créées')
      const stats = await userDatabase.getStats()
      console.log('📈 Statistiques de la base de données:', stats)

      if (stats.users === 0) {
        throw new Error('Aucun utilisateur créé')
      }
      if (stats.roles === 0) {
        throw new Error('Aucun rôle créé')
      }
      if (stats.permissions === 0) {
        throw new Error('Aucune permission créée')
      }

      // Test 5: Vérification de l'utilisateur admin
      console.log('📊 Test 5: Vérification de l\'utilisateur admin')
      const adminUser = await userDatabase.users.where('username').equals('admin').first()
      if (!adminUser) {
        throw new Error('Utilisateur admin non trouvé')
      }
      console.log('✅ Utilisateur admin trouvé:', {
        id: adminUser.id,
        username: adminUser.username,
        email: adminUser.email,
        role: adminUser.role
      })

      // Test 6: Vérification des credentials admin
      console.log('📊 Test 6: Vérification des credentials admin')
      const adminCredentials = await userDatabase.credentials.where('userId').equals(adminUser.id!).first()
      if (!adminCredentials) {
        throw new Error('Credentials admin non trouvés')
      }
      console.log('✅ Credentials admin trouvés (hash présent:', !!adminCredentials.passwordHash, ')')

      // Test 7: Test de nettoyage
      console.log('📊 Test 7: Test de nettoyage des données expirées')
      const cleanupResult = await userDatabase.cleanupExpiredData()
      console.log('✅ Nettoyage effectué:', cleanupResult)

      console.log('🎉 Tous les tests sont passés avec succès !')
      return true

    } catch (error) {
      console.error('❌ Erreur lors des tests:', error)
      return false
    }
  }

  static async resetDatabase(): Promise<void> {
    console.log('🗑️ Réinitialisation de la base de données...')
    try {
      await userDatabase.delete()
      console.log('✅ Base de données supprimée')
      
      // Rouvrir la base de données
      await userDatabase.open()
      console.log('✅ Base de données recréée')
      
      // Réinitialiser les données par défaut
      await userDatabase.initializeDefaultData()
      console.log('✅ Données par défaut recréées')
      
    } catch (error) {
      console.error('❌ Erreur lors de la réinitialisation:', error)
      throw error
    }
  }

  static async showDatabaseInfo(): Promise<void> {
    console.log('📊 Informations de la base de données:')
    
    try {
      const stats = await userDatabase.getStats()
      console.table(stats)

      console.log('\n👥 Utilisateurs:')
      const users = await userDatabase.users.toArray()
      console.table(users.map(u => ({
        id: u.id,
        username: u.username,
        email: u.email,
        role: u.role,
        isActive: u.isActive,
        createdAt: u.createdAt.toISOString()
      })))

      console.log('\n🛡️ Rôles:')
      const roles = await userDatabase.roles.toArray()
      console.table(roles.map(r => ({
        id: r.id,
        name: r.name,
        displayName: r.displayName,
        isSystem: r.isSystem,
        permissionsCount: r.permissions.length
      })))

      console.log('\n🔑 Permissions:')
      const permissions = await userDatabase.permissions.toArray()
      console.table(permissions.map(p => ({
        id: p.id,
        resource: p.resource,
        action: p.action,
        description: p.description
      })))

      console.log('\n📝 Derniers logs d\'audit:')
      const recentLogs = await userDatabase.auditLogs
        .orderBy('timestamp')
        .reverse()
        .limit(5)
        .toArray()
      
      console.table(recentLogs.map(log => ({
        id: log.id,
        action: log.action,
        resource: log.resource,
        success: log.success,
        timestamp: log.timestamp.toISOString()
      })))

    } catch (error) {
      console.error('❌ Erreur lors de l\'affichage des informations:', error)
    }
  }
}

// Fonction utilitaire pour tester depuis la console du navigateur
(window as any).testDatabase = async () => {
  return await DatabaseTest.runTests()
}

(window as any).resetDatabase = async () => {
  return await DatabaseTest.resetDatabase()
}

(window as any).showDatabaseInfo = async () => {
  return await DatabaseTest.showDatabaseInfo()
}

export default DatabaseTest
