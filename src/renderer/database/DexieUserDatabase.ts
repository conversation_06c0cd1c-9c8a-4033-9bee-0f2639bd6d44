// Base de données Dexie.js pour la gestion des utilisateurs
// Compatible Desktop (Electron) et Web

import Dexie, { Table } from 'dexie'
import {
  User,
  UserCredentials,
  Session,
  Role,
  Permission,
  UserRole,
  AuditLog,
  DatabaseConfig,
  SYSTEM_ROLES,
  SYSTEM_PERMISSIONS
} from '../../shared/types/database'

export class DexieUserDatabase extends Dexie {
  // Tables de la base de données
  users!: Table<User, number>
  credentials!: Table<UserCredentials, number>
  sessions!: Table<Session, number>
  roles!: Table<Role, number>
  permissions!: Table<Permission, number>
  userRoles!: Table<UserRole, number>
  auditLogs!: Table<AuditLog, number>

  constructor(databaseName: string = 'DataTecUserDB') {
    super(databaseName)

    // Version 1 : Schéma initial
    this.version(1).stores({
      users: '++id, username, email, role, isActive, createdAt, updatedAt, lastLogin',
      credentials: '++id, userId, algorithm, createdAt, updatedAt',
      sessions: '++id, userId, token, refreshToken, expiresAt, refreshExpiresAt, isActive, createdAt, lastActivity',
      roles: '++id, name, isSystem, createdAt, updatedAt',
      permissions: '++id, resource, action',
      userRoles: '++id, userId, roleId, assignedAt, assignedBy, expiresAt, isActive',
      auditLogs: '++id, userId, sessionId, action, resource, resourceId, success, timestamp'
    })

    // Version 2 : Ajout d'index pour les performances (future migration)
    this.version(2).stores({
      users: '++id, username, email, role, isActive, createdAt, updatedAt, lastLogin, displayName',
      credentials: '++id, userId, algorithm, createdAt, updatedAt',
      sessions: '++id, userId, token, refreshToken, expiresAt, refreshExpiresAt, isActive, createdAt, lastActivity, ipAddress',
      roles: '++id, name, isSystem, createdAt, updatedAt, displayName',
      permissions: '++id, resource, action, [resource+action]',
      userRoles: '++id, userId, roleId, assignedAt, assignedBy, expiresAt, isActive, [userId+roleId]',
      auditLogs: '++id, userId, sessionId, action, resource, resourceId, success, timestamp, [userId+timestamp], [action+timestamp]'
    }).upgrade(trans => {
      // Migration des données si nécessaire
      console.log('Upgrading database to version 2...')
      return trans.table('permissions').toCollection().modify(permission => {
        // Ajouter des champs manquants si nécessaire
        if (!permission.description) {
          permission.description = `Permission ${permission.action} sur ${permission.resource}`
        }
      })
    })

    // Hooks pour l'audit automatique
    this.setupAuditHooks()
    
    // Hooks pour la validation des données
    this.setupValidationHooks()
  }

  // Configuration des hooks d'audit automatique
  private setupAuditHooks(): void {
    // Audit des créations d'utilisateurs
    this.users.hook('creating', (primKey, obj, trans) => {
      const now = new Date()
      obj.createdAt = now
      obj.updatedAt = now
      
      // Log de création (sera exécuté après la transaction)
      trans.on('complete', () => {
        this.logAction({
          action: 'USER_CREATED',
          resource: 'user',
          resourceId: obj.id?.toString(),
          details: { username: obj.username, role: obj.role },
          success: true
        })
      })
    })

    // Audit des modifications d'utilisateurs
    this.users.hook('updating', (modifications, primKey, obj, trans) => {
      modifications.updatedAt = new Date()
      
      trans.on('complete', () => {
        this.logAction({
          userId: obj.id,
          action: 'USER_UPDATED',
          resource: 'user',
          resourceId: primKey.toString(),
          details: modifications,
          success: true
        })
      })
    })

    // Audit des suppressions d'utilisateurs
    this.users.hook('deleting', (primKey, obj, trans) => {
      trans.on('complete', () => {
        this.logAction({
          action: 'USER_DELETED',
          resource: 'user',
          resourceId: primKey.toString(),
          details: { username: obj.username },
          success: true
        })
      })
    })

    // Audit des sessions
    this.sessions.hook('creating', (primKey, obj, trans) => {
      const now = new Date()
      obj.createdAt = now
      obj.lastActivity = now
    })

    this.sessions.hook('updating', (modifications, primKey, obj, trans) => {
      if (modifications.isActive === false) {
        trans.on('complete', () => {
          this.logAction({
            userId: obj.userId,
            sessionId: obj.id,
            action: 'SESSION_ENDED',
            resource: 'session',
            resourceId: primKey.toString(),
            success: true
          })
        })
      }
    })
  }

  // Configuration des hooks de validation
  private setupValidationHooks(): void {
    // Validation des utilisateurs
    this.users.hook('creating', (primKey, obj, trans) => {
      this.validateUser(obj)
    })

    this.users.hook('updating', (modifications, primKey, obj, trans) => {
      if (modifications.email) {
        this.validateEmail(modifications.email)
      }
      if (modifications.username) {
        this.validateUsername(modifications.username)
      }
    })

    // Validation des sessions
    this.sessions.hook('creating', (primKey, obj, trans) => {
      if (!obj.expiresAt || obj.expiresAt <= new Date()) {
        throw new Error('Session expiration date must be in the future')
      }
    })
  }

  // Méthodes de validation
  private validateUser(user: User): void {
    if (!user.username || user.username.length < 3) {
      throw new Error('Username must be at least 3 characters long')
    }
    
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error('Valid email address is required')
    }
    
    if (!user.displayName || user.displayName.length < 1) {
      throw new Error('Display name is required')
    }
  }

  private validateEmail(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format')
    }
  }

  private validateUsername(username: string): void {
    const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/
    if (!usernameRegex.test(username)) {
      throw new Error('Username must be 3-20 characters and contain only letters, numbers, underscore, or dash')
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Initialisation de la base de données avec données par défaut
  async initializeDefaultData(): Promise<void> {
    try {
      await this.transaction('rw', [this.roles, this.permissions, this.users, this.credentials, this.userRoles], async () => {
        // Créer les permissions système
        const permissionIds = new Map<string, number>()
        
        for (const [key, permission] of Object.entries(SYSTEM_PERMISSIONS)) {
          const existingPermission = await this.permissions
            .where('[resource+action]')
            .equals([permission.resource, permission.action])
            .first()
          
          if (!existingPermission) {
            const id = await this.permissions.add({
              resource: permission.resource,
              action: permission.action,
              description: `Permission ${permission.action} sur ${permission.resource}`
            })
            permissionIds.set(key, id as number)
          } else {
            permissionIds.set(key, existingPermission.id!)
          }
        }

        // Créer les rôles système
        const roleIds = new Map<string, number>()
        
        for (const [key, roleData] of Object.entries(SYSTEM_ROLES)) {
          const existingRole = await this.roles.where('name').equals(roleData.name).first()
          
          if (!existingRole) {
            const rolePermissions = roleData.permissions.map(p => ({
              resource: p.resource,
              action: p.action,
              description: `Permission ${p.action} sur ${p.resource}`
            }))

            const id = await this.roles.add({
              name: roleData.name,
              displayName: roleData.displayName,
              description: roleData.description,
              permissions: rolePermissions,
              isSystem: true,
              createdAt: new Date(),
              updatedAt: new Date()
            })
            roleIds.set(key, id as number)
          } else {
            roleIds.set(key, existingRole.id!)
          }
        }

        // Note: L'admin sera créé après cette transaction

        console.log('✅ Base de données initialisée avec les données par défaut')
      })

      // Créer l'utilisateur admin APRÈS la transaction principale
      await this.createAdminIfNotExists()

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de la base de données:', error)
      throw error
    }
  }

  // Méthode pour créer l'admin en dehors des transactions
  private async createAdminIfNotExists(): Promise<void> {
    try {
      const adminExists = await this.users.where('username').equals('admin').first()
      if (!adminExists) {
        console.log('🔧 Création de l\'utilisateur admin par défaut...')

        // Préparer le hash du mot de passe en dehors de la transaction
        const bcrypt = await import('bcryptjs')
        const salt = await bcrypt.genSalt(12)
        const passwordHash = await bcrypt.hash('admin123', salt)

        // Créer l'admin dans une transaction séparée et courte
        await this.transaction('rw', [this.users, this.credentials], async () => {
          const userId = await this.users.add({
            username: 'admin',
            email: '<EMAIL>',
            displayName: 'Administrateur',
            role: 'admin',
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          })

          await this.credentials.add({
            userId: userId as number,
            passwordHash,
            salt,
            algorithm: 'bcrypt',
            iterations: 12,
            createdAt: new Date(),
            updatedAt: new Date()
          })
        })

        console.log('✅ Utilisateur admin créé : admin / admin123')
      } else {
        console.log('✅ Utilisateur admin déjà présent')
      }
    } catch (error) {
      console.error('❌ Erreur lors de la création de l\'admin:', error)
    }
  }

  // Création de l'utilisateur admin par défaut
  private async createDefaultAdmin(adminRoleId: number): Promise<void> {
    const bcrypt = await import('bcryptjs')
    const salt = await bcrypt.genSalt(12)
    const passwordHash = await bcrypt.hash('admin123', salt)

    const userId = await this.users.add({
      username: 'admin',
      email: '<EMAIL>',
      displayName: 'Administrateur',
      role: 'admin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    })

    await this.credentials.add({
      userId: userId as number,
      passwordHash,
      salt,
      algorithm: 'bcrypt',
      iterations: 12,
      createdAt: new Date(),
      updatedAt: new Date()
    })

    await this.userRoles.add({
      userId: userId as number,
      roleId: adminRoleId,
      assignedAt: new Date(),
      assignedBy: userId as number,
      isActive: true
    })
  }

  // Méthode utilitaire pour les logs d'audit
  private async logAction(auditData: Omit<AuditLog, 'id' | 'timestamp'>): Promise<void> {
    try {
      await this.auditLogs.add({
        ...auditData,
        timestamp: new Date()
      })
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du log d\'audit:', error)
    }
  }

  // Nettoyage des données expirées
  async cleanupExpiredData(): Promise<{ sessions: number; auditLogs: number }> {
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    const expiredSessions = await this.sessions
      .where('expiresAt')
      .below(now)
      .delete()

    const oldAuditLogs = await this.auditLogs
      .where('timestamp')
      .below(thirtyDaysAgo)
      .delete()

    return {
      sessions: expiredSessions,
      auditLogs: oldAuditLogs
    }
  }

  // Statistiques de la base de données
  async getStats(): Promise<{
    users: number
    activeUsers: number
    sessions: number
    activeSessions: number
    auditLogs: number
    roles: number
    permissions: number
  }> {
    const [users, activeUsers, sessions, activeSessions, auditLogs, roles, permissions] = await Promise.all([
      this.users.count(),
      this.users.where('isActive').equals(true).count(),
      this.sessions.count(),
      this.sessions.where('isActive').equals(true).and(s => s.expiresAt > new Date()).count(),
      this.auditLogs.count(),
      this.roles.count(),
      this.permissions.count()
    ])

    return {
      users,
      activeUsers,
      sessions,
      activeSessions,
      auditLogs,
      roles,
      permissions
    }
  }
}

// Instance singleton de la base de données
export const userDatabase = new DexieUserDatabase()

// Export par défaut
export default userDatabase
