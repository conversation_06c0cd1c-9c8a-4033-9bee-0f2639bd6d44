// Configuration de la base de données utilisateurs
// Compatible Desktop (Electron) et Web

import { DatabaseConfig } from '../../shared/types/database'
import platform from '../platform'

export class DatabaseConfigManager {
  private static instance: DatabaseConfigManager
  private config: DatabaseConfig | null = null

  private constructor() {}

  public static getInstance(): DatabaseConfigManager {
    if (!DatabaseConfigManager.instance) {
      DatabaseConfigManager.instance = new DatabaseConfigManager()
    }
    return DatabaseConfigManager.instance
  }

  // Configuration par défaut
  private getDefaultConfig(): DatabaseConfig {
    return {
      name: 'DataTecUserDB',
      version: 1,
      encryptionKey: this.generateEncryptionKey(),
      backupEnabled: true,
      auditEnabled: true,
      sessionTimeout: platform.type === 'desktop' ? 1440 : 480, // 24h desktop, 8h web
      maxLoginAttempts: 5,
      lockoutDuration: 30 // 30 minutes
    }
  }

  // Génération d'une clé de chiffrement unique
  private generateEncryptionKey(): string {
    // En production, cette clé devrait être générée de manière plus sécurisée
    // et stockée de manière sécurisée (keychain sur macOS, credential manager sur Windows)
    const crypto = require('crypto-js')
    return crypto.lib.WordArray.random(256 / 8).toString()
  }

  // Chargement de la configuration
  public async loadConfig(): Promise<DatabaseConfig> {
    if (this.config) {
      return this.config
    }

    try {
      // Essayer de charger la configuration existante
      const storedConfig = await platform.getStoreValue('database-config')
      
      if (storedConfig) {
        this.config = {
          ...this.getDefaultConfig(),
          ...storedConfig
        }
      } else {
        // Créer une nouvelle configuration
        this.config = this.getDefaultConfig()
        await this.saveConfig()
      }

      return this.config
    } catch (error) {
      console.error('Erreur lors du chargement de la configuration de base de données:', error)
      // Utiliser la configuration par défaut en cas d'erreur
      this.config = this.getDefaultConfig()
      return this.config
    }
  }

  // Sauvegarde de la configuration
  public async saveConfig(): Promise<void> {
    if (!this.config) {
      throw new Error('Aucune configuration à sauvegarder')
    }

    try {
      await platform.setStoreValue('database-config', this.config)
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la configuration:', error)
      throw error
    }
  }

  // Mise à jour de la configuration
  public async updateConfig(updates: Partial<DatabaseConfig>): Promise<DatabaseConfig> {
    if (!this.config) {
      await this.loadConfig()
    }

    this.config = {
      ...this.config!,
      ...updates
    }

    await this.saveConfig()
    return this.config
  }

  // Obtenir la configuration actuelle
  public getConfig(): DatabaseConfig | null {
    return this.config
  }

  // Réinitialiser la configuration
  public async resetConfig(): Promise<DatabaseConfig> {
    this.config = this.getDefaultConfig()
    await this.saveConfig()
    return this.config
  }

  // Validation de la configuration
  public validateConfig(config: DatabaseConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config.name || config.name.length < 3) {
      errors.push('Le nom de la base de données doit contenir au moins 3 caractères')
    }

    if (config.version < 1) {
      errors.push('La version de la base de données doit être supérieure à 0')
    }

    if (config.sessionTimeout < 5) {
      errors.push('Le timeout de session doit être d\'au moins 5 minutes')
    }

    if (config.maxLoginAttempts < 1) {
      errors.push('Le nombre maximum de tentatives de connexion doit être d\'au moins 1')
    }

    if (config.lockoutDuration < 1) {
      errors.push('La durée de verrouillage doit être d\'au moins 1 minute')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // Configuration spécifique par plateforme
  public getPlatformSpecificConfig(): {
    platform: 'desktop' | 'web'
    features: {
      encryption: boolean
      backup: boolean
      compression: boolean
      audit: boolean
    }
    limits: {
      maxUsers: number
      maxSessions: number
      maxAuditLogs: number
    }
  } {
    const isDesktop = platform.type === 'desktop'

    return {
      platform: isDesktop ? 'desktop' : 'web',
      features: {
        encryption: true,
        backup: isDesktop, // Backup uniquement sur desktop
        compression: isDesktop,
        audit: true
      },
      limits: {
        maxUsers: isDesktop ? 10000 : 1000,
        maxSessions: isDesktop ? 1000 : 100,
        maxAuditLogs: isDesktop ? 100000 : 10000
      }
    }
  }

  // Informations sur la base de données
  public getDatabaseInfo(): {
    name: string
    version: number
    platform: string
    features: string[]
    estimatedSize: string
  } {
    const config = this.config || this.getDefaultConfig()
    const platformConfig = this.getPlatformSpecificConfig()

    const features = []
    if (platformConfig.features.encryption) features.push('Chiffrement')
    if (platformConfig.features.backup) features.push('Sauvegarde')
    if (platformConfig.features.compression) features.push('Compression')
    if (platformConfig.features.audit) features.push('Audit')

    return {
      name: config.name,
      version: config.version,
      platform: platformConfig.platform,
      features,
      estimatedSize: this.estimateSize(platformConfig.limits)
    }
  }

  // Estimation de la taille de la base de données
  private estimateSize(limits: { maxUsers: number; maxSessions: number; maxAuditLogs: number }): string {
    // Estimation approximative en KB
    const userSize = limits.maxUsers * 1 // ~1KB par utilisateur
    const sessionSize = limits.maxSessions * 0.5 // ~0.5KB par session
    const auditSize = limits.maxAuditLogs * 0.3 // ~0.3KB par log

    const totalKB = userSize + sessionSize + auditSize

    if (totalKB < 1024) {
      return `${Math.round(totalKB)} KB`
    } else {
      return `${Math.round(totalKB / 1024 * 10) / 10} MB`
    }
  }
}

// Instance singleton
export const databaseConfig = DatabaseConfigManager.getInstance()

// Export par défaut
export default databaseConfig
