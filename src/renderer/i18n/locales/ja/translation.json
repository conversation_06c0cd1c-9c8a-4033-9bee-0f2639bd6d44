{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] 保存, [Ctrl+Shift+Enter] 保存して再送信", "[Enter] send, [Shift+Enter] line break, [Ctrl+Enter] send without generating": "[Enter] 送信、[Shift+Enter] 改行、[Ctrl+Enter] 生成せずに送信", "{{count}} MCP servers imported": "{{count}} MCPサーバーがインポートされました", "About": "情報", "About Chatbox": "Chatboxについて", "about-introduction": "複数の先進的なAIモデルをサポートするユーザーフレンドリーなAIデスクトップクライアントで、最先端の人工知能技術を使いやすい生産性ツールに変えます。", "about-slogan": "AIを使って効率を上げましょう、仕事と学習の究極の共同作業者", "Access to all future premium feature updates": "全ての未来のプレミアム機能アップデートにアクセス", "Action": "アクション", "Activate License": "ライセンスを有効化", "Activating...": "有効化中...", "Add": "追加", "Add at least one model to check connection": "接続を確認するために、少なくとも1つのモデルを追加してください", "Add Custom Provider": "カスタムプロバイダーを追加", "Add Custom Server": "カスタムサーバーを追加", "Add MCP Server": "MCP Server を追加", "Add or Import": "追加またはインポート", "Add provider": "プロバイダーを追加", "Add Server": "サーバーを追加", "Add your first MCP server": "最初のMCPサーバーを追加", "advanced": "高度", "Advanced": "高度", "Advanced Mode": "高度モード", "AI Model Provider": "AIモデル提供者", "ai provider no implemented paint tips": "現在のAIモデルプロバイダー({{aiProvider}})は、描画機能をサポートしていません。現在、描画機能はChatbox AI、OpenAI、およびAzure OpenAIのみが提供しています。必要であれば、<0>設定に進んで</0>AIモデルプロバイダーを切り替えてください。", "AI Settings": "AI設定", "All data is stored locally, ensuring privacy and rapid access": "すべてのデータはローカルに保存され、プライバシーと迅速なアクセスを保証", "All major AI models in one subscription": "すべての主要なAIモデルを1つのサブスクリプションで使用", "All threads": "すべてのスレッド", "already existed": "すでに存在します", "An easy-to-use AI client app": "使いやすいAIクライアントアプリ", "An error occurred while processing your request. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "リクエストの処理中にエラーが発生しました。後でもう一度お試しください。このエラーが続く場合は、****************にメールを送信してください。", "An error occurred while sending the message.": "メッセージ送信中にエラーが発生しました。", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "構造化された思考プロセスを通じて、動的かつ内省的な問題解決のためのツールを提供するMCPサーバーの実装。", "An unknown error occurred. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "不明なエラーが発生しました。後でもう一度お試しください。このエラーが続く場合は、****************にメールを送信してください。", "any number key": "任意の数字キー", "api error tips": "{{aiProvider}}でエラーが発生しました。通常、設定の誤りやアカウントの問題によって引き起こされます。AI設定とアカウントステータスを確認するか、<0>ここをクリックしてFAQドキュメントを表示</0>してください。", "api host": "APIホスト", "API Host": "APIホスト", "api key": "APIキー", "API Key": "APIキー", "API KEY & License": "APIキーとライセンス", "API key invalid!": "APIキーが無効です！", "API Key is required to check connection": "APIキーは接続確認に必要です", "API Mode": "APIモード", "api path": "APIパス", "API Path": "API パス", "Are you sure you want to delete this server?": "このサーバーを削除してもよろしいですか？", "Arguments": "引数", "assistant": "アシスタント", "Attach Image": "画像を添付", "Attach Link": "リンクを添付", "Auther Message": "私は自分のためにChatboxを作りましたが、多くの人々がそれを楽しんでいるのを見るのは素晴らしいことです！開発を支援したい方は、寄付をいただければ大変ありがたいですが、全く任意です。多くの感謝、Benn", "Auto": "自動", "Auto (Use Chat Model)": "自動（チャットモデルを使用）", "Auto (Use Last Used)": "自動（最後に使用したものを使用）", "Auto-collapse code blocks": "コードブロックを自動的に折りたたむ", "Auto-Generate Chat Titles": "チャットのタイトルを自動生成", "Auto-preview artifacts": "アーティファクトを自動プレビュー", "Automatic updates": "自動更新", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "生成されたアーティファクト（HTML、CSS、JS、Tailwindなど）を自動的にレンダリング", "Azure API Key": "Azure API キー", "Azure API Version": "Azure APIバージョン", "Azure Dall-E Deployment Name": "Azure Dall-E モデルデプロイメント名", "Azure Deployment Name": "Azure デプロイメント名", "Azure Endpoint": "Azure エンドポイント", "Back to Previous": "前のスレッドに戻る", "Beta updates": "ベータ更新", "Browsing and retrieving information from the internet.": "Webブラウジング中、インターネットから情報を検索しています。", "Builtin MCP Servers": "内蔵MCPサーバー", "Can be activated on up to 5 devices": "最大5台のデバイスで有効化可能", "cancel": "キャンセル", "Cancel": "キャンセル", "cannot be empty": "空にできません", "Capabilities": "機能", "Changelog": "変更履歴", "chat": "チャット", "Chat History": "チャット履歴", "Chat Settings": "チャット設定", "Chatbox AI Advanced Model Quota": "Chatbox AI 高度モデルクオータ", "Chatbox AI Cloud": "Chatbox AI クラウド", "Chatbox AI Image Quota": "Chatbox AI 画像クオータ", "Chatbox AI License": "Chatbox AIライセンス", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AIは使いやすいAIソリューションを提供し、生産性を向上させるお手伝いをします", "Chatbox AI Quota": "Chatbox AI クォータ", "Chatbox AI Standard Model Quota": "Chatbox AI 標準モデルクオータ", "Chatbox Featured": "チャットボックス特集", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "Chatboxはあなたのプライバシーを尊重し、必要な場合にのみ匿名のエラーデータとイベントをアップロードします。設定でいつでも設定を変更できます。", "Chatbox will automatically use this model to construct search term.": "Chatboxは自動的にこのモデルを使用して検索語を構築します。", "Chatbox will automatically use this model to rename threads.": "Chatboxは自動的にこのモデルを使用してスレッドの名前を変更します。", "Chatbox will use this model as the default for new chats.": "Chatboxは新しいチャットのデフォルトとしてこのモデルを使用します。", "ChatGLM-6B URL Helper": "オープンソースモデル、<1>ChatGLM-6B</1>の<0>APIインターフェース</0>をサポート", "ChatGLM-6B Warnning for Chatbox-Web": "Chatboxのウェブ版を使用しているようですが、ChatGLM-6Bとのクロスドメインやその他のネットワーク問題が発生する可能性があります。Chatboxクライアントをダウンロードして使用し、潜在的な問題を避けてください。", "Check": "チェック", "Check Update": "更新を確認", "Child-inappropriate content": "子供に不適切な内容", "clean": "クリーン", "clean it up": "クリーンアップ", "Clear All Messages": "すべてのメッセージをクリアする", "Clear Conversation List": "会話リストをクリア", "Click here to set up": "セットアップするにはここをクリック", "Click to view license details and quota usage": "ライセンスの詳細とクオータの使用状況を確認するには、こちらをクリックしてください", "close": "閉じる", "Collapse": "折りたたむ", "Coming soon": "近日公開", "Command": "コマンド", "Configure MCP server manually": "MCPサーバーを手動で設定", "Confirm": "確認", "Confirm deletion?": "削除を確認しますか？", "Confirm to delete this custom provider?": "このカスタムプロバイダーを削除してもよろしいですか？", "Confirm?": "確認しますか？", "Connection failed!": "接続失敗！", "Connection successful!": "接続成功！", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "{{aiProvider}}への接続に失敗しました。これは通常、設定の誤りや{{aiProvider}}アカウントの問題によるものです。設定を確認して{{aiProvider}}アカウントのステータスを確認するか、<LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing>を購入して、すべての高度なモデルを即座に設定なしでロック解除してください。", "Continue this thread": "このスレッドを続ける", "Continue this Thread": "このスレッドを続ける", "Conversation Settings": "会話設定", "copied to clipboard": "クリップボードにコピーしました", "Copilot Avatar URL": "コパイロットアバターURL", "Copilot Name": "コパイロット名", "Copilot Prompt": "コパイロットプロンプト", "Copilot Prompt Demo": "あなたは翻訳者で、仕事は非英語から英語への翻訳です", "copy": "コピー", "Create a New Conversation": "新しい会話を作成", "Create a New Image-Creator Conversation": "新しい画像作成会話を作成", "Create New Copilot": "新しいコパイロットを作成", "creative": "クリエイティブ", "Current conversation configured with specific model settings": "現在の会話は特定のモデル設定で構成されています", "Current thread": "現在のスレッド", "Custom": "カスタム", "Custom MCP Servers": "カスタム MCP サーバー", "Custom Model": "カスタムモデル", "Custom Model Name": "カスタムモデル名", "Customize settings for the current conversation": "現在の会話の設定をカスタマイズ", "Dark Mode": "ダークモード", "Data Backup": "データバックアップ", "Data Backup and Restore": "データのバックアップと復元", "Data Restore": "データ復元", "Deactivate": "無効化する", "Deeply thought": "深く考えた", "Default Assistant Avatar": "デフォルトアシスタントアバター", "Default Chat Model": "デフォルトのチャットモデル", "Default Models": "デフォルトモデル", "Default Prompt for New Conversation": "新しい会話のデフォルトプロンプト", "Default Settings for New Conversation": "新規会話の既定の設定", "Default Thread Naming Model": "デフォルトスレッド命名モデル", "delete": "削除", "Delete": "削除", "delete confirmation": "この操作は、{{sessionName}}の全ての非システムメッセージを永久に削除します。続行しますか？", "Delete Current Session": "現在のセッションを削除", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "HTMLコンテンツをEdgeOne Pagesにデプロイし、アクセス可能な公開URLを取得する。", "Description": "説明", "Details": "詳細", "Disabled": "無効", "display": "表示", "Display": "表示", "Display Settings": "表示設定", "Donate": "寄付する", "Download": "ダウンロード", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "ローカル処理の制限により、より高度な文書処理機能と優れた結果を得るために<Link>Chatbox AIサービス</Link>をご利用いただくことをお勧めします。", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "ローカル処理の制限により、より高度なWebページ解析機能を得るために<Link>Chatbox AIサービス</Link>をご利用いただくことをお勧めします。", "E-mail": "電子メール", "e.g., Model Name, Current Date": "例：モデル名、現在の日付", "Easy Access": "簡単アクセス", "edit": "編集", "Edit": "編集", "Edit Avatars": "アバターを編集", "Edit default assistant avatar": "デフォルトアシスタントアバターを編集", "Edit MCP Server": "MCPサーバー編集", "Edit Model": "モデルを編集", "Edit Thread Name": "話題の名前を編集", "Edit user avatar": "ユーザーアバターを編集", "Email Us": "メール連絡", "Enable optional anonymous reporting of crash and event data": "クラッシュとイベントデータの任意の匿名レポートを有効にする", "Enable Thinking": "思考を有効にする", "Enabled": "有効", "Ending with / ignores v1, ending with # forces use of input address": "/で終わるとv1を無視し、#で終わると入力アドレスの使用を強制します", "Enjoying Chatbox?": "Chatboxをお楽しみですか？", "Enter": "エンター", "Environment Variables": "環境変数", "Error Reporting": "エラーレポート", "expand": "展開", "Expand": "展開", "Expansion Pack Quota": "拡張パック割り当て", "Explore (community)": "探索 (コミュニティ)", "Explore (official)": "探索 (公式)", "export": "エクスポート", "Export Chat": "チャットをエクスポート", "Export Selected Data": "選択したデータをエクスポート", "Exporting...": "エクスポート中...", "extension": "拡張機能", "Failed to activate license, please check your license key and network connection": "ライセンスの有効化に失敗しました。ライセンスキーとネットワーク接続を確認してください", "Failed to export file: {{error}}": "ファイルのエクスポートに失敗しました: {{error}}", "Failed to fetch models": "モデルの取得に失敗しました", "Failed to save file: {{error}}": "ファイルの保存に失敗しました: {{error}}", "FAQs": "よくある質問", "Favorite": "お気に入り", "Feedback": "フィードバック", "Fetch": "取得", "File saved to {{uri}}": "ファイルが{{uri}}に保存されました", "File type not supported. Supported types include txt, md, html, doc, docx, pdf, excel, pptx, csv, and all text-based files, including code files.": "サポートされていないファイルタイプです。サポートされているタイプには、txt、md、html、doc、docx、pdf、excel、pptx、csv、およびすべてのテキストベースのファイル、コードファイルが含まれます。", "Focus on the Input Box": "入力ボックスにフォーカス", "Focus on the Input Box and Enter Web Browsing Mode": "入力ボックスにフォーカスを移し、Webブラウジングモードに入る", "Follow me on Twitter(X)": "Twitterで私をフォローしてください", "Follow System": "システムに従う", "Font Size": "フォントサイズ", "font size changed, effective after next launch": "フォントサイズが変更されました、次回起動後に有効", "Format": "フォーマット", "Full-text search of chat history (coming soon)": "チャット履歴の全文検索（近日公開）", "Function": "機能", "General Settings": "一般設定", "Generate More Images Below": "以下でさらに画像を生成", "Get API Key": "API キーを取得", "Get better connectivity and stability with the Chatbox desktop application. <a>Download now</a>.": "Chatboxデスクトップアプリを使用して、より良い接続と安定性を得てください。<a>今すぐダウンロード</a>。", "Get License": "ライセンスを取得", "get more": "もっと見る", "Github": "<PERSON><PERSON><PERSON>", "Harmful or offensive content": "有害または不適切な内容", "Hassle-free setup": "手間いらずのセットアップ", "Hate speech or harassment": "憎悪発言または虐待", "Here you can add and manage various custom model providers. As long as the provider's API is compatible with the selected API mode, you can seamlessly connect and use it within Chatbox.": "ここでは、さまざまなカスタムモデルプロバイダーを追加および管理できます。プロバイダーのAPIが選択したAPIモードと互換性がある限り、Chatbox内でシームレスに接続して使用できます。", "High": "高", "Home Page": "ホームページ", "Homepage": "ホームページ", "Hotkeys": "ショートカットキー", "How to use?": "使い方？", "Ideal for both work and educational scenarios": "仕事と教育の両方のシナリオに最適", "Ideal for work and study": "仕事と学習に最適", "Image Creator": "画像生成", "Image Creator Intro": "こんにちは！私はChatbox Image Creator、あなたのアーティスティックなAIコンパニオンです。あなたの言葉を魅力的なビジュアルに変えることに特化しています。あなたが夢見ることができれば、私が創造できますー魅惑的な風景、ダイナミックなキャラクター、アプリアイコンから抽象的なものまで。\n\n私は静かなロボットです、だから**単にあなたが心に描くイメージの説明を教えてください**、そして私は私の全ピクセルを集中してあなたのビジョンを形作ります。\n\nアートを作りましょう！", "Image Style": "画像スタイル", "Import and Restore": "インポートと復元", "Import failed, unsupported data format": "インポート失敗、サポートされていないデータ形式", "Import from JSON in clipboard": "クリップボード内のJSONからインポート", "Import MCP servers from JSON in your clipboard": "クリップボード内のJSONからMCPサーバーをインポート", "Importing...": "インポート中...", "Improve Network Compatibility": "ネットワークの互換性を向上", "Inject default metadata": "デフォルトのメタデータを注入", "Insert a New Line into the Input Box": "入力ボックスに新しい行を挿入", "Instruction (System Prompt)": "指示（システムプロンプト）", "Invalid request parameters detected. Please try again later. Persistent failures may indicate an outdated software version. Consider upgrading to access the latest performance improvements and features.": "無効なリクエストパラメータが検出されました。後で再試行してください。持続的な失敗は、古いソフトウェアバージョンを示す可能性があります。最新のパフォーマンスの向上と機能にアクセスするためにアップグレードを検討してください。", "It only takes a few seconds and helps a lot.": "これはたった数秒で、大きな助けになります。", "Keep only the Top N Conversations in List and Permanently Delete the Rest": "リストの上位<0>{{n}}</0>の会話だけを保持し、残りを永久に削除", "Key Combination": "キーの組み合わせ", "Keyboard Shortcuts": "キーボードショートカット", "Language": "言語", "Last Session": "前回のセッション", "LaTeX Rendering (Requires Markdown)": "LaTeXレンダリング（Markdownが必要）", "Launch at system startup": "システム起動時に自動起動", "License Activated": "ライセンスが有効化", "License expired, please check your license key": "ライセンスが期限切れです、ライセンスキーを確認してください", "License Expiry": "ライセンス有効期限", "License not found, please check your license key": "ライセンスが見つかりません、ライセンスキーを確認してください", "License Plan Overview": "ライセンスプラン概要", "lifetime license": "ライフタイムライセンス", "Light Mode": "ライトモード", "Loading webpage...": "Webページを読み込んでいます...", "Local (stdio)": "ローカル (stdio)", "Local Mode": "ローカルモード", "Low": "低い", "Make sure you have the following command installed:": "以下のコマンドがインストールされていることを確認してください。", "Manage License and Devices": "ライセンスとデバイスの管理", "Manually": "手動で", "Markdown Rendering": "Markdownレンダリング", "Max Message Count in Context": "コンテキスト内の最大メッセージ数", "max tokens in context": "コンテキスト内の最大トークン数", "max tokens to generate": "生成する最大トークン数", "Maybe Later": "後でやる", "MCP server added": "MCPサーバーが追加されました", "MCP server for accessing arXiv papers": "arXiv 論文にアクセスするための MCP サーバー", "MCP Settings": "MCP 設定", "Medium": "中", "Mermaid Diagrams & Charts Rendering": "Mermaid ダイアグラムとチャートのレンダリング", "meticulous": "細心", "Misleading information": "誤った情報", "Mobile devices temporarily do not support local parsing of this file type. Please use text files (txt, markdown, etc.) or use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis.": "モバイルデバイスは一時的にこのファイル形式のローカル解析に対応していません。テキストファイル（txt、markdownなど）をご利用いただくか、クラウドベースのドキュメント分析には<LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing>をご利用ください。", "model": "モデル", "Model": "モデル", "Model ID": "モデルID", "Model Provider": "モデルプロバイダー", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "AIの応答の創造性を変更します。値が高いほど、回答はよりランダムで魅力的になりますが、値が低いとより大きな安定性と信頼性が確保されます。", "More Images": "更に画像", "Move to Conversations": "対話に移動", "My Assistant": "私のアシスタント", "My Copilots": "私のコパイロット", "name": "名前", "Name": "名前", "Natural": "よりリアル", "Navigate to the Next Conversation": "次の会話に移動", "Navigate to the Next Option (in search dialog)": "次のオプションに移動（検索ダイアログ内）", "Navigate to the Previous Conversation": "前の会話に移動", "Navigate to the Previous Option (in search dialog)": "前のオプションに移動（検索ダイアログ内）", "Navigate to the Specific Conversation": "特定の会話に移動", "network error tips": "ネットワークエラーが発生しました。現在のネットワークステータスと{{host}}との接続を確認してください。", "Network Proxy": "ネットワークプロキシ", "network proxy error tips": "プロキシアドレスが設定されています {{proxy}}。プロキシサーバーが正常に機能しているか確認するか、設定からプロキシアドレスを削除することを検討してください。", "New": "新規", "New Chat": "新しいチャット", "New Images": "新しい画像", "New Thread": "新しいスレッド", "Nickname": "ニックネーム", "No": "いいえ", "No eligible models available": "利用可能なモデルがありません", "No Limit": "無制限", "No MCP servers parsed from clipboard": "クリップボードからMCPサーバーが解析されませんでした", "No permission to write file": "ファイルを書き込む権限がありません", "No results found": "結果は見つかりませんでした", "No search results found. Please use another <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton> or try again later.": "検索結果が見つかりませんでした。別の<OpenExtensionSettingButton>検索プロバイダー</OpenExtensionSettingButton>を使用するか、後で再試行してください。", "None": "なし", "not available in browser": "この機能はブラウザでは使用できません。すべての機能を利用するためには、デスクトップアプリをダウンロードしてください。", "Not set": "未設定", "Nothing found...": "見つかりませんでした...", "Number of Images per Reply": "返信ごとの画像数", "One-click MCP servers for Chatbox AI subscribers": "Chatbox AI登録者向けのワンクリックMCPサーバー", "OpenAI API Compatible": "OpenAI API互換", "Operations": "操作", "optional": "任意", "or": "または", "Or become a sponsor": "またはスポンサーになる", "Other concerns": "その他の問題", "Paste long text as a file": "長文をファイルとして貼り付け", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "長文をファイルとして貼り付けると、チャットをクリーンに保ち、プロンプトキャッシュを使用してトークン使用量を削減できます。", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF、DOC、PPT、XLS、TXT、Code...", "Please describe the content you want to report (Optional)": "報告したい内容を説明してください（オプション）", "Please ensure that the Remote LM Studio Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "リモートLM Studioサービスがリモート接続できることを確認してください。詳細については、<a>このチュートリアル</a>を参照してください。", "Please ensure that the Remote Ollama Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "リモートOllamaサービスがリモート接続できることを確認してください。詳細については、<a>このチュートリアル</a>を参照してください。", "Please note that as a client tool, Chatbox cannot guarantee the quality of service and data privacy of the model providers. If you are looking for a stable, reliable, and privacy-protecting model service, consider <a>Chatbox AI</a>.": "クライアントツールとして、Chatboxはモデルプロバイダーのサービス品質とデータプライバシーを保証できません。安定した、信頼性の高い、プライバシーを保護するモデルサービスをお探しの場合は、<a>Chatbox AI</a>を検討してください。", "Please select a model": "モデルを選択してください", "Please test before saving": "保存する前にテストしてください", "Please wait about 20 seconds": "約20秒お待ちください", "pre-sale discount": "プレセール割引", "premium": "プレミアム", "Premium Activation": "プレミアムの有効化", "Premium License Activated": "プレミアムライセンスが有効化されました", "Premium License Key": "プレミアムライセンスキー", "Press hotkey": "ショートカットキーを入力", "Preview": "プレビュー", "Privacy Policy": "プライバシーポリシー", "Prompt": "プロンプト", "Provider not found": "プロバイダーが見つかりません", "proxy": "プロキシ", "Proxy Address": "プロキシアドレス", "Purchase": "購入", "QR Code": "QRコード", "Quota Reset": "クオータリセット", "quote": "引用", "Rate Now": "今すぐ評価する", "Reading file...": "ファイルを読み込んでいます...", "Reasoning": "推論", "RedNote": "レッドノート", "Refresh": "リフレッシュ", "regenerate": "再生成", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "AIに送信される歴史的メッセージの量を調整し、理解の深さと応答の効率の間に調和のとれたバランスを見つけます。", "Remote (http/sse)": "リモート (http/sse)", "rename": "名前を変更", "Reply Again": "もう一度返信", "Reply Again Below": "下記で再び返信", "report": "報告", "Report Content": "報告内容", "Report Content ID": "報告内容ID", "Report Type": "報告タイプ", "reset": "リセット", "Reset": "リセット", "Reset All Hotkeys": "すべてのショートカットキーをリセット", "Reset to Default": "デフォルトにリセット", "Reset to Global Settings": "グローバル設定にリセット", "Result": "結果", "Retrieve License": "ライセンスを取得", "Retrieves up-to-date documentation and code examples for any library.": "あらゆるライブラリの最新のドキュメントとコード例を取得します。", "Roadmap": "ロードマップ", "save": "保存", "Save": "保存", "Save & Resend": "保存して再送信", "Scope": "スコープ", "Search": "検索", "Search All Conversations": "全ての会話を検索する", "Search in Current Conversation": "現在の会話を検索する", "Search models": "モデルを検索", "Search Provider": "検索プロバイダー", "Search query": "検索クエリ", "Search Term Construction Model": "検索語構築モデル", "Search...": "検索...", "Select and configure an AI model provider": "AIモデルプロバイダーを選択して設定", "Select File": "ファイルを選択", "Select Model": "モデルを選択", "Select the Current Option (in search dialog)": "現在のオプションを選択（検索ダイアログ内）", "send": "送信", "Send": "送信", "Send Without Generating Response": "レスポンスを生成せずに送信", "Setting the avatar for Copilot": "コパイロットのアバターを設定", "settings": "設定", "Settings": "設定", "Sexual content": "性的な内容", "Share File": "ファイルを共有", "Share with Chatbox": "チャットボックスで共有", "Show all ({{x}})": "すべて表示 ({{x}})", "show first token latency": "最初のトークンの遅延を表示", "Show in Thread List": "スレッドリストに表示", "show message timestamp": "メッセージのタイムスタンプを表示", "show message token count": "メッセージのトークン数を表示", "show message token usage": "メッセージのトークン使用状況を表示", "show message word count": "メッセージの単語数を表示", "show model name": "モデル名を表示", "Show/Hide the Application Window": "アプリケーションウィンドウの表示/非表示", "Show/Hide the Search Dialog": "検索ダイアログの表示/非表示", "Smartest AI-Powered Services for Rapid Access": "最もスマートなAIパワードサービスで迅速なアクセス", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model or use the recommended <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.": "現在のモデル{{model}} APIは画像の理解をサポートしていません。画像を送信する必要がある場合は、別のモデルに切り替えるか、推奨される<OpenMorePlanButton>Chatbox AIモデル</OpenMorePlanButton>を使用してください。", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model.": "現在のモデル{{model}} APIは画像の理解をサポートしていません。画像を送信する必要がある場合は、別のモデルに切り替えてください。", "Spam or advertising": "スパムまたは広告", "Special thanks to the following sponsors:": "以下のスポンサーに特別な感謝を：", "Specific model settings": "特定のモデル設定", "Specific model settings configured for this conversation": "この会話に対して設定された特定のモデル設定", "Spell Check": "スペルチェック", "star": "スター", "Start a New Thread": "新しいスレッドを開始", "Start Setup": "セットアップを開始", "Startup Page": "スタートアップページ", "stop generating": "生成を停止", "submit": "送信", "Support for ChatBox development": "ChatBox開発のサポート", "Support jpg or png file smaller than 5MB": "5MB未満のjpgまたはpngファイルをサポート", "Supports a variety of advanced AI models": "さまざまな先進的なAIモデルをサポート", "Survey": "調査", "Switch": "切り替え", "system": "システム", "Tavily API Key": "Tavily API キー", "temperature": "温度", "Temperature": "温度", "Test": "テスト", "Thank you for your report": "ご報告ありがとうございます", "The {{model}} API does not support files. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} APIはファイルをサポートしていません。ローカル処理のために<LinkToHomePage>デスクトップアプリ</LinkToHomePage>をダウンロードしてください。", "The {{model}} API does not support files. Please use <LinkToAdvancedFileProcessing>Chatbox AI models</LinkToAdvancedFileProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} APIはファイルをサポートしていません。代わりに<LinkToAdvancedFileProcessing>Chatbox AIモデル</LinkToAdvancedFileProcessing>をご利用ください、またはローカル処理のために<LinkToHomePage>デスクトップアプリ</LinkToHomePage>をダウンロードしてください。", "The {{model}} API does not support links. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} APIはリンクをサポートしていません。ローカル処理のために<LinkToHomePage>デスクトップアプリ</LinkToHomePage>をダウンロードしてください。", "The {{model}} API does not support links. Please use <LinkToAdvancedUrlProcessing>Chatbox AI models</LinkToAdvancedUrlProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} APIはリンクをサポートしていません。代わりに<LinkToAdvancedUrlProcessing>Chatbox AIモデル</LinkToAdvancedUrlProcessing>をご利用ください、またはローカル処理のために<LinkToHomePage>デスクトップアプリ</LinkToHomePage>をダウンロードしてください。", "The {{model}} API doesn't support document understanding. You can download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "現在のモデル{{model}} APIはドキュメントの理解をサポートしていません。ドキュメントの分析には、<LinkToHomePage>Chatboxデスクトップアプリ</LinkToHomePage>をダウンロードしてローカルで分析してください。", "The {{model}} API doesn't support document understanding. You can use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis, or download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "現在のモデル{{model}} APIはドキュメントの理解をサポートしていません。ドキュメントの分析には、<LinkToAdvancedFileProcessing>Chatbox AIサービス</LinkToAdvancedFileProcessing>を使用するか、<LinkToHomePage>Chatboxデスクトップアプリ</LinkToHomePage>をダウンロードしてローカルで分析してください。", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code).": "{{model}} API 自体はファイルの送信をサポートしていません。ローカル処理の複雑さにより、Chatbox はテキストベースのファイル（コードを含む）のみを処理します。", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code). For additional file formats and enhanced document understanding capabilities, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> is recommended.": "{{model}} API 自体はファイルの送信をサポートしていません。ローカル処理の複雑さにより、Chatbox はテキストベースのファイル（コードを含む）のみを処理します。追加のファイル形式と強化されたドキュメント理解能力をサポートするには、<LinkToAdvancedFileProcessing>Chatbox AI サービス</LinkToAdvancedFileProcessing>をお勧めします。", "The {{model}} API itself does not support web browsing. Supported models: {{supported_web_browsing_models}}": "現在のモデル{{model}} APIはWebブラウジングをサポートしていません。サポートされているモデル：{{supported_web_browsing_models}}", "The {{model}} API itself does not support web browsing. Supported models: <OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}": "現在のモデル{{model}} APIはWebブラウジングをサポートしていません。サポートされているモデル：<OpenMorePlanButton>Chatbox AIモデル</OpenMorePlanButton>, {{supported_web_browsing_models}}", "The cache data for the file was not found. Please create a new conversation or refresh the context, and then send the file again.": "ファイルのキャッシュデータが見つかりませんでした。新しい会話を作成するか、コンテキストをリフレッシュしてから、ファイルを再度送信してください。", "The current model {{model}} does not support sending links.": "現在のモデル{{model}}はリンクの送信をサポートしていません。", "The current model {{model}} does not support sending links. Currently supported models: Chatbox AI models.": "現在のモデル{{model}}はリンクの送信をサポートしていません。現在サポートされているモデル：Chatbox AIモデル。", "The file size exceeds the limit of 50MB. Please reduce the file size and try again.": "ファイルサイズが50MBの制限を超えています。ファイルサイズを縮小してからもう一度お試しください。", "The file you sent has expired. To protect your privacy, all file-related cache data has been cleared. You need to create a new conversation or refresh the context, and then send the file again.": "送信したファイルの有効期限が切れました。プライバシーを保護するため、すべてのファイル関連のキャッシュデータがクリアされました。新しい会話を作成するか、コンテキストをリフレッシュしてから、ファイルを再度送信する必要があります。", "The Image Creator plugin has been activated for the current conversation": "画像作成プラグインが現在の会話で有効化されました", "The license key you entered is invalid. Please check your license key and try again.": "入力したライセンスキーが無効です。ライセンスキーを確認してもう一度お試しください。", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "topPパラメーターはAIの応答の多様性を制御します。値が低いほど出力はより焦点を絞り予測可能になりますが、値が高いほどより多様で創造的な返答が可能になります。", "Theme": "テーマ", "Thinking": "考え中", "Thinking Budget": "思考予算", "Thinking Budget only works for 2.0 or later models": "思考予算は2.0以降のモデルでのみ動作します", "Thinking Budget only works for 3.7 or later models": "思考予算は3.7以降のモデルにのみ対応しています", "Thinking Effort": "思考労力", "Thinking Effort only works for OpenAI o-series models": "思考努力は OpenAI o-series models にのみ対応しています", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "このライセンスキーは有効化制限に達しました、古いデバイスを無効化するためにライセンスとデバイスを管理するには<a>ここをクリックして</a>ください。", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "このサーバーは、LLMがウェブページからコンテンツを取得・処理することを可能にし、HTMLをマークダウンに変換することで、より利用しやすくします。", "Thread History": "スレッド履歴", "To access locally deployed model services, please install the Chatbox desktop version": "ローカルにデプロイされたモデルサービスにアクセスするには、Chatboxのデスクトップ版をインストールしてください", "Toggle": "トグル", "token": "トークン", "Tool use": "ツール使用", "Tool Use": "ツール使用", "Tools": "ツール", "Top P": "Top P", "Type": "種類", "Type a command or search": "コマンドを入力または検索", "Type your question here...": "ここに質問を入力してください...", "unknown error tips": "不明なエラーが発生しました。AI設定とアカウントステータスを確認するか、<0>ここをクリックしてFAQドキュメントを表示</0>してください。", "Unlock Copilot Avatar by Upgrading to Premium Edition": "プレミアムエディションにアップグレードしてコパイロットアバターを解除", "Unsaved settings": "未保存の設定", "unstar": "スター解除", "Untitled": "無題", "Update Available": "更新が利用可能", "Upload Image": "画像をアップロード", "Upon import, changes will take effect immediately and existing data will be overwritten": "インポート後、変更はすぐに反映され、既存のデータは上書きされます", "Use My Own API Key / Local Model": "自分のAPIキー/ローカルモデルを使用", "Use proxy to resolve CORS and other network issues": "プロキシを使用して CORS とその他のネットワーク問題を解決", "user": "ユーザー", "User Avatar": "ユーザーアバター", "User Terms": "ユーザー規約", "version": "バージョン", "View All Copilots": "すべてのコパイロットを表示", "View historical threads": "過去のスレッドを表示", "View More Plans": "プランをもっと見る", "Violence or dangerous content": "暴力または危険な内容", "Vision": "ビジョン", "Vision capability is not enabled for Model {{model}}. Please enable it in <OpenSettingButton>provider settings</OpenSettingButton> or switch to a model that supports vision.": "視覚機能はモデル {{model}} で有効になっていません。<OpenSettingButton>プロバイダー設定</OpenSettingButton>で有効にするか、視覚をサポートするモデルに切り替えてください。", "Vision, Drawing, File Understanding and more": "ビジョン、描画、ファイル理解など", "Vivid": "よりアーティスティック", "Web Browsing": "Webブラウジング", "Web browsing (coming soon)": "ウェブブラウジング（近日公開）", "Web Browsing...": "Webブラウジング中...", "Web Search": "インターネット検索", "WeChat": "We<PERSON><PERSON> (ウィーチャット)", "What can I help you with today?": "今日は何をお手伝いしましょうか？", "Yes": "はい", "You are already a Premium user": "あなたはすでにプレミアムユーザーです", "You have exceeded the rate limit for the Chatbox AI service. Please try again later.": "Chatbox AIサービスのレート制限を超過しました。後で再試行してください。", "You have no more Chatbox AI quota left this month.": "今月のChatbox AIのクォータはもうありません。", "You have reached your monthly quota for the {{model}} model. Please <OpenSettingButton>go to Settings</OpenSettingButton> to switch to a different model, view your quota usage, or upgrade your plan.": "あなたは{{model}}モデルの月間クオータに達しました。別のモデルに切り替えたり、クオータの使用状況を表示したり、プランをアップグレードしたりするには、<OpenSettingButton>設定に移動</OpenSettingButton>してください。", "You have selected Chatbox AI as the model provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different model provider.": "Chatbox AIをモデルプロバイダーとして選択しましたが、ライセンスキーがまだ入力されていません。ライセンスキーを入力するか、別のモデルプロバイダーを選択するには、<OpenSettingButton>ここをクリックして設定を開いて</OpenSettingButton>ください。", "You have selected Chatbox AI as the search provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton>.": "Chatbox AIを検索プロバイダーとして選択しましたが、ライセンスキーがまだ入力されていません。ライセンスキーを入力するか、別の<OpenExtensionSettingButton>検索プロバイダー</OpenExtensionSettingButton>を選択してください。", "You have selected Tavily as the search provider, but an API key has not been entered yet. Please <OpenExtensionSettingButton>click here to open Settings</OpenExtensionSettingButton> and enter your API key, or choose a different search provider.": "Tavilyを検索プロバイダーとして選択しましたが、APIキーがまだ入力されていません。APIキーを入力するか、別の検索プロバイダーを選択してください。", "You have unsaved settings. Are you sure you want to leave?": "未保存の設定があります。本当に移動しますか？", "Your ChatboxAI subscription already includes access to models from various providers. There's no need to switch providers - you can select different models directly within ChatboxAI. Switching from ChatboxAI to other providers will require their respective API keys. <button>Back to ChatboxAI</button>": "あなたのChatboxAIサブスクリプションには、さまざまなプロバイダーのモデルへのアクセスがすでに含まれています。プロバイダーを切り替える必要はありません。ChatboxAI内で直接異なるモデルを選択できます。ChatboxAIから他のプロバイダーに切り替えると、それぞれのAPIキーが必要になります。<button>ChatboxAIに戻る</button>", "Your current License (Chatbox AI Lite) does not support the {{model}} model. To use this model, please <OpenMorePlanButton>upgrade</OpenMorePlanButton> to Chatbox AI Pro or a higher-tier package. Alternatively, you can switch to a different model by <OpenSettingButton>accessing the settings</OpenSettingButton>.": "あなたの現在のライセンス（Chatbox AI Lite）は{{model}}モデルをサポートしていません。このモデルを使用するには、<OpenMorePlanButton>アップグレード</OpenMorePlanButton>してChatbox AI Proまたはより高いティアのパッケージにアップグレードしてください。または、<OpenSettingButton>設定にアクセス</OpenSettingButton>して別のモデルに切り替えることもできます。", "Your license has expired. Please check your subscription or purchase a new one.": "ライセンスが期限切れです。サブスクリプションを確認するか、新しいものを購入してください。", "Your rating on the App Store would help make Chatbox even better!": "App Storeでの評価は、Chatboxをさらに良くするのに役立ちます！"}