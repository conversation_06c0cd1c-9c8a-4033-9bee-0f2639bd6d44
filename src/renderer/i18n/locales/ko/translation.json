{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] 저장, [Ctrl+Shift+Enter] 저장 및 재전송", "[Enter] send, [Shift+Enter] line break, [Ctrl+Enter] send without generating": "[Enter] 전송, [Shift+Enter] 줄 바꿈, [Ctrl+Enter] 생성하지 않고 전송", "{{count}} MCP servers imported": "{{count}} MCP 서버 가져옴", "About": "정보", "About Chatbox": "Chatbox 정보", "about-introduction": "다양한 고급 AI 모델을 지원하는 사용자 친화적인 AI 데스크톱 클라이언트로, 최첨단 인공지능 기술을 쉽게 사용할 수 있는 생산성 도구로 변환합니다.", "about-slogan": "AI와 함께 효율성을 높여라, 일과 학습에 필수적인 동반자", "Access to all future premium feature updates": "모든 향후 프리미엄 기능 업데이트에 액세스 가능", "Action": "작업", "Activate License": "라이선스 활성화", "Activating...": "활성화 중...", "Add": "추가", "Add at least one model to check connection": "연결 확인을 위해 모델을 하나 이상 추가하세요.", "Add Custom Provider": "사용자 정의 공급자 추가", "Add Custom Server": "사용자 지정 서버 추가", "Add MCP Server": "MCP 서버 추가", "Add or Import": "추가 또는 가져오기", "Add provider": "제공자 추가", "Add Server": "서버 추가", "Add your first MCP server": "첫 번째 MCP 서버를 추가하세요", "advanced": "고급", "Advanced": "고급", "Advanced Mode": "고급 모드", "AI Model Provider": "AI 모델 공급자", "ai provider no implemented paint tips": "현재 AI 모델 제공 업체({{aiProvider}})는 그림 기능을 지원하지 않습니다. 현재 그림 기능은 Chatbox AI, OpenAI, Azure OpenAI 만 지원합니다. 필요한 경우 <0>설정으로 이동하여</0>AI 모델 제공 업체를 변경하세요.", "AI Settings": "AI 설정", "All data is stored locally, ensuring privacy and rapid access": "모든 데이터는 로컬에 저장되어 개인 정보 보호와 빠른 액세스를 보장합니다", "All major AI models in one subscription": "모든 주요 AI 모델을 하나의 구독으로 사용", "All threads": "모든 스레드", "already existed": "이미 존재함", "An easy-to-use AI client app": "사용하기 쉬운 AI 클라이언트 앱", "An error occurred while processing your request. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "요청 처리 중 오류가 발생했습니다. 나중에 다시 시도하세요. 이 오류가 지속되면 ****************로 이메일을 보내주세요.", "An error occurred while sending the message.": "메시지 전송 중 오류가 발생했습니다.", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "구조화된 사고 과정을 통해 동적이고 성찰적인 문제 해결을 위한 도구를 제공하는 MCP 서버 구현.", "An unknown error occurred. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "알 수 없는 오류가 발생했습니다. 나중에 다시 시도하세요. 이 오류가 지속되면 ****************로 이메일을 보내주세요.", "any number key": "숫자 키 아무거나", "api error tips": "{{aiProvider}}에서 오류가 발생했습니다. 이는 일반적으로 잘못된 설정이나 계정 문제로 인한 것입니다. AI 설정과 계정 상태를 확인하거나 <0>여기를 클릭하여 FAQ 문서를 확인</0>하십시오.", "api host": "API 호스트", "API Host": "API 호스트", "api key": "API 키", "API Key": "API 키", "API KEY & License": "API KEY 및 라이선스", "API key invalid!": "API 키가 유효하지 않습니다!", "API Key is required to check connection": "연결 확인을 위해 API 키가 필요합니다.", "API Mode": "API 모드", "api path": "API 경로", "API Path": "API 경로", "Are you sure you want to delete this server?": "정말로 이 서버를 삭제하시겠습니까?", "Arguments": "인수", "assistant": "어시스턴트", "Attach Image": "이미지 첨부", "Attach Link": "링크 첨부", "Auther Message": "Chatbox는 제 개인적인 용도로 만들었고 많은 사람들이 즐기는 것을 보는 것이 정말 좋습니다! 개발을 지원하고 싶으시다면, 기부를 진심으로 환영합니다. 그러나 기부는 완전히 선택 사항입니다. 감사합니다, <PERSON><PERSON>", "Auto": "자동", "Auto (Use Chat Model)": "자동 (채팅 모델 사용)", "Auto (Use Last Used)": "자동 (마지막 사용 모델 사용)", "Auto-collapse code blocks": "코드 블록 자동 숨기기", "Auto-Generate Chat Titles": "채팅 제목 자동 생성", "Auto-preview artifacts": "아티팩트 자동 미리보기", "Automatic updates": "자동 업데이트", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "생성된 아티팩트(예: CSS, JS, Tailwind가 포함된 HTML)를 자동으로 렌더링", "Azure API Key": "Azure API 키", "Azure API Version": "Azure API 버전", "Azure Dall-E Deployment Name": "Azure Dall-E 모델 배포 이름", "Azure Deployment Name": "Azure 배포 이름", "Azure Endpoint": "Azure 엔드포인트", "Back to Previous": "이전으로 돌아가기", "Beta updates": "베타 업데이트", "Browsing and retrieving information from the internet.": "웹 브라우징, 인터넷에서 정보 검색", "Builtin MCP Servers": "내장 MCP 서버", "Can be activated on up to 5 devices": "최대 5대의 기기에서 활성화 가능", "cancel": "취소", "Cancel": "취소", "cannot be empty": "비워둘 수 없습니다", "Capabilities": "기능", "Changelog": "변경 내역", "chat": "채팅", "Chat History": "채팅 기록", "Chat Settings": "채팅 설정", "Chatbox AI Advanced Model Quota": "Chatbox AI 고급 모델 할당량", "Chatbox AI Cloud": "Chatbox AI 클라우드", "Chatbox AI Image Quota": "Chatbox AI 이미지 할당량", "Chatbox AI License": "Chatbox AI 라이선스", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AI는 사용하기 쉬운 AI 솔루션을 제공하여 생산성을 향상시키는데 도움을 줍니다", "Chatbox AI Quota": "Chatbox AI 할당량", "Chatbox AI Standard Model Quota": "Chatbox AI 표준 모델 할당량", "Chatbox Featured": "Chatbox 추천", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "Chatbox는 당신의 개인 정보를 존중하며 필요할 때만 익명의 오류 데이터와 이벤트를 업로드합니다. 설정에서 언제든지 선호도를 변경할 수 있습니다.", "Chatbox will automatically use this model to construct search term.": "Chatbox는 이 모델을 사용하여 검색어를 자동으로 구성합니다.", "Chatbox will automatically use this model to rename threads.": "Chatbox는 이 모델을 사용하여 스레드 이름을 자동으로 변경합니다.", "Chatbox will use this model as the default for new chats.": "Chatbox는 새 채팅에 이 모델을 기본으로 사용합니다.", "ChatGLM-6B URL Helper": "오픈 소스 모델 <1>ChatGLM-6B</1>의 <0>API 인터페이스</0> 지원", "ChatGLM-6B Warnning for Chatbox-Web": "Chatbox의 웹 버전을 사용 중인 것 같습니다. ChatGLM-6B와 관련된 교차 도메인이나 기타 네트워크 문제가 발생할 수 있습니다. 잠재적인 문제를 피하려면 Chatbox 클라이언트를 다운로드하여 사용하십시오.", "Check": "확인", "Check Update": "업데이트 확인", "Child-inappropriate content": "어린이에게 적합하지 않은 내용", "clean": "정리", "clean it up": "정리", "Clear All Messages": "모든 메시지 지우기", "Clear Conversation List": "대화 목록 지우기", "Click here to set up": "여기를 클릭하여 설정하세요", "Click to view license details and quota usage": "라이선스 세부 정보 및 할당량 사용 현황을 보려면 클릭하세요", "close": "닫기", "Collapse": "축소", "Coming soon": "곧 출시 예정", "Command": "명령", "Configure MCP server manually": "수동으로 MCP 서버 설정", "Confirm": "확인", "Confirm deletion?": "삭제 확인?", "Confirm to delete this custom provider?": "이 사용자 정의 제공자를 삭제하시겠습니까?", "Confirm?": "확인?", "Connection failed!": "연결 실패!", "Connection successful!": "연결 성공!", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "{{aiProvider}}에 연결하지 못했습니다. 일반적으로 잘못된 구성 또는 {{aiProvider}} 계정 문제로 발생합니다. <buttonOpenSettings>설정을 확인</buttonOpenSettings>하고 {{aiProvider}} 계정 상태를 확인하거나 <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing>를 구매하여 모든 고급 모델을 즉시 구성 없이 잠금 해제하세요.", "Continue this thread": "이 스레드 계속하기", "Continue this Thread": "이 스레드 계속하기", "Conversation Settings": "대화 설정", "copied to clipboard": "클립보드에 복사되었습니다", "Copilot Avatar URL": "동반자 아바타 URL", "Copilot Name": "동반자 이름", "Copilot Prompt": "동반자 프롬프트", "Copilot Prompt Demo": "번역사이고, 당신의 일은 비영어를 영어로 번역하는 것입니다", "copy": "복사", "Create a New Conversation": "새 대화 만들기", "Create a New Image-Creator Conversation": "새 이미지 생성기 대화 만들기", "Create New Copilot": "새 동반자 만들기", "creative": "창의적인", "Current conversation configured with specific model settings": "현재 대화 특정 모델 설정으로 구성됨", "Current thread": "현재 스레드", "Custom": "사용자 정의", "Custom MCP Servers": "사용자 지정 MCP 서버", "Custom Model": "사용자 정의 모델", "Custom Model Name": "사용자 정의 모델 이름", "Customize settings for the current conversation": "현재 대화에 대한 설정 사용자 정의", "Dark Mode": "다크 모드", "Data Backup": "데이터 백업", "Data Backup and Restore": "데이터 백업 및 복원", "Data Restore": "데이터 복원", "Deactivate": "비활성화", "Deeply thought": "깊이 생각된", "Default Assistant Avatar": "기본 어시스턴트 아바타", "Default Chat Model": "기본 채팅 모델", "Default Models": "기본 모델", "Default Prompt for New Conversation": "새 대화의 기본 프롬프트", "Default Settings for New Conversation": "새 대화 기본 설정", "Default Thread Naming Model": "기본 스레드 명명 모델", "delete": "삭제", "Delete": "삭제", "delete confirmation": "{{sessionName}}에서 모든 시스템이 아닌 메시지가 영구적으로 삭제됩니다. 계속하시겠습니까?", "Delete Current Session": "현재 세션 삭제", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "HTML 콘텐츠를 EdgeOne Pages에 배포하고 접근 가능한 공개 URL을 획득합니다.", "Description": "설명", "Details": "세부 정보", "Disabled": "비활성화됨", "display": "표시", "Display": "표시", "Display Settings": "디스플레이 설정", "Donate": "기부", "Download": "다운로드", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "로컬 처리의 한계로 인해, 향상된 문서 처리 기능과 더 나은 결과를 위해 <Link>Chatbox AI 서비스</Link>를 권장합니다.", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "로컬 처리의 한계로 인해, 향상된 웹페이지 파싱 기능을 위해 <Link>Chatbox AI 서비스</Link>를 권장합니다.", "E-mail": "이메일", "e.g., Model Name, Current Date": "예: 모델 이름, 현재 날짜", "Easy Access": "쉬운 액세스", "edit": "편집", "Edit": "편집", "Edit Avatars": "아바타 편집", "Edit default assistant avatar": "기본 어시스턴트 아바타 편집", "Edit MCP Server": "MCP Server 편집", "Edit Model": "모델 편집", "Edit Thread Name": "话题 이름 수정", "Edit user avatar": "사용자 아바타 편집", "Email Us": "이메일 문의", "Enable optional anonymous reporting of crash and event data": "선택적으로 충돌 및 이벤트 데이터의 익명 보고 활성화", "Enable Thinking": "사고 활성화", "Enabled": "활성화", "Ending with / ignores v1, ending with # forces use of input address": "/로 끝나면 v1을 무시하고 #로 끝나면 입력 주소를 강제로 사용합니다.", "Enjoying Chatbox?": "Chatbox를 즐기고 있습니까?", "Enter": "Enter", "Environment Variables": "환경 변수", "Error Reporting": "오류 보고", "expand": "확장", "Expand": "확장", "Expansion Pack Quota": "확장팩 할당량", "Explore (community)": "탐색 (커뮤니티)", "Explore (official)": "탐색 (공식)", "export": "내보내기", "Export Chat": "채팅 내보내기", "Export Selected Data": "선택한 데이터 내보내기", "Exporting...": "내보내는 중...", "extension": "확장", "Failed to activate license, please check your license key and network connection": "라이선스 활성화에 실패했습니다. 라이선스 키와 네트워크 연결을 확인하세요.", "Failed to export file: {{error}}": "파일 내보내기 실패: {{error}}", "Failed to fetch models": "모델을 가져오는 데 실패했습니다", "Failed to save file: {{error}}": "파일 저장 실패: {{error}}", "FAQs": "자주 묻는 질문", "Favorite": "즐겨찾기", "Feedback": "피드백", "Fetch": "가져오기", "File saved to {{uri}}": "파일이 {{uri}}에 저장되었습니다.", "File type not supported. Supported types include txt, md, html, doc, docx, pdf, excel, pptx, csv, and all text-based files, including code files.": "지원되지 않는 파일 형식입니다. 지원되는 형식은 txt, md, html, doc, docx, pdf, excel, pptx, csv 및 모든 코드 파일을 포함한 모든 텍스트 기반 파일입니다.", "Focus on the Input Box": "입력 상자에 초점 맞추기", "Focus on the Input Box and Enter Web Browsing Mode": "입력 상자에 포커스를 맞추고 웹 브라우징 모드로 진입", "Follow me on Twitter(X)": "트위터(X)에서 나를 팔로우하세요", "Follow System": "시스템 따름", "Font Size": "글꼴 크기", "font size changed, effective after next launch": "글꼴 크기가 변경되었습니다. 다음 실행 이후에 적용됩니다", "Format": "형식", "Full-text search of chat history (coming soon)": "채팅 기록의 전체 텍스트 검색 (곧 출시 예정)", "Function": "기능", "General Settings": "일반 설정", "Generate More Images Below": "아래에서 더 많은 이미지 생성", "Get API Key": "API 키 가져오기", "Get better connectivity and stability with the Chatbox desktop application. <a>Download now</a>.": "Chatbox 데스크톱 앱을 사용하여 더 나은 연결과 안정성을 얻으세요. <a>지금 다운로드</a>.", "Get License": "라이선스 가져오기", "get more": "추가 구매", "Github": "<PERSON><PERSON><PERSON>", "Harmful or offensive content": "해로운 또는 비속한 내용", "Hassle-free setup": "번거로움 없는 설정", "Hate speech or harassment": "증오 발언 또는 괴롭힘", "Here you can add and manage various custom model providers. As long as the provider's API is compatible with the selected API mode, you can seamlessly connect and use it within Chatbox.": "여기에서 다양한 사용자 정의 모델 공급자를 추가하고 관리할 수 있습니다. 공급자의 API가 선택한 API 모드와 호환되는 한 Chatbox 내에서 원활하게 연결하고 사용할 수 있습니다.", "High": "높음", "Home Page": "홈 페이지", "Homepage": "홈페이지", "Hotkeys": "단축키", "How to use?": "사용 방법?", "Ideal for both work and educational scenarios": "일과 교육적인 시나리오에 모두 이상적입니다", "Ideal for work and study": "일과 공부에 이상적", "Image Creator": "이미지 생성기", "Image Creator Intro": "안녕하세요! 저는 Chatbox Image Creator, 당신의 말을 환상적인 시각 이미지로 바꿔드리는 예술적 AI 동반자입니다. 상상할 수 있다면 저는 그것을 만들 수 있습니다—매혹적인 풍경, 역동적인 캐릭터, 앱 아이콘, 또는 추상적인 개념까지.\n\n저는 조용한 로봇이니, **그저 마음에 떠오르는 이미지에 대한 설명을 말해주세요**, 그러면 저는 모든 픽셀을 집중하여 당신의 비전을 현실로 만들겠습니다.\n\n자, 예술을 만들어 봅시다!", "Image Style": "이미지 스타일", "Import and Restore": "가져오기 및 복원", "Import failed, unsupported data format": "가져오기 실패, 지원하지 않는 데이터 형식", "Import from JSON in clipboard": "클립보드에서 JSON 가져오기", "Import MCP servers from JSON in your clipboard": "클립보드에 있는 JSON에서 MCP 서버 가져오기", "Importing...": "가져오는 중...", "Improve Network Compatibility": "네트워크 호환성 개선", "Inject default metadata": "기본 메타데이터 주입", "Insert a New Line into the Input Box": "입력 상자에 새 줄 삽입", "Instruction (System Prompt)": "지시 (시스템 프롬프트)", "Invalid request parameters detected. Please try again later. Persistent failures may indicate an outdated software version. Consider upgrading to access the latest performance improvements and features.": "잘못된 요청 매개변수가 감지되었습니다. 나중에 다시 시도하세요. 지속적인 실패는 오래된 소프트웨어 버전을 나타낼 수 있습니다. 최신 성능 향상 및 기능에 액세스하려면 업그레이드를 고려하세요.", "It only takes a few seconds and helps a lot.": "이 작업은 단 몇 초 만에 도움이 됩니다.", "Keep only the Top N Conversations in List and Permanently Delete the Rest": "목록에서 상위 <0>{{n}}</0>개의 대화만 유지하고 나머지는 영구적으로 삭제", "Key Combination": "단축키 조합", "Keyboard Shortcuts": "키보드 단축키", "Language": "언어", "Last Session": "마지막 세션", "LaTeX Rendering (Requires Markdown)": "LaTeX 렌더링 (마크다운 필요)", "Launch at system startup": "시스템 시작 시 자동 시작", "License Activated": "라이선스 활성화됨", "License expired, please check your license key": "라이선스가 만료되었습니다. 라이선스 키를 확인하세요", "License Expiry": "라이선스 만료", "License not found, please check your license key": "라이선스를 찾을 수 없습니다. 라이선스 키를 확인하세요", "License Plan Overview": "라이선스 플랜 개요", "lifetime license": "평생 라이선스", "Light Mode": "라이트 모드", "Loading webpage...": "웹 페이지를 로드하는 중...", "Local (stdio)": "로컬 (stdio)", "Local Mode": "로컬 모드", "Low": "낮음", "Make sure you have the following command installed:": "다음 명령이 설치되어 있는지 확인하세요:", "Manage License and Devices": "라이선스 및 기기 관리", "Manually": "수동으로", "Markdown Rendering": "마크다운 렌더링", "Max Message Count in Context": "컨텍스트 내 최대 메시지 수", "max tokens in context": "컨텍스트 내 최대 토큰 수", "max tokens to generate": "생성할 최대 토큰 수", "Maybe Later": "나중에 하기", "MCP server added": "MCP 서버 추가됨", "MCP server for accessing arXiv papers": "arXiv 논문 접근용 MCP 서버", "MCP Settings": "MCP 설정", "Medium": "중간", "Mermaid Diagrams & Charts Rendering": "Mermaid 다이어그램 및 차트 렌더링", "meticulous": "세심한", "Misleading information": "오해를 불러일으키는 정보", "Mobile devices temporarily do not support local parsing of this file type. Please use text files (txt, markdown, etc.) or use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis.": "모바일 기기에서는 이 파일 형식의 로컬 파싱을 일시적으로 지원하지 않습니다. 텍스트 파일(txt, markdown 등)을 사용하시거나, 클라우드 기반 문서 분석을 위해 <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing>를 이용해 주세요.", "model": "모델", "Model": "모델", "Model ID": "모델 ID", "Model Provider": "모델 공급자", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "AI 응답의 창의성을 수정하십시오. 값이 높을수록 답변이 더 무작위하고 매력적으로 변하며, 값이 낮을수록 더 큰 안정성과 신뢰성을 보장합니다.", "More Images": "더 많은 이미지", "Move to Conversations": "대화로 이동", "My Assistant": "내 어시스턴트", "My Copilots": "내 동반자", "name": "이름", "Name": "이름", "Natural": "리얼리스틱", "Navigate to the Next Conversation": "다음 대화로 이동", "Navigate to the Next Option (in search dialog)": "다음 옵션으로 이동 (검색 대화 상자에서)", "Navigate to the Previous Conversation": "이전 대화로 이동", "Navigate to the Previous Option (in search dialog)": "이전 옵션으로 이동 (검색 대화 상자에서)", "Navigate to the Specific Conversation": "특정 대화로 이동", "network error tips": "네트워크 오류가 발생했습니다. 현재 네트워크 상태와 {{host}}와의 연결을 확인하십시오.", "Network Proxy": "네트워크 프록시", "network proxy error tips": "프록시 주소를 설정하여 {{proxy}}로 설정하셨기 때문에 프록시 서버가 정상적으로 작동하는지 확인하거나 설정에서 프록시 주소를 삭제하는 것을 고려해 주세요.", "New": "새로 만들기", "New Chat": "새로운 채팅", "New Images": "새 이미지", "New Thread": "새 스레드", "Nickname": "별명", "No": "아니요", "No eligible models available": "사용 가능한 적격 모델이 없습니다.", "No Limit": "제한 없음", "No MCP servers parsed from clipboard": "클립보드에서 파싱된 MCP 서버가 없습니다.", "No permission to write file": "파일 쓰기 권한 없음", "No results found": "결과가 없습니다", "No search results found. Please use another <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton> or try again later.": "검색 결과가 없습니다. 다른 <OpenExtensionSettingButton>검색 제공자</OpenExtensionSettingButton>를 사용하거나 나중에 다시 시도하세요.", "None": "없음", "not available in browser": "이 기능은 웹 브라우저에서는 사용할 수 없으며, 모든 기능을 얻으려면 데스크톱 앱을 다운로드하십시오.", "Not set": "설정되지 않음", "Nothing found...": "찾을 수 없음...", "Number of Images per Reply": "답변당 이미지 수", "One-click MCP servers for Chatbox AI subscribers": "Chatbox AI 구독자용 원클릭 MCP 서버", "OpenAI API Compatible": "OpenAI API 호환", "Operations": "작업", "optional": "선택 사항", "or": "또는", "Or become a sponsor": "혹은 후원자가 되십시오", "Other concerns": "기타 걱정", "Paste long text as a file": "긴 텍스트를 파일로 붙여넣기", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "긴 텍스트를 붙여넣으면 파일로 삽입되어 대화를 깨끗하게 유지하고 프롬프트 캐싱을 사용하여 토큰 사용량을 줄입니다.", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF, DOC, PPT, XLS, TXT, 코드...", "Please describe the content you want to report (Optional)": "신고하려는 내용을 설명해 주세요 (선택 사항)", "Please ensure that the Remote LM Studio Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "원격 LM Studio 서비스가 원격으로 연결할 수 있는지 확인하십시오. 자세한 내용은 <a>이 튜토리얼</a>을 참조하십시오.", "Please ensure that the Remote Ollama Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "원격 Ollama 서비스가 원격으로 연결할 수 있는지 확인하십시오. 자세한 내용은 <a>이 튜토리얼</a>을 참조하십시오.", "Please note that as a client tool, Chatbox cannot guarantee the quality of service and data privacy of the model providers. If you are looking for a stable, reliable, and privacy-protecting model service, consider <a>Chatbox AI</a>.": "클라이언트 도구로서 Chatbox는 모델 공급자의 서비스 품질과 데이터 개인 정보 보호를 보장할 수 없습니다. 안정적이고 신뢰할 수 있으며 개인 정보를 보호하는 모델 서비스를 찾고 있다면 <a>Chatbox AI</a>를 고려해 보세요.", "Please select a model": "모델을 선택해주세요", "Please test before saving": "저장하기 전에 테스트해주세요", "Please wait about 20 seconds": "약 20초간 기다려 주세요", "pre-sale discount": "사전 판매 할인", "premium": "프리미엄", "Premium Activation": "프리미엄 활성화", "Premium License Activated": "프리미엄 라이선스 활성화됨", "Premium License Key": "프리미엄 라이선스 키", "Press hotkey": "단축키 입력", "Preview": "미리보기", "Privacy Policy": "개인정보 처리방침", "Prompt": "프롬프트", "Provider not found": "제공자를 찾을 수 없음", "proxy": "프록시", "Proxy Address": "프록시 주소", "Purchase": "구매", "QR Code": "QR 코드", "Quota Reset": "할당량 초기화", "quote": "인용", "Rate Now": "지금 평점 주기", "Reading file...": "파일 읽는 중...", "Reasoning": "추론", "RedNote": "빨간 노트", "Refresh": "새로 고침", "regenerate": "재생성", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "AI에 전송되는 역사적 메시지의 양을 조정하고, 이해의 깊이와 응답의 효율성 간의 조화로운 균형을 찾습니다.", "Remote (http/sse)": "원격 (http/sse)", "rename": "이름 변경", "Reply Again": "다시 답변하기", "Reply Again Below": "아래에 다시 답변하기", "report": "신고", "Report Content": "신고 내용", "Report Content ID": "신고 내용 ID", "Report Type": "신고 유형", "reset": "재설정", "Reset": "재설정", "Reset All Hotkeys": "모든 단축키 재설정", "Reset to Default": "기본값으로 재설정", "Reset to Global Settings": "전역 설정으로 재설정", "Result": "결과", "Retrieve License": "라이선스 검색", "Retrieves up-to-date documentation and code examples for any library.": "어떤 라이브러리든 최신 문서와 코드 예시를 가져옵니다.", "Roadmap": "로드맵", "save": "저장", "Save": "저장", "Save & Resend": "저장 및 재전송", "Scope": "범위", "Search": "검색", "Search All Conversations": "모든 대화에서 검색", "Search in Current Conversation": "현재 대화에서 검색", "Search models": "모델 검색", "Search Provider": "검색 제공자", "Search query": "검색 질의", "Search Term Construction Model": "검색어 구성 모델", "Search...": "검색...", "Select and configure an AI model provider": "AI 모델 공급자를 선택하고 구성하세요", "Select File": "파일 선택", "Select Model": "모델 선택", "Select the Current Option (in search dialog)": "현재 옵션 선택 (검색 대화 상자에서)", "send": "전송", "Send": "전송", "Send Without Generating Response": "응답 생성 없이 전송", "Setting the avatar for Copilot": "동반자의 아바타 설정", "settings": "설정", "Settings": "설정", "Sexual content": "성적인 내용", "Share File": "파일 공유", "Share with Chatbox": "Chatbox와 공유", "Show all ({{x}})": "모두 보기 ({{x}})", "show first token latency": "첫 토큰 지연 표시", "Show in Thread List": "스레드 목록에 표시", "show message timestamp": "메시지 타임스탬프 표시", "show message token count": "메시지 토큰 수 표시", "show message token usage": "메시지 토큰 사용량 표시", "show message word count": "메시지 단어 수 표시", "show model name": "모델 이름 표시", "Show/Hide the Application Window": "애플리케이션 창 표시/숨기기", "Show/Hide the Search Dialog": "검색 대화 상자 표시/숨기기", "Smartest AI-Powered Services for Rapid Access": "빠른 액세스를 위한 가장 스마트한 AI 서비스", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model or use the recommended <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.": "현재 모델 {{model}} API는 이미지 이해를 지원하지 않습니다. 이미지를 보내려면 다른 모델로 전환하거나 권장되는 <OpenMorePlanButton>Chatbox AI 모델</OpenMorePlanButton>을 사용하세요.", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model.": "현재 모델 {{model}} API는 이미지 이해를 지원하지 않습니다. 이미지를 보내려면 다른 모델로 전환하세요.", "Spam or advertising": "스팸 또는 광고", "Special thanks to the following sponsors:": "다음 후원자에게 특별히 감사드립니다:", "Specific model settings": "특정 모델 설정", "Specific model settings configured for this conversation": "이 대화에 구성된 특정 모델 설정", "Spell Check": "맞춤법 검사", "star": "별 표시", "Start a New Thread": "새 스레드 시작", "Start Setup": "설정 시작", "Startup Page": "시작 페이지", "stop generating": "생성 중지", "submit": "제출", "Support for ChatBox development": "ChatBox 개발 지원", "Support jpg or png file smaller than 5MB": "5MB 미만의 jpg 또는 png 파일 지원", "Supports a variety of advanced AI models": "다양한 고급 AI 모델을 지원합니다", "Survey": "설문 조사", "Switch": "전환", "system": "시스템", "Tavily API Key": "Tavily API 키", "temperature": "온도", "Temperature": "온도", "Test": "테스트", "Thank you for your report": "보고서에 감사드립니다.", "The {{model}} API does not support files. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API는 파일을 지원하지 않습니다. 로컬 처리를 위해 <LinkToHomePage>데스크톱 앱</LinkToHomePage>을 다운로드하세요.", "The {{model}} API does not support files. Please use <LinkToAdvancedFileProcessing>Chatbox AI models</LinkToAdvancedFileProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API는 파일을 지원하지 않습니다. 대신 <LinkToAdvancedFileProcessing>Chatbox AI 모델</LinkToAdvancedFileProcessing>을 사용하거나 <LinkToHomePage>데스크톱 앱</LinkToHomePage>을 다운로드하여 로컬 처리를 수행하세요.", "The {{model}} API does not support links. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API는 링크를 지원하지 않습니다. 로컬 처리를 위해 <LinkToHomePage>데스크톱 앱</LinkToHomePage>을 다운로드하세요.", "The {{model}} API does not support links. Please use <LinkToAdvancedUrlProcessing>Chatbox AI models</LinkToAdvancedUrlProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "{{model}} API는 링크를 지원하지 않습니다. 대신 <LinkToAdvancedUrlProcessing>Chatbox AI 모델</LinkToAdvancedUrlProcessing>을 사용하거나 <LinkToHomePage>데스크톱 앱</LinkToHomePage>을 다운로드하여 로컬 처리를 수행하세요.", "The {{model}} API doesn't support document understanding. You can download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "현재 모델 {{model}} API는 문서 이해를 지원하지 않습니다. 로컬 문서 분석을 위해 <LinkToHomePage>Chatbox 데스크톱 앱</LinkToHomePage>을 다운로드하세요.", "The {{model}} API doesn't support document understanding. You can use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis, or download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "현재 모델 {{model}} API는 문서 이해를 지원하지 않습니다. 클라우드 기반 문서 분석을 위해 <LinkToAdvancedFileProcessing>Chatbox AI 서비스</LinkToAdvancedFileProcessing>를 사용하거나 <LinkToHomePage>Chatbox 데스크톱 앱</LinkToHomePage>을 다운로드하여 로컬 문서 분석을 수행하세요.", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code).": "{{model}} API 자체는 파일 전송을 지원하지 않습니다. 로컬 처리의 복잡성으로 인해 Chatbox는 텍스트 기반 파일(코드 포함)만 처리합니다.", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code). For additional file formats and enhanced document understanding capabilities, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> is recommended.": "{{model}} API 자체는 파일 전송을 지원하지 않습니다. 로컬 처리의 복잡성으로 인해 Chatbox는 텍스트 기반 파일(코드 포함)만 처리합니다. 추가 파일 형식 및 향상된 문서 이해 기능을 지원하기 위해 <LinkToAdvancedFileProcessing>Chatbox AI 서비스</LinkToAdvancedFileProcessing>를 권장합니다.", "The {{model}} API itself does not support web browsing. Supported models: {{supported_web_browsing_models}}": "현재 모델 {{model}} API는 웹 브라우징을 지원하지 않습니다. 지원되는 모델: {{supported_web_browsing_models}}", "The {{model}} API itself does not support web browsing. Supported models: <OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}": "현재 모델 {{model}} API는 웹 브라우징을 지원하지 않습니다. 지원되는 모델: <OpenMorePlanButton>Chatbox AI 모델</OpenMorePlanButton>, {{supported_web_browsing_models}}", "The cache data for the file was not found. Please create a new conversation or refresh the context, and then send the file again.": "파일의 캐시 데이터를 찾을 수 없습니다. 새 대화를 만들거나 컨텍스트를 새로 고친 후 파일을 다시 보내야 합니다.", "The current model {{model}} does not support sending links.": "현재 모델 {{model}}은 링크 전송을 지원하지 않습니다.", "The current model {{model}} does not support sending links. Currently supported models: Chatbox AI models.": "현재 모델 {{model}}은 링크 전송을 지원하지 않습니다. 현재 지원되는 모델: Chatbox AI 모델.", "The file size exceeds the limit of 50MB. Please reduce the file size and try again.": "파일 크기가 50MB 제한을 초과합니다. 파일 크기를 줄이고 다시 시도하세요.", "The file you sent has expired. To protect your privacy, all file-related cache data has been cleared. You need to create a new conversation or refresh the context, and then send the file again.": "보낸 파일이 만료되었습니다. 개인 정보 보호를 위해 모든 파일 관련 캐시 데이터가 지워졌습니다. 새 대화를 만들거나 컨텍스트를 새로 고친 후 파일을 다시 보내야 합니다.", "The Image Creator plugin has been activated for the current conversation": "이 대화에 이미지 생성기 플러그인이 활성화되었습니다", "The license key you entered is invalid. Please check your license key and try again.": "입력한 라이선스 키가 유효하지 않습니다. 라이선스 키를 확인하고 다시 시도하세요.", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "topP 매개변수는 AI 응답의 다양성을 조절합니다: 낮은 값은 결과물을 더 집중적이고 예측 가능하게 만들고, 높은 값은 더 다양하고 창의적인 답변을 가능하게 합니다.", "Theme": "테마", "Thinking": "생각 중", "Thinking Budget": "사고 예산", "Thinking Budget only works for 2.0 or later models": "사고 예산은 2.0 이상 모델에서만 작동합니다", "Thinking Budget only works for 3.7 or later models": "사고 예산은 3.7 이상 모델에서만 작동합니다", "Thinking Effort": "생각 노력", "Thinking Effort only works for OpenAI o-series models": "사고 노력은 OpenAI o-series 모델에서만 작동합니다", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "이 라이선스 키는 활성화 제한에 도달했습니다. 이전 기기를 비활성화하려면 <a>여기를 클릭</a>하여 라이선스 및 기기를 관리하세요.", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "이 서버는 LLM이 웹 페이지에서 콘텐츠를 검색하고 처리할 수 있도록 지원하며, HTML을 마크다운으로 변환하여 더 쉽게 활용할 수 있도록 합니다.", "Thread History": "스레드 기록", "To access locally deployed model services, please install the Chatbox desktop version": "로컬에 배포된 모델 서비스에 접근하려면 Chatbox 데스크톱 버전을 설치해 주세요", "Toggle": "전환", "token": "토큰", "Tool use": "도구 사용", "Tool Use": "도구 사용", "Tools": "도구", "Top P": "Top P", "Type": "입력", "Type a command or search": "명령 입력이나 검색을 입력하세요", "Type your question here...": "여기에 질문을 입력하세요...", "unknown error tips": "알 수 없는 오류입니다. AI 설정과 계정 상태를 확인하거나 <0>여기를 클릭하여 FAQ 문서를 확인</0>하십시오.", "Unlock Copilot Avatar by Upgrading to Premium Edition": "프리미엄 버전으로 업그레이드하여 동반자 아바타 잠금 해제", "Unsaved settings": "저장되지 않은 설정", "unstar": "별 표시 해제", "Untitled": "제목 없음", "Update Available": "업데이트 가능", "Upload Image": "이미지 업로드", "Upon import, changes will take effect immediately and existing data will be overwritten": "가져오기 후 변경사항이 즉시 적용되며 기존 데이터는 덮어쓰게 됩니다", "Use My Own API Key / Local Model": "내 자체 API 키 / 로컬 모델 사용", "Use proxy to resolve CORS and other network issues": "프록시를 사용하여 CORS 및 기타 네트워크 문제 해결", "user": "사용자", "User Avatar": "사용자 아바타", "User Terms": "이용약관", "version": "버전", "View All Copilots": "모든 동반자 보기", "View historical threads": "과거 스레드 보기", "View More Plans": "더 많은 플랜 보기", "Violence or dangerous content": "폭력적 또는 위험한 내용", "Vision": "비전", "Vision capability is not enabled for Model {{model}}. Please enable it in <OpenSettingButton>provider settings</OpenSettingButton> or switch to a model that supports vision.": "모델 {{model}}에 대해 시각 기능이 활성화되어 있지 않습니다. <OpenSettingButton>제공자 설정</OpenSettingButton>에서 활성화하거나 시각을 지원하는 모델로 전환하세요.", "Vision, Drawing, File Understanding and more": "비전, 그림, 파일 이해 등", "Vivid": "아티스틱", "Web Browsing": "웹 브라우징", "Web browsing (coming soon)": "웹 브라우징 (곧 출시 예정)", "Web Browsing...": "웹 브라우징...", "Web Search": "인터넷 검색", "WeChat": "위챗", "What can I help you with today?": "오늘 무엇을 도와드릴까요?", "Yes": "예", "You are already a Premium user": "이미 프리미엄 사용자입니다", "You have exceeded the rate limit for the Chatbox AI service. Please try again later.": "Chatbox AI 서비스의 요청 제한을 초과했습니다. 나중에 다시 시도하세요.", "You have no more Chatbox AI quota left this month.": "이번 달 Chatbox AI 쿼터가 모두 소진되었습니다.", "You have reached your monthly quota for the {{model}} model. Please <OpenSettingButton>go to Settings</OpenSettingButton> to switch to a different model, view your quota usage, or upgrade your plan.": "{{model}} 모델의 월간 할당량에 도달했습니다. 다른 모델로 전환하거나 할당량 사용 현황을 보거나 플랜을 업그레이드하려면 <OpenSettingButton>설정으로 이동</OpenSettingButton>하세요.", "You have selected Chatbox AI as the model provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different model provider.": "Chatbox AI를 모델 공급자로 선택했지만 라이선스 키가 아직 입력되지 않았습니다. <OpenSettingButton>여기를 클릭하여 설정을 열고</OpenSettingButton> 라이선스 키를 입력하거나 다른 모델 공급자를 선택하세요.", "You have selected Chatbox AI as the search provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton>.": "Chatbox AI를 검색 제공자로 선택했지만 라이선스 키가 아직 입력되지 않았습니다. <OpenSettingButton>여기를 클릭하여 설정을 열고</OpenSettingButton> 라이선스 키를 입력하거나 다른 <OpenExtensionSettingButton>검색 제공자</OpenExtensionSettingButton>를 선택하세요.", "You have selected Tavily as the search provider, but an API key has not been entered yet. Please <OpenExtensionSettingButton>click here to open Settings</OpenExtensionSettingButton> and enter your API key, or choose a different search provider.": "Tavily를 검색 제공자로 선택했지만 API 키가 아직 입력되지 않았습니다. <OpenExtensionSettingButton>여기를 클릭하여 설정을 열고</OpenExtensionSettingButton> API 키를 입력하거나 다른 검색 제공자를 선택하세요.", "You have unsaved settings. Are you sure you want to leave?": "저장되지 않은 설정이 있습니다. 나가시겠습니까?", "Your ChatboxAI subscription already includes access to models from various providers. There's no need to switch providers - you can select different models directly within ChatboxAI. Switching from ChatboxAI to other providers will require their respective API keys. <button>Back to ChatboxAI</button>": "ChatboxAI 구독에는 이미 다양한 제공업체의 모델에 대한 접근 권한이 포함되어 있습니다. 제공업체를 전환할 필요가 없으며 ChatboxAI 내에서 직접 다른 모델을 선택할 수 있습니다. ChatboxAI에서 다른 제공업체로 전환하려면 해당 API 키가 필요합니다. <button>ChatboxAI로 돌아가기</button>", "Your current License (Chatbox AI Lite) does not support the {{model}} model. To use this model, please <OpenMorePlanButton>upgrade</OpenMorePlanButton> to Chatbox AI Pro or a higher-tier package. Alternatively, you can switch to a different model by <OpenSettingButton>accessing the settings</OpenSettingButton>.": "현재 라이선스(Chatbox AI Lite)는 {{model}} 모델을 지원하지 않습니다. 이 모델을 사용하려면 <OpenMorePlanButton>업그레이드</OpenMorePlanButton>하여 Chatbox AI Pro 또는 더 높은 티어 패키지로 전환하세요. 또는 <OpenSettingButton>설정에 접근</OpenSettingButton>하여 다른 모델로 전환할 수 있습니다.", "Your license has expired. Please check your subscription or purchase a new one.": "라이선스가 만료되었습니다. 구독을 확인하거나 새로운 라이선스를 구매하세요.", "Your rating on the App Store would help make Chatbox even better!": "App Store에서 평점을 주시면 Chatbox을 더 나은 것으로 만들 수 있습니다!"}