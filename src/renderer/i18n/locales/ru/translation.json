{"[Ctrl+Enter] Save, [Ctrl+Shift+Enter] Save and Resend": "[Ctrl+Enter] Сохранить, [Ctrl+Shift+Enter] Сохранить и отправить заново", "[Enter] send, [Shift+Enter] line break, [Ctrl+Enter] send without generating": "[Enter] отправить, [Shift+Enter] перенос строки, [Ctrl+Enter] отправить без генерации", "{{count}} MCP servers imported": "{{count}} MCP серверов импортировано", "About": "О нас", "About Chatbox": "О приложении Chatbox", "about-introduction": "Простой в использовании настольный клиент искусственного интеллекта, поддерживающий несколько передовых моделей искусственного интеллекта, превращающий передовые технологии искусственного интеллекта в простой и удобный инструмент для повышения производительности.", "about-slogan": "Увеличьте свою эффективность с помощью искусственного интеллекта, вашего надежного помощника в работе и обучении", "Access to all future premium feature updates": "Доступ ко всем будущим обновлениям премиум-функций", "Action": "Действие", "Activate License": "Активировать лицензию", "Activating...": "Активация...", "Add": "Добавить", "Add at least one model to check connection": "Добавьте хотя бы одну модель для проверки соединения", "Add Custom Provider": "Добавить пользовательский поставщик", "Add Custom Server": "Добавить пользовательский сервер", "Add MCP Server": "Добавить MCP Server", "Add or Import": "Добавить или Импортировать", "Add provider": "Добавить провайдера", "Add Server": "Добавить Сервер", "Add your first MCP server": "Добавить ваш первый MCP сервер", "advanced": "Расширенные", "Advanced": "Рас<PERSON>иренный", "Advanced Mode": "Расширенный режим", "AI Model Provider": "Поставщик модели AI", "ai provider no implemented paint tips": "Текущий провайдер AI модели ({{aiProvider}}) временно не поддерживает функцию рисования. В настоящее время данная функция поддерживается только Chatbox AI, OpenAI и Azure OpenAI. Если это необходимо, пожалуйста, <0>перейдите в настройки</0>, чтобы сменить провайдера AI модели.", "AI Settings": "Настройки ИИ", "All data is stored locally, ensuring privacy and rapid access": "Все данные хранятся локально, обеспечивая конфиденциальность и быстрый доступ", "All major AI models in one subscription": "Все основные AI-модели в одной подписке", "All threads": "Все темы", "already existed": "уже существует", "An easy-to-use AI client app": "Простое в использовании приложение для работы с искусственным интеллектом", "An error occurred while processing your request. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "Произошла ошибка при обработке вашего запроса. Пожалуйста, попробуйте снова позже. Если эта ошибка продолжается, пожалуйста, отправьте электронное письмо на <EMAIL> для получения поддержки.", "An error occurred while sending the message.": "Произошла ошибка при отправке сообщения.", "An MCP server implementation that provides a tool for dynamic and reflective problem-solving through a structured thinking process.": "Реализация сервера MCP, которая предоставляет инструмент для динамического и рефлексивного решения проблем посредством структурированного мыслительного процесса.", "An unknown error occurred. Please try again later. If this error continues, please send an <NAME_EMAIL> for support.": "Произошла неизвестная ошибка. Пожалуйста, попробуйте снова позже. Если эта ошибка продолжается, пожалуйста, отправьте электронное письмо на <EMAIL> для получения поддержки.", "any number key": "любая числовая клавиша", "api error tips": "Произошла ошибка с {{aiProvider}}, что обычно вызвано неверными настройками или проблемами с учетной записью. Пожалуйста, проверьте настройки ИИ и статус вашей учетной записи, или <0>нажмите здесь, чтобы просмотреть документ FAQ</0>.", "api host": "Хост API", "API Host": "Хост API", "api key": "Ключ API", "API Key": "API-к<PERSON><PERSON><PERSON>", "API KEY & License": "API KEY & Лицензия", "API key invalid!": "Недействительный API-ключ!", "API Key is required to check connection": "Для проверки соединения требуется API ключ.", "API Mode": "Режим API", "api path": "путь API", "API Path": "Путь API", "Are you sure you want to delete this server?": "Вы уверены, что хотите удалить этот сервер?", "Arguments": "Аргументы", "assistant": "Ассистент", "Attach Image": "Прикрепить изображение", "Attach Link": "Прикрепить ссылку", "Auther Message": "Я создал Chatbox для своего собственного использования, и рад видеть, что так много людей получают удовольствие от него! Если вы хотите поддержать разработку, пожертвование будет очень ценным, хотя это абсолютно необязательно. Большое спасибо, Benn", "Auto": "Авто", "Auto (Use Chat Model)": "Авто (использовать модель чата)", "Auto (Use Last Used)": "Авто (использовать последний использованный)", "Auto-collapse code blocks": "Автоматически скрывать блоки кода", "Auto-Generate Chat Titles": "Автоматически генерировать названия чатов", "Auto-preview artifacts": "Автоматический предпросмотр артефактов", "Automatic updates": "Автоматические обновления", "Automatically render generated artifacts (e.g., HTML with CSS, JS, Tailwind)": "Автоматически рендерить сгенерированные артефакты (например, HTML с CSS, JS, Tailwind)", "Azure API Key": "Ключ Azure API", "Azure API Version": "Версия API Azure", "Azure Dall-E Deployment Name": "Название развертывания модели Azure Dall-E", "Azure Deployment Name": "Имя развертывания Azure", "Azure Endpoint": "Конечная точка Azure", "Back to Previous": "Вернуться к Предыдущему", "Beta updates": "Бета обновления", "Browsing and retrieving information from the internet.": "Веб-брау<PERSON><PERSON><PERSON>, просматривает и извлекает информацию из интернета.", "Builtin MCP Servers": "Встроенные MCP Серверы", "Can be activated on up to 5 devices": "Может быть активировано на до 5 устройств", "cancel": "Отмена", "Cancel": "Отмена", "cannot be empty": "не может быть пустым", "Capabilities": "Возможности", "Changelog": "Список изменений", "chat": "Чат", "Chat History": "История чата", "Chat Settings": "Настройки чата", "Chatbox AI Advanced Model Quota": "Квота расширенной модели Chatbox AI", "Chatbox AI Cloud": "Chatbox AI Облако", "Chatbox AI Image Quota": "Квота на изображения Chatbox AI", "Chatbox AI License": "Лицензия Chatbox AI", "Chatbox AI offers a user-friendly AI solution to help you enhance productivity": "Chatbox AI предлагает простое в использовании решение на основе искусственного интеллекта, которое помогает повысить производительность", "Chatbox AI Quota": "Chatbox AI Квота", "Chatbox AI Standard Model Quota": "Квота стандартной модели Chatbox AI", "Chatbox Featured": "Рекомендуемые Chatbox", "Chatbox respects your privacy and only uploads anonymous error data and events when necessary. You can change your preferences at any time in the settings.": "Chatbox уважает вашу конфиденциальность и загружает только анонимные данные об ошибках и событиях при необходимости. Вы можете изменить свои предпочтения в любое время в настройках.", "Chatbox will automatically use this model to construct search term.": "Chatbox будет автоматически использовать эту модель для построения поисковых запросов.", "Chatbox will automatically use this model to rename threads.": "Chatbox будет автоматически использовать эту модель для переименования тем.", "Chatbox will use this model as the default for new chats.": "Chatbox будет использовать эту модель по умолчанию для новых чатов.", "ChatGLM-6B URL Helper": "Поддерживает <0>API интерфейс</0> для модели с открытым исходным кодом, <1>ChatGLM-6B</1>", "ChatGLM-6B Warnning for Chatbox-Web": "Похоже, вы используете веб-версию Chatbox, которая может столкнуться с проблемами междоменного доступа или другими сетевыми проблемами с ChatGLM-6B. Скачайте и используйте клиент Chatbox, чтобы избежать потенциальных проблем.", "Check": "Проверить", "Check Update": "Проверить обновления", "Child-inappropriate content": "Содержимое, неприемлемое для детей", "clean": "Очистить", "clean it up": "Очистить", "Clear All Messages": "Очистить Все Сообщения", "Clear Conversation List": "Очистить список разговоров", "Click here to set up": "Нажмите здесь, чтобы настроить", "Click to view license details and quota usage": "Нажмите, чтобы просмотреть детали лицензии и использование квоты", "close": "Закрыть", "Collapse": "Свернуть", "Coming soon": "Скоро будет", "Command": "Команда", "Configure MCP server manually": "Настроить сервер MCP вручную", "Confirm": "Подтвердить", "Confirm deletion?": "Подтвердить удаление?", "Confirm to delete this custom provider?": "Подтвердите удаление этого пользовательского поставщика?", "Confirm?": "Подтвердить?", "Connection failed!": "Ошибка соединения!", "Connection successful!": "Соединение успешно!", "Connection to {{aiProvider}} failed. This typically occurs due to incorrect configuration or {{aiProvider}} account issues. Please <buttonOpenSettings>check your settings</buttonOpenSettings> and verify your {{aiProvider}} account status, or purchase a <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing> to unlock all advanced models instantly without any configuration.": "Соединение с {{aiProvider}} не удалось. Обычно это происходит из-за неправильной конфигурации или проблем с аккаунтом {{aiProvider}}. Пожалуйста, <buttonOpenSettings>проверьте ваши настройки</buttonOpenSettings> и проверьте статус вашего аккаунта {{aiProvider}}, или приобретите <LinkToLicensePricing>Chatbox AI License</LinkToLicensePricing>, чтобы мгновенно разблокировать все расширенные модели без какой-либо конфигурации.", "Continue this thread": "Продолжить эту тему", "Continue this Thread": "Продолжить эту Тему", "Conversation Settings": "Настройки разговора", "copied to clipboard": "Скопировано в буфер обмена", "Copilot Avatar URL": "URL аватара Copilot", "Copilot Name": "<PERSON><PERSON><PERSON>pi<PERSON>", "Copilot Prompt": "Подсказка Copilot", "Copilot Prompt Demo": "Вы - переводчик, и ваша задача - переводить с нерусского на русский", "copy": "Копировать", "Create a New Conversation": "Создать новый разговор", "Create a New Image-Creator Conversation": "Создать новый разговор с Image-Creator", "Create New Copilot": "Создать нового Copilot", "creative": "Творческий", "Current conversation configured with specific model settings": "Текущий разговор сконфигурирован со специфическими настройками модели", "Current thread": "Текущая тема", "Custom": "Пользовательский", "Custom MCP Servers": "Настраиваемые MCP Серверы", "Custom Model": "Пользовательская модель", "Custom Model Name": "Имя пользовательской модели", "Customize settings for the current conversation": "Настроить настройки для текущего разговора", "Dark Mode": "Темный режим", "Data Backup": "Резервное копирование данных", "Data Backup and Restore": "Резервное копирование и восстановление данных", "Data Restore": "Восстановление данных", "Deactivate": "Деактивировать", "Deeply thought": "Глубоко продуманный", "Default Assistant Avatar": "Аватар помощника по умолчанию", "Default Chat Model": "Модель чата по умолчанию", "Default Models": "Модели по умолчанию", "Default Prompt for New Conversation": "Подсказка по умолчанию для нового разговора", "Default Settings for New Conversation": "Настройки по умолчанию для нового разговора", "Default Thread Naming Model": "Модель именования тем по умолчанию", "delete": "Удалить", "Delete": "Удалить", "delete confirmation": "Это действие безвозвратно удалит все несистемные сообщения в {{sessionName}}. Вы уверены, что хотите продолжить?", "Delete Current Session": "Удалить текущую сессию", "Deploy HTML content to EdgeOne Pages and obtaining an accessible public URL.": "Развертывание HTML-контента на EdgeOne Pages и получение доступного публичного URL.", "Description": "Описание", "Details": "Детали", "Disabled": "Отключено", "display": "дисплей", "Display": "Отображение", "Display Settings": "Настройки отображения", "Donate": "Пожертвовать", "Download": "Скачать", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended for enhanced document processing capabilities and better results.": "Из-за ограничений локальной обработки, <Link>Служба Chatbox AI</Link> рекомендуется для улучшения возможностей обработки документов и получения лучших результатов.", "Due to local processing limitations, <Link>Chatbox AI Service</Link> is recommended to enhance webpage parsing capabilities, especially for dynamic pages.": "Из-за ограничений локальной обработки, <Link>Служба Chatbox AI</Link> рекомендуется для улучшения возможностей анализа веб-страниц, особенно для динамических страниц.", "E-mail": "Электронная почта", "e.g., Model Name, Current Date": "например, название модели, текущая дата", "Easy Access": "Легкий доступ", "edit": "Редактировать", "Edit": "Редактировать", "Edit Avatars": "Редактировать аватары", "Edit default assistant avatar": "Редактировать аватар ассистента по умолчанию", "Edit MCP Server": "Редактировать MCP Сервер", "Edit Model": "Редактировать модель", "Edit Thread Name": "Редактировать название темы", "Edit user avatar": "Редактировать аватар пользователя", "Email Us": "Написать на почту", "Enable optional anonymous reporting of crash and event data": "Включить дополнительную анонимную отправку отчетов о сбоях и событиях", "Enable Thinking": "Включить Мышление", "Enabled": "Включен", "Ending with / ignores v1, ending with # forces use of input address": "Об окончании на / игнорируется v1; окончание на # заставляет использовать введенный адрес", "Enjoying Chatbox?": "Приятно ли использовать Chatbox?", "Enter": "Ввод", "Environment Variables": "Переменные среды", "Error Reporting": "Отчеты об ошибках", "expand": "Развернуть", "Expand": "Развернуть", "Expansion Pack Quota": "Квота пакетов расширений", "Explore (community)": "Исследовать (сообщество)", "Explore (official)": "Исследовать (официальный)", "export": "Экспорт", "Export Chat": "Экспорт Переписки", "Export Selected Data": "Экспорт выбранных данных", "Exporting...": "Экспорт...", "extension": "Расширения", "Failed to activate license, please check your license key and network connection": "Не удалось активировать лицензию, пожалуйста, проверьте свой ключ лицензии и сетевое соединение", "Failed to export file: {{error}}": "Не удалось экспортировать файл: {{error}}", "Failed to fetch models": "Не удалось получить модели", "Failed to save file: {{error}}": "Не удалось сохранить файл: {{error}}", "FAQs": "Часто задаваемые вопросы", "Favorite": "Избранное", "Feedback": "Обратная связь", "Fetch": "Получить", "File saved to {{uri}}": "Файл сохранен в {{uri}}", "File type not supported. Supported types include txt, md, html, doc, docx, pdf, excel, pptx, csv, and all text-based files, including code files.": "Тип файла не поддерживается. Поддерживаемые типы включают txt, md, html, doc, docx, pdf, excel, pptx, csv и все файлы на основе текста, включая файлы кода.", "Focus on the Input Box": "Фокус на поле ввода", "Focus on the Input Box and Enter Web Browsing Mode": "Фокусировать на поле ввода и войти в режим веб-браузера", "Follow me on Twitter(X)": "Подписывайтесь на меня в Twitter (X)", "Follow System": "Следовать системе", "Font Size": "Размер шрифта", "font size changed, effective after next launch": "Размер шрифта изменен, изменения вступят в силу после следующего запуска", "Format": "Формат", "Full-text search of chat history (coming soon)": "Полнотекстовый поиск истории чата (скоро)", "Function": "Функция", "General Settings": "Общие настройки", "Generate More Images Below": "Создать больше изображений ниже", "Get API Key": "Получить API ключ", "Get better connectivity and stability with the Chatbox desktop application. <a>Download now</a>.": "Получите лучшую совместимость и стабильность с помощью приложения Chatbox для рабочего стола. <a>Скачать сейчас</a>.", "Get License": "Получить лицензию", "get more": "Получить больше", "Github": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Harmful or offensive content": "Вредное или неприемлемое содержимое", "Hassle-free setup": "Простая настройка", "Hate speech or harassment": "Неприемлемые высказывания или оскорбления", "Here you can add and manage various custom model providers. As long as the provider's API is compatible with the selected API mode, you can seamlessly connect and use it within Chatbox.": "Здесь вы можете добавлять и управлять различными пользовательскими поставщиками моделей. Пока API-интерфейс поставщика совместим с выбранным режимом API, вы можете без проблем подключиться и использовать его в Chatbox.", "High": "Высокий", "Home Page": "Домашняя страница", "Homepage": "Домашняя страница", "Hotkeys": "Горячие клавиши", "How to use?": "Как использовать?", "Ideal for both work and educational scenarios": "Идеально подходит как для рабочих, так и для учебных сценариев", "Ideal for work and study": "Идеально подходит для работы и учебы", "Image Creator": "Создатель изображений", "Image Creator Intro": "Привет! Я Chatbox Image Creator, ваш художественный AI-соратник, предан созданию поразительных визуальных образов из ваших слов. Если вы можете это мечтать, я могу это создать — от очаровывающих пейзажей, динамичных персонажей, значков приложений до абстракции и за её пределы.\n\nЯ тихий робот, просто **скажите мне описание изображения, которое у вас в голове**, и я сосредоточу все мои пиксели на воплощении вашего видения.\n\nДавайте творить искусство!", "Image Style": "Стиль изображения", "Import and Restore": "Импорт и восстановление", "Import failed, unsupported data format": "Ошибка импорта, неподдерживаемый формат данных", "Import from JSON in clipboard": "Импорт из JSON в буфер обмена", "Import MCP servers from JSON in your clipboard": "Импортировать MCP-серверы из JSON из буфера обмена", "Importing...": "Импорт...", "Improve Network Compatibility": "Улучшить совместимость с сетью", "Inject default metadata": "Вставить метаданные по умолчанию", "Insert a New Line into the Input Box": "Вставить новую строку в поле ввода", "Instruction (System Prompt)": "Инструкция (Системная подсказка)", "Invalid request parameters detected. Please try again later. Persistent failures may indicate an outdated software version. Consider upgrading to access the latest performance improvements and features.": "Обнаружены недопустимые параметры запроса. Пожалуйста, попробуйте снова позже. Постоянные сбои могут указывать на устаревшую версию программного обеспечения. Рассмотрите возможность обновления для доступа к последним улучшениям производительности и функциям.", "It only takes a few seconds and helps a lot.": "Это займет всего несколько секунд и очень поможет.", "Keep only the Top N Conversations in List and Permanently Delete the Rest": "Сохранить только топ <0>{{n}}</0> разговоров в списке и окончательно удалить остальные", "Key Combination": "Комбинация клавиш", "Keyboard Shortcuts": "Горячие клавиши", "Language": "Язык", "Last Session": "Последняя сессия", "LaTeX Rendering (Requires Markdown)": "Отображение LaTeX (требуется Markdown)", "Launch at system startup": "Запуск при запуске системы", "License Activated": "Лицензия активирована", "License expired, please check your license key": "Лицензия истекла, пожалуйста, проверьте ваш ключ лицензии", "License Expiry": "Истечение лицензии", "License not found, please check your license key": "Лицензия не найдена, пожалуйста, проверьте ваш ключ лицензии", "License Plan Overview": "Обзор Лицензионных Планов", "lifetime license": "пожизненная лицензия", "Light Mode": "Светлый режим", "Loading webpage...": "Загрузка веб-страницы...", "Local (stdio)": "Локальный (stdio)", "Local Mode": "Локальный режим", "Low": "Низкий", "Make sure you have the following command installed:": "Убедитесь, что у вас установлена следующая команда:", "Manage License and Devices": "Управление лицензией и устройствами", "Manually": "Вручн<PERSON>ю", "Markdown Rendering": "Отображение Markdown", "Max Message Count in Context": "Максимальное количество сообщений в контексте", "max tokens in context": "Максимальное количество токенов в контексте", "max tokens to generate": "Максимальное количество токенов для генерации", "Maybe Later": "Может позже", "MCP server added": "Сервер MCP добавлен", "MCP server for accessing arXiv papers": "MCP сервер для доступа к статьям arXiv", "MCP Settings": "Настройки MCP", "Medium": "Средний", "Mermaid Diagrams & Charts Rendering": "Рендеринг диаграмм и графиков Mermaid", "meticulous": "Тщательный", "Misleading information": "Ошибочная информация", "Mobile devices temporarily do not support local parsing of this file type. Please use text files (txt, markdown, etc.) or use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis.": "Мобильные устройства временно не поддерживают локальный анализ этого типа файла. Пожалуйста, используйте текстовые файлы (txt, markdown и т.д.) или используйте <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> для облачного анализа документов.", "model": "Модель", "Model": "Модель", "Model ID": "Идентификатор модели", "Model Provider": "Поставщик модели", "Modify the creativity of AI responses; the higher the value, the more random and intriguing the answers become, while a lower value ensures greater stability and reliability.": "Измените креативность ответов ИИ; чем выше значение, тем более случайными и интригующими становятся ответы, в то время как более низкое значение обеспечивает большую стабильность и надежность.", "More Images": "Больше изображений", "Move to Conversations": "Переместить в диалоги", "My Assistant": "Мой ассистент", "My Copilots": "Мои Copilots", "name": "Имя", "Name": "Название", "Natural": "более реалистичный", "Navigate to the Next Conversation": "Перейти к следующему разговору", "Navigate to the Next Option (in search dialog)": "Перейти к следующему варианту (в диалоге поиска)", "Navigate to the Previous Conversation": "Перейти к предыдущему разговору", "Navigate to the Previous Option (in search dialog)": "Перейти к предыдущему варианту (в диалоге поиска)", "Navigate to the Specific Conversation": "Перейти к конкретному разговору", "network error tips": "Произошла сетевая ошибка. Пожалуйста, проверьте текущий статус вашей сети и подключение к {{host}}.", "Network Proxy": "Сетевой прокси", "network proxy error tips": "Поскольку вы настроили адрес прокси {{proxy}}, пожалуйста, проверьте, работает ли прокси-сервер правильно, или рассмотрите возможность удаления адреса прокси из настроек.", "New": "Новый", "New Chat": "Новый чат", "New Images": "Новые изображения", "New Thread": "Новая Тема", "Nickname": "Псевдоним", "No": "Нет", "No eligible models available": "Подходящие модели отсутствуют", "No Limit": "Без ограничений", "No MCP servers parsed from clipboard": "MCP серверы не распознаны из буфера обмена", "No permission to write file": "Нет разрешения на запись файла", "No results found": "Не найдено результатов", "No search results found. Please use another <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton> or try again later.": "Результаты поиска не найдены. Пожалуйста, используйте другой <OpenExtensionSettingButton>поисковый сервис</OpenExtensionSettingButton> или попробуйте позже.", "None": "Нет", "not available in browser": "Эта функция недоступна в браузере. Скачайте наше рабочее приложение, чтобы получить все возможности.", "Not set": "Не задано", "Nothing found...": "Ничего не найдено...", "Number of Images per Reply": "Количество изображений в ответе", "One-click MCP servers for Chatbox AI subscribers": "Однокликовые MCP серверы для подписчиков Chatbox AI", "OpenAI API Compatible": "Совместимо с API OpenAI", "Operations": "Операции", "optional": "необязательный", "or": "или", "Or become a sponsor": "Или станьте спонсором", "Other concerns": "Другие проблемы", "Paste long text as a file": "Вставить длинный текст как файл", "Pasting long text will automatically insert it as a file, keeping chats clean and reducing token usage with prompt caching.": "Вставление длинного текста в виде файла поможет сохранить чистоту в чат-списке и уменьшить использование токенов с помощью кэширования промптов.", "PDF, DOC, PPT, XLS, TXT, Code...": "PDF, DOC, PPT, XLS, TXT, Код...", "Please describe the content you want to report (Optional)": "Пожалуйста, опишите содержимое, которое вы хотите сообщить (необязательно)", "Please ensure that the Remote LM Studio Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "Пожалуйста, убедите<PERSON><PERSON>, что удаленный сервис LM Studio может подключаться удаленно. Для получения дополнительной информации обратитесь к <a>этому руководству</a>.", "Please ensure that the Remote Ollama Service is able to connect remotely. For more details, refer to <a>this tutorial</a>.": "Пожалуйста, убедите<PERSON><PERSON>, что удаленный сервис Ollama может подключаться удаленно. Для получения дополнительной информации обратитесь к <a>этому руководству</a>.", "Please note that as a client tool, Chatbox cannot guarantee the quality of service and data privacy of the model providers. If you are looking for a stable, reliable, and privacy-protecting model service, consider <a>Chatbox AI</a>.": "Обратите внимание, что в качестве клиентского инструмента Chatbox не может гарантировать качество обслуживания и конфиденциальность данных поставщиков моделей. Если вы ищете стабильный, надежный и защищающий конфиденциальность сервис моделей, рассмотрите <a>Chatbox AI</a>.", "Please select a model": "Пожалуйста, выберите модель", "Please test before saving": "Пожалуйста, проверьте перед сохранением", "Please wait about 20 seconds": "Пожалуйста, подождите примерно 20 секунд", "pre-sale discount": "скидка на предзаказ", "premium": "премиум", "Premium Activation": "Активация премиум", "Premium License Activated": "Премиум лицензия активирована", "Premium License Key": "Ключ премиум лицензии", "Press hotkey": "Ввести горячую клавишу", "Preview": "Предпросмотр", "Privacy Policy": "Политика конфиденциальности", "Prompt": "Подсказка", "Provider not found": "Поставщик не найден", "proxy": "Прокси", "Proxy Address": "Адрес прокси", "Purchase": "Купить", "QR Code": "QR-код", "Quota Reset": "Сброс квоты", "quote": "Цитировать", "Rate Now": "Оценить сейчас", "Reading file...": "Чтение файла...", "Reasoning": "Логика", "RedNote": "Красная Заметка", "Refresh": "Обновить", "regenerate": "Перегенерировать", "Regulate the volume of historical messages sent to the AI, striking a harmonious balance between depth of comprehension and the efficiency of responses.": "Регулируйте объем исторических сообщений, отправляемых ИИ, достигая гармоничного баланса между глубиной понимания и эффективностью ответов.", "Remote (http/sse)": "Удаленный (http/sse)", "rename": "Переименовать", "Reply Again": "Ответить снова", "Reply Again Below": "Ответить снова ниже", "report": "Отправить жалобу", "Report Content": "Содержимое жалобы", "Report Content ID": "ID содержимого жалобы", "Report Type": "Тип жалобы", "reset": "Сброс", "Reset": "Сброс", "Reset All Hotkeys": "Сбросить все горячие клавиши", "Reset to Default": "Сбросить настройки по умолчанию", "Reset to Global Settings": "Сбросить к глобальным настройкам", "Result": "Результат", "Retrieve License": "Восстановить лицензию", "Retrieves up-to-date documentation and code examples for any library.": "Получает актуальную документацию и примеры кода для любой библиотеки.", "Roadmap": "План развития", "save": "Сохранить", "Save": "Сохранить", "Save & Resend": "Сохранить и отправить заново", "Scope": "Область", "Search": "Поиск", "Search All Conversations": "Поиск во всех разговорах", "Search in Current Conversation": "Поиск в текущем разговоре", "Search models": "Поиск моделей", "Search Provider": "Поисковый сервис", "Search query": "Поисковый запрос", "Search Term Construction Model": "Модель построения поисковых запросов", "Search...": "Поиск...", "Select and configure an AI model provider": "Выберите и настройте поставщика модели искусственного интеллекта", "Select File": "Выбрать файл", "Select Model": "Выбрать модель", "Select the Current Option (in search dialog)": "Выбрать текущий вариант (в диалоге поиска)", "send": "Отправить", "Send": "Отправить", "Send Without Generating Response": "Отправить без генерации ответа", "Setting the avatar for Copilot": "Установка аватара для Copilot", "settings": "hастройки", "Settings": "Настройки", "Sexual content": "Сексуальное содержимое", "Share File": "Поделиться файлом", "Share with Chatbox": "Поделиться с Chatbox", "Show all ({{x}})": "Показать все ({{x}})", "show first token latency": "Показать задержку первого токена", "Show in Thread List": "Показать в списке тем", "show message timestamp": "Показать временную метку сообщения", "show message token count": "Показать количество токенов в сообщении", "show message token usage": "Показать использование токенов в сообщении", "show message word count": "Показать количество слов в сообщении", "show model name": "Показать название модели", "Show/Hide the Application Window": "Показать/скрыть окно приложения", "Show/Hide the Search Dialog": "Показать/скрыть диалог поиска", "Smartest AI-Powered Services for Rapid Access": "Самые умные сервисы на основе искусственного интеллекта для быстрого доступа", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model or use the recommended <OpenMorePlanButton>Chatbox AI Models</OpenMorePlanButton>.": "Извините, текущая модель {{model}} API не поддерживает понимание изображений. Если вам нужно отправить изображения, пожалуйста, переключитесь на другую модель или используйте рекомендуемые <OpenMorePlanButton>модели Chatbox AI</OpenMorePlanButton>.", "Sorry, the current model {{model}} API itself does not support image understanding. If you need to send images, please switch to another model.": "Извините, текущая модель {{model}} API не поддерживает понимание изображений. Если вам нужно отправить изображения, пожалуйста, переключитесь на другую модель.", "Spam or advertising": "Спам или реклама", "Special thanks to the following sponsors:": "Отдельная благодарность следующим спонсорам:", "Specific model settings": "Специфические настройки модели", "Specific model settings configured for this conversation": "Специфические настройки модели, настроенные для этого разговора", "Spell Check": "Проверка орфографии", "star": "Добавить в избранное", "Start a New Thread": "Начать новую тему", "Start Setup": "Начать настройку", "Startup Page": "Страница запуска", "stop generating": "Остановить генерацию", "submit": "Отправить", "Support for ChatBox development": "Поддержка развития ChatBox", "Support jpg or png file smaller than 5MB": "Поддержка файлов jpg или png размером менее 5 МБ", "Supports a variety of advanced AI models": "Поддерживает различные передовые модели искусственного интеллекта", "Survey": "Опрос", "Switch": "Переключить", "system": "Система", "Tavily API Key": "Ключ <PERSON> Tavily", "temperature": "Температура", "Temperature": "Температура", "Test": "Тест", "Thank you for your report": "Спасибо за ваш отчёт", "The {{model}} API does not support files. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "API {{model}} не поддерживает файлы. Пожалуйста, скачайте <LinkToHomePage>настольную версию приложения</LinkToHomePage> для локальной обработки.", "The {{model}} API does not support files. Please use <LinkToAdvancedFileProcessing>Chatbox AI models</LinkToAdvancedFileProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "API {{model}} не поддерживает файлы. Пожалуйста, используйте <LinkToAdvancedFileProcessing>модели Chatbox AI</LinkToAdvancedFileProcessing> вместо этого, или скачайте <LinkToHomePage>настольную версию приложения</LinkToHomePage> для локальной обработки.", "The {{model}} API does not support links. Please download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "API {{model}} не поддерживает ссылки. Пожалуйста, скачайте <LinkToHomePage>настольную версию приложения</LinkToHomePage> для локальной обработки.", "The {{model}} API does not support links. Please use <LinkToAdvancedUrlProcessing>Chatbox AI models</LinkToAdvancedUrlProcessing> instead, or download <LinkToHomePage>the desktop app</LinkToHomePage> for local processing.": "API {{model}} не поддерживает ссылки. Пожалуйста, используйте <LinkToAdvancedUrlProcessing>модели Chatbox AI</LinkToAdvancedUrlProcessing> вместо этого, или скачайте <LinkToHomePage>настольную версию приложения</LinkToHomePage> для локальной обработки.", "The {{model}} API doesn't support document understanding. You can download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "Текущая модель {{model}} API не поддерживает понимание документов. Вы можете скачать <LinkToHomePage>настольную версию приложения Chatbox</LinkToHomePage> для локального анализа документов.", "The {{model}} API doesn't support document understanding. You can use <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> for cloud-based document analysis, or download <LinkToHomePage>Chatbox Desktop App</LinkToHomePage> for local document analysis.": "Текущая модель {{model}} API не поддерживает понимание документов. Вы можете использовать <LinkToAdvancedFileProcessing>Службу Chatbox AI</LinkToAdvancedFileProcessing> для анализа документов в облаке или <LinkToHomePage>настольную версию приложения Chatbox</LinkToHomePage> для локального анализа документов.", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code).": "API {{model}} сама по себе не поддерживает отправку файлов. Из-за сложности локального анализа файлов Chatbox обрабатывает только текстовые файлы (включая код).", "The {{model}} API itself does not support sending files. Due to the complexity of file parsing locally, Chatbox only processes text-based files (including code). For additional file formats and enhanced document understanding capabilities, <LinkToAdvancedFileProcessing>Chatbox AI Service</LinkToAdvancedFileProcessing> is recommended.": "API {{model}} сама по себе не поддерживает отправку файлов. Из-за сложности локального анализа файлов Chatbox обрабатывает только текстовые файлы (включая код). Для поддержки дополнительных форматов файлов и улучшенных возможностей понимания документов рекомендуется использовать <LinkToAdvancedFileProcessing>Службу Chatbox AI</LinkToAdvancedFileProcessing>.", "The {{model}} API itself does not support web browsing. Supported models: {{supported_web_browsing_models}}": "Текущая модель {{model}} API не поддерживает веб-браузер. Поддерживаемые модели: {{supported_web_browsing_models}}", "The {{model}} API itself does not support web browsing. Supported models: <OpenMorePlanButton>Chatbox AI models</OpenMorePlanButton>, {{supported_web_browsing_models}}": "Текущая модель {{model}} API не поддерживает веб-браузер. Поддерживаемые модели: <OpenMorePlanButton>модели Chatbox AI</OpenMorePlanButton>, {{supported_web_browsing_models}}", "The cache data for the file was not found. Please create a new conversation or refresh the context, and then send the file again.": "Данные кэша для файла не найдены. Пожалуйста, создайте новый разговор или обновите контекст, а затем отправьте файл снова.", "The current model {{model}} does not support sending links.": "Текущая модель {{model}} не поддерживает отправку ссылок.", "The current model {{model}} does not support sending links. Currently supported models: Chatbox AI models.": "Текущая модель {{model}} не поддерживает отправку ссылок. Текущие поддерживаемые модели: Chatbox AI.", "The file size exceeds the limit of 50MB. Please reduce the file size and try again.": "Размер файла превышает лимит 50 МБ. Пожалуйста, уменьшите размер файла и попробуйте снова.", "The file you sent has expired. To protect your privacy, all file-related cache data has been cleared. You need to create a new conversation or refresh the context, and then send the file again.": "Отправленный вами файл истек. Для защиты вашей конфиденциальности все данные кэша, связанные с файлами, были очищены. Вам нужно создать новый разговор или обновить контекст, а затем отправить файл снова.", "The Image Creator plugin has been activated for the current conversation": "Плагин Image Creator был активирован для текущего разговора", "The license key you entered is invalid. Please check your license key and try again.": "Введенный вами ключ лицензии недействителен. Пожалуйста, проверьте ваш ключ лицензии и попробуйте снова.", "The topP parameter controls the diversity of AI responses: lower values make the output more focused and predictable, while higher values allow for more varied and creative replies.": "Параметр topP контролирует разнообразие ответов AI: более низкие значения делают вывод более сфокусированным и предсказуемым, тогда как более высокие значения позволяют получать более разнообразные и творческие ответы.", "Theme": "Тема", "Thinking": "Ду<PERSON><PERSON><PERSON>", "Thinking Budget": "Бюджет мышления", "Thinking Budget only works for 2.0 or later models": "Бюджет мышления работает только с моделями 2.0 или более поздних версий", "Thinking Budget only works for 3.7 or later models": "Бюджет мышления работает только с моделями 3.7 или новее", "Thinking Effort": "Мыслительное усилие", "Thinking Effort only works for OpenAI o-series models": "Усилие мышления работает только для моделей OpenAI o-серии", "This license key has reached the activation limit, <a>click here</a> to manage license and devices to deactivate old devices.": "Этот ключ лицензии достиг лимита активации, <a>нажмите здесь</a>, чтобы управлять лицензией и устройствами для деактивации старых устройств.", "This server enables LLMs to retrieve and process content from web pages, converting HTML to markdown for easier consumption.": "Этот сервер позволяет LLM получать и обрабатывать контент с веб-страниц, преобразуя HTML в markdown для более легкого потребления.", "Thread History": "История Тем", "To access locally deployed model services, please install the Chatbox desktop version": "Для доступа к локально развернутым модельным сервисам, пожалуйста, установите настольную версию Chatbox", "Toggle": "Переключение", "token": "Токен", "Tool use": "Использование инструмента", "Tool Use": "Использование инструмента", "Tools": "Инструменты", "Top P": "Верхний P", "Type": "Тип", "Type a command or search": "Введи команду или поиск", "Type your question here...": "Введите ваш вопрос здесь...", "unknown error tips": "Неизвестная ошибка. Пожалуйста, проверьте настройки ИИ и статус вашей учетной записи, или <0>нажмите здесь, чтобы просмотреть документ FAQ</0>.", "Unlock Copilot Avatar by Upgrading to Premium Edition": "Разблокируйте ава<PERSON><PERSON><PERSON>, обновившись до премиум версии", "Unsaved settings": "Не сохраненные настройки", "unstar": "Удалить из избранного", "Untitled": "Без названия", "Update Available": "Доступно обновление", "Upload Image": "Загрузить изображение", "Upon import, changes will take effect immediately and existing data will be overwritten": "После импорта изменения вступят в силу немедленно, а существующие данные будут перезаписаны", "Use My Own API Key / Local Model": "Использовать свой API Key / Локальная модель", "Use proxy to resolve CORS and other network issues": "Использовать прокси для решения проблем CORS и других сетевых проблем", "user": "Пользователь", "User Avatar": "Аватар пользователя", "User Terms": "Условия использования", "version": "Версия", "View All Copilots": "Просмотреть всех Copilots", "View historical threads": "Просмотреть историю тем", "View More Plans": "Просмотреть больше планов", "Violence or dangerous content": "Опасное или опасное содержимое", "Vision": "Видение", "Vision capability is not enabled for Model {{model}}. Please enable it in <OpenSettingButton>provider settings</OpenSettingButton> or switch to a model that supports vision.": "Функция зрения не включена для модели {{model}}. Пожалуйста, включите ее в <OpenSettingButton>настройках провайдера</OpenSettingButton> или переключитесь на модель, которая поддерживает зрение.", "Vision, Drawing, File Understanding and more": "Видение, рисование, понимание файлов и многое другое", "Vivid": "более художественный", "Web Browsing": "Веб-браузер", "Web browsing (coming soon)": "Просмотр веб-страниц (скоро)", "Web Browsing...": "Веб-браузер...", "Web Search": "Поиск в Интернете", "WeChat": "WeChat", "What can I help you with today?": "Чем я могу помочь вам сегодня?", "Yes": "Да", "You are already a Premium user": "Вы уже являетесь премиум-пользователем", "You have exceeded the rate limit for the Chatbox AI service. Please try again later.": "Вы превысили лимит скорости для сервиса Chatbox AI. Пожалуйста, попробуйте снова позже.", "You have no more Chatbox AI quota left this month.": "У вас закончилась квота Chatbox AI на этот месяц.", "You have reached your monthly quota for the {{model}} model. Please <OpenSettingButton>go to Settings</OpenSettingButton> to switch to a different model, view your quota usage, or upgrade your plan.": "Вы достигли своей месячной квоты для модели {{model}}. Пожалуйста, <OpenSettingButton>перейдите в Настройки</OpenSettingButton>, чтобы переключиться на другую модель, просмотреть использование квоты или обновить свой план.", "You have selected Chatbox AI as the model provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different model provider.": "Вы выбрали Chatbox AI в качестве поставщика модели, но ключ лицензии еще не введен. Пожалуйста, <OpenSettingButton>нажмите здесь, чтобы открыть Настройки</OpenSettingButton> и введите свой ключ лицензии, или выберите другого поставщика модели.", "You have selected Chatbox AI as the search provider, but a license key has not been entered yet. Please <OpenSettingButton>click here to open Settings</OpenSettingButton> and enter your license key, or choose a different <OpenExtensionSettingButton>search provider</OpenExtensionSettingButton>.": "Вы выбрали Chatbox AI в качестве поискового сервиса, но ключ лицензии еще не введен. Пожалуйста, <OpenSettingButton>нажмите здесь, чтобы открыть Настройки</OpenSettingButton> и введите свой ключ лицензии, или выберите другой <OpenExtensionSettingButton>поисковый сервис</OpenExtensionSettingButton>.", "You have selected Tavily as the search provider, but an API key has not been entered yet. Please <OpenExtensionSettingButton>click here to open Settings</OpenExtensionSettingButton> and enter your API key, or choose a different search provider.": "Вы выбра<PERSON><PERSON> в качестве поискового сервиса, но ключ API еще не введен. Пожалуйста, <OpenExtensionSettingButton>нажмите здесь, чтобы открыть Настройки</OpenExtensionSettingButton> и введите свой ключ API, или выберите другой поисковый сервис.", "You have unsaved settings. Are you sure you want to leave?": "У вас есть несохраненные настройки. Вы уверены, что хотите уйти?", "Your ChatboxAI subscription already includes access to models from various providers. There's no need to switch providers - you can select different models directly within ChatboxAI. Switching from ChatboxAI to other providers will require their respective API keys. <button>Back to ChatboxAI</button>": "Ваша подписка ChatboxAI уже включает доступ к моделям от различных поставщиков. Нет необходимости переключаться на другие поставщики - вы можете выбрать разные модели непосредственно в ChatboxAI. Переключение с ChatboxAI на другие поставщики потребует их соответствующих API-ключей. <button>Вернуться в ChatboxAI</button>", "Your current License (Chatbox AI Lite) does not support the {{model}} model. To use this model, please <OpenMorePlanButton>upgrade</OpenMorePlanButton> to Chatbox AI Pro or a higher-tier package. Alternatively, you can switch to a different model by <OpenSettingButton>accessing the settings</OpenSettingButton>.": "Ваша текущая лицензия (Chatbox AI Lite) не поддерживает модель {{model}}. Чтобы использовать эту модель, пожалуйста, <OpenMorePlanButton>обновитесь</OpenMorePlanButton> до Chatbox AI Pro или пакета более высокого уровня. В качестве альтернативы, вы можете переключиться на другую модель, <OpenSettingButton>перейдя в настройки</OpenSettingButton>.", "Your license has expired. Please check your subscription or purchase a new one.": "Ваша лицензия истекла. Пожалуйста, проверьте вашу подписку или приобретите новую.", "Your rating on the App Store would help make Chatbox even better!": "Ваш рейтинг в App Store поможет сделать Chatbox еще лучше!"}