import { useState, useCallback, useEffect } from 'react'

export interface Category {
  id: string
  name: string
  isDefault: boolean
  createdAt: string
}

export function useCategories() {
  const [categories, setCategories] = useState<Category[]>([])

  // Catégorie par défaut
  const DEFAULT_CATEGORY: Category = {
    id: 'general',
    name: 'Généra<PERSON>',
    isDefault: true,
    createdAt: new Date().toISOString()
  }

  // Charger les catégories depuis localStorage
  const loadCategories = useCallback(() => {
    try {
      const stored = localStorage.getItem('knowledgeBaseCategories')
      const storedCategories = stored ? JSON.parse(stored) : []
      
      // S'assurer que la catégorie "Général" existe toujours
      const hasDefault = storedCategories.some((cat: Category) => cat.id === 'general')
      if (!hasDefault) {
        storedCategories.unshift(DEFAULT_CATEGORY)
      }
      
      setCategories(storedCategories)
      return storedCategories
    } catch (error) {
      console.error('Erreur lors du chargement des catégories:', error)
      setCategories([DEFAULT_CATEGORY])
      return [DEFAULT_CATEGORY]
    }
  }, [])

  // Sauvegarder les catégories dans localStorage
  const saveCategories = useCallback((cats: Category[]) => {
    try {
      localStorage.setItem('knowledgeBaseCategories', JSON.stringify(cats))
      // Déclencher un événement pour notifier les autres composants
      window.dispatchEvent(new CustomEvent('categoriesChanged'))
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des catégories:', error)
    }
  }, [])

  // Créer une nouvelle catégorie
  const createCategory = useCallback((name: string): Category => {
    const newCategory: Category = {
      id: `cat_${Date.now()}`,
      name: name.trim(),
      isDefault: false,
      createdAt: new Date().toISOString()
    }

    const updatedCategories = [...categories, newCategory]
    setCategories(updatedCategories)
    saveCategories(updatedCategories)
    
    return newCategory
  }, [categories, saveCategories])

  // Obtenir ou créer une catégorie par nom
  const getOrCreateCategory = useCallback((name: string): Category => {
    const trimmedName = name.trim()
    
    // Chercher une catégorie existante
    const existing = categories.find(cat => 
      cat.name.toLowerCase() === trimmedName.toLowerCase()
    )
    
    if (existing) {
      return existing
    }
    
    // Créer une nouvelle catégorie si elle n'existe pas
    return createCategory(trimmedName)
  }, [categories, createCategory])

  // Mettre à jour une catégorie
  const updateCategory = useCallback((categoryId: string, newName: string): boolean => {
    if (categoryId === 'general') {
      console.warn('Impossible de modifier la catégorie "Général"')
      return false
    }

    const trimmedName = newName.trim()
    if (!trimmedName) return false

    // Vérifier si le nom existe déjà
    const nameExists = categories.some(cat =>
      cat.id !== categoryId &&
      cat.name.toLowerCase() === trimmedName.toLowerCase()
    )

    if (nameExists) {
      console.warn('Une catégorie avec ce nom existe déjà')
      return false
    }

    const updatedCategories = categories.map(cat =>
      cat.id === categoryId
        ? { ...cat, name: trimmedName }
        : cat
    )

    setCategories(updatedCategories)
    saveCategories(updatedCategories)

    return true
  }, [categories, saveCategories])

  // Supprimer une catégorie (sauf "Général")
  const deleteCategory = useCallback((categoryId: string) => {
    if (categoryId === 'general') {
      console.warn('Impossible de supprimer la catégorie "Général"')
      return false
    }

    const updatedCategories = categories.filter(cat => cat.id !== categoryId)
    setCategories(updatedCategories)
    saveCategories(updatedCategories)

    return true
  }, [categories, saveCategories])

  // Obtenir toutes les catégories
  const getAllCategories = useCallback(() => {
    return categories
  }, [categories])

  // Obtenir une catégorie par ID
  const getCategoryById = useCallback((id: string): Category | undefined => {
    return categories.find(cat => cat.id === id)
  }, [categories])

  // Initialiser les catégories de démonstration
  const initializeDemoCategories = useCallback(() => {
    const demoCategories = [
      DEFAULT_CATEGORY,
      {
        id: 'support',
        name: 'Support Client',
        isDefault: false,
        createdAt: new Date().toISOString()
      },
      {
        id: 'juridique',
        name: 'Juridique',
        isDefault: false,
        createdAt: new Date().toISOString()
      },
      {
        id: 'marketing',
        name: 'Marketing',
        isDefault: false,
        createdAt: new Date().toISOString()
      }
    ]

    const stored = localStorage.getItem('knowledgeBaseCategories')
    if (!stored) {
      setCategories(demoCategories)
      saveCategories(demoCategories)
    }
  }, [saveCategories])

  // Charger les catégories au démarrage
  useEffect(() => {
    loadCategories()
    initializeDemoCategories()
  }, [loadCategories, initializeDemoCategories])

  return {
    categories,
    createCategory,
    updateCategory,
    getOrCreateCategory,
    deleteCategory,
    getAllCategories,
    getCategoryById,
    loadCategories,
    DEFAULT_CATEGORY
  }
}
