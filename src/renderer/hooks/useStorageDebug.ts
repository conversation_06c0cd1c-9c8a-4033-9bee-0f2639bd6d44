import { useEffect, useRef } from 'react'
import { useAtomValue } from 'jotai'
import { authAtom } from '@/stores/atoms/authAtoms'
import { newAuthAtom } from '@/stores/atoms/newAuthAtoms'
import { sessionsListAtom, currentSessionIdAtom } from '@/stores/atoms/sessionAtoms'
import { settingsAtom } from '@/stores/atoms/settingsAtoms'

interface StorageConflict {
  type: 'duplicate_keys' | 'version_mismatch' | 'timing_issue' | 'migration_incomplete'
  description: string
  severity: 'low' | 'medium' | 'high'
  data?: any
}

export const useStorageDebug = () => {
  const authState = useAtomValue(authAtom)
  const newAuthState = useAtomValue(newAuthAtom)
  const sessionsList = useAtomValue(sessionsListAtom)
  const currentSessionId = useAtomValue(currentSessionIdAtom)
  const settings = useAtomValue(settingsAtom)
  
  const initTimeRef = useRef<number>(Date.now())
  const conflictsRef = useRef<StorageConflict[]>([])

  // Détecter les conflits entre systèmes d'authentification
  useEffect(() => {
    const conflicts: StorageConflict[] = []

    // 1. Vérifier les clés dupliquées dans localStorage
    const authKeys = ['auth', 'new-auth', 'splash_completed', 'datatec_session']
    const existingKeys = authKeys.filter(key => localStorage.getItem(key) !== null)
    
    if (existingKeys.length > 2) {
      conflicts.push({
        type: 'duplicate_keys',
        description: `Plusieurs systèmes d'auth détectés: ${existingKeys.join(', ')}`,
        severity: 'high',
        data: { keys: existingKeys }
      })
    }

    // 2. Vérifier la cohérence entre les deux systèmes d'auth
    if (authState.isAuthenticated !== newAuthState.isAuthenticated) {
      conflicts.push({
        type: 'version_mismatch',
        description: 'Incohérence entre authAtom et newAuthAtom',
        severity: 'high',
        data: { 
          authAtom: authState.isAuthenticated, 
          newAuthAtom: newAuthState.isAuthenticated 
        }
      })
    }

    // 3. Vérifier les problèmes de timing
    const timeSinceInit = Date.now() - initTimeRef.current
    if (timeSinceInit < 1000 && (!sessionsList.length && !currentSessionId)) {
      conflicts.push({
        type: 'timing_issue',
        description: 'Données non chargées - possible problème de timing',
        severity: 'medium',
        data: { timeSinceInit, sessionsCount: sessionsList.length }
      })
    }

    // 4. Vérifier la migration incomplète
    const hasOldData = localStorage.getItem('auth') !== null
    const hasNewData = localStorage.getItem('new-auth') !== null
    if (hasOldData && hasNewData) {
      conflicts.push({
        type: 'migration_incomplete',
        description: 'Migration incomplète - ancien et nouveau système coexistent',
        severity: 'medium',
        data: { hasOldData, hasNewData }
      })
    }

    // Mettre à jour les conflits
    conflictsRef.current = conflicts

    // Logger les conflits
    if (conflicts.length > 0) {
      console.warn('⚠️ [STORAGE DEBUG] Conflits détectés:', conflicts)
    }

  }, [authState, newAuthState, sessionsList, currentSessionId])

  // Logger l'ordre d'initialisation
  useEffect(() => {
    const logInitOrder = () => {
      console.log('🔄 [INIT ORDER] État des atoms:', {
        timestamp: new Date().toISOString(),
        timeSinceInit: Date.now() - initTimeRef.current,
        authAtom: {
          isAuthenticated: authState.isAuthenticated,
          hasUser: !!authState.user,
          hasToken: !!authState.token
        },
        newAuthAtom: {
          isAuthenticated: newAuthState.isAuthenticated,
          initialized: newAuthState.initialized,
          isLoading: newAuthState.isLoading
        },
        sessions: {
          count: sessionsList.length,
          currentId: currentSessionId
        },
        settings: {
          theme: settings?.theme,
          hasApiHost: !!settings?.apiHost
        }
      })
    }

    // Logger immédiatement
    logInitOrder()

    // Logger après un délai pour voir l'évolution
    const timer = setTimeout(logInitOrder, 2000)
    return () => clearTimeout(timer)
  }, [authState, newAuthState, sessionsList, currentSessionId, settings])

  // Fonction pour forcer la synchronisation
  const forceSyncStorage = async () => {
    console.log('🔧 [STORAGE DEBUG] Force sync storage...')
    
    try {
      // Vider le cache des atoms
      if (typeof window !== 'undefined') {
        // Forcer le rechargement des données depuis le storage
        window.location.reload()
      }
    } catch (error) {
      console.error('❌ [STORAGE DEBUG] Erreur lors de la sync:', error)
    }
  }

  // Fonction pour nettoyer les conflits
  const cleanupConflicts = () => {
    console.log('🧹 [STORAGE DEBUG] Nettoyage des conflits...')
    
    const conflicts = conflictsRef.current
    
    conflicts.forEach(conflict => {
      switch (conflict.type) {
        case 'duplicate_keys':
          // Garder seulement le nouveau système
          if (localStorage.getItem('new-auth')) {
            localStorage.removeItem('auth')
            console.log('🧹 Suppression de l\'ancien système auth')
          }
          break
          
        case 'migration_incomplete':
          // Marquer la migration comme terminée
          localStorage.setItem('migration_completed', 'true')
          console.log('🧹 Migration marquée comme terminée')
          break
      }
    })
  }

  // Fonction pour diagnostiquer les problèmes
  const diagnoseIssues = () => {
    const diagnosis = {
      conflicts: conflictsRef.current,
      localStorage: {} as Record<string, any>,
      timing: {
        initTime: initTimeRef.current,
        currentTime: Date.now(),
        elapsed: Date.now() - initTimeRef.current
      },
      recommendations: [] as string[]
    }

    // Collecter les données localStorage pertinentes
    const relevantKeys = ['auth', 'new-auth', 'splash_completed', 'datatec_session', '_currentSessionIdCachedAtom']
    relevantKeys.forEach(key => {
      const value = localStorage.getItem(key)
      if (value) {
        try {
          diagnosis.localStorage[key] = JSON.parse(value)
        } catch {
          diagnosis.localStorage[key] = value
        }
      }
    })

    // Générer des recommandations
    if (conflictsRef.current.some(c => c.type === 'duplicate_keys')) {
      diagnosis.recommendations.push('Nettoyer les clés dupliquées avec cleanupConflicts()')
    }
    
    if (conflictsRef.current.some(c => c.type === 'timing_issue')) {
      diagnosis.recommendations.push('Ajouter des délais d\'initialisation ou des fallbacks')
    }
    
    if (conflictsRef.current.some(c => c.type === 'version_mismatch')) {
      diagnosis.recommendations.push('Synchroniser les deux systèmes d\'authentification')
    }

    console.log('🔍 [STORAGE DEBUG] Diagnostic complet:', diagnosis)
    return diagnosis
  }

  return {
    conflicts: conflictsRef.current,
    forceSyncStorage,
    cleanupConflicts,
    diagnoseIssues
  }
}
