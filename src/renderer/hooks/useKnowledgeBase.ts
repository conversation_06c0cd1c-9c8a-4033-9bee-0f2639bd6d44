import { useState, useCallback } from 'react'

export interface SerializableFile {
  name: string
  size: number
  type: string
  lastModified: number
  content: string
}

export interface KnowledgeBaseData {
  id?: string
  name: string
  description: string
  personalityTags: string[]
  additionalInfo: string
  files: SerializableFile[]
  isActive: boolean
  category: string // Nouvelle propriété pour la catégorie
  createdAt?: string
  updatedAt?: string
}

export interface KnowledgeBaseApiResponse {
  success: boolean
  data?: KnowledgeBaseData
  error?: string
}

export function useKnowledgeBase() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBaseData[]>([])

  // Simuler un stockage local pour les bases de connaissances
  const saveToLocalStorage = (kbs: KnowledgeBaseData[]) => {
    localStorage.setItem('knowledgeBases', JSON.stringify(kbs))
    // Déclencher un événement pour notifier les autres composants
    window.dispatchEvent(new CustomEvent('knowledgeBasesChanged'))
  }

  const loadFromLocalStorage = (): KnowledgeBaseData[] => {
    try {
      const stored = localStorage.getItem('knowledgeBases')
      const data = stored ? JSON.parse(stored) : []

      // S'assurer que toutes les bases de connaissances ont une catégorie
      return data.map((kb: KnowledgeBaseData) => ({
        ...kb,
        category: kb.category || 'general' // Catégorie par défaut si manquante
      }))
    } catch {
      return []
    }
  }

  // Charger les données au démarrage
  useState(() => {
    const stored = loadFromLocalStorage()
    setKnowledgeBases(stored)
  })

  const createKnowledgeBase = useCallback(async (data: KnowledgeBaseData): Promise<KnowledgeBaseApiResponse> => {
    setLoading(true)
    setError(null)

    try {
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Créer la nouvelle base de connaissances
      const newKB: KnowledgeBaseData = {
        ...data,
        id: `kb_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // Mettre à jour la liste locale
      const updatedKBs = [...knowledgeBases, newKB]
      setKnowledgeBases(updatedKBs)
      saveToLocalStorage(updatedKBs)

      return {
        success: true,
        data: newKB
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue'
      setError(errorMessage)
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      setLoading(false)
    }
  }, [knowledgeBases])

  const updateKnowledgeBase = useCallback(async (id: string, data: Partial<KnowledgeBaseData>): Promise<KnowledgeBaseApiResponse> => {
    setLoading(true)
    setError(null)

    try {
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Mettre à jour la base de connaissances
      const updatedKBs = knowledgeBases.map(kb =>
        kb.id === id
          ? { ...kb, ...data, updatedAt: new Date().toISOString() }
          : kb
      )

      setKnowledgeBases(updatedKBs)
      saveToLocalStorage(updatedKBs)

      const updatedKB = updatedKBs.find(kb => kb.id === id)

      return {
        success: true,
        data: updatedKB!
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue'
      setError(errorMessage)
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      setLoading(false)
    }
  }, [knowledgeBases])

  const deleteKnowledgeBase = useCallback(async (id: string): Promise<KnowledgeBaseApiResponse> => {
    setLoading(true)
    setError(null)

    try {
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Supprimer la base de connaissances
      const updatedKBs = knowledgeBases.filter(kb => kb.id !== id)
      setKnowledgeBases(updatedKBs)
      saveToLocalStorage(updatedKBs)

      return {
        success: true
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue'
      setError(errorMessage)
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      setLoading(false)
    }
  }, [knowledgeBases])

  const refreshKnowledgeBases = useCallback(async (): Promise<KnowledgeBaseData[]> => {
    setLoading(true)
    setError(null)

    try {
      // Simuler un délai d'API
      await new Promise(resolve => setTimeout(resolve, 500))

      // Recharger depuis le localStorage
      const stored = loadFromLocalStorage()
      setKnowledgeBases(stored)
      return stored
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue'
      setError(errorMessage)
      return []
    } finally {
      setLoading(false)
    }
  }, [])

  const testKnowledgeBase = useCallback(async (data: KnowledgeBaseData): Promise<KnowledgeBaseApiResponse> => {
    setLoading(true)
    setError(null)

    try {
      // TODO: Implémenter l'appel API pour tester la configuration
      await new Promise(resolve => setTimeout(resolve, 3000))

      return {
        success: true,
        data: {
          ...data,
          // Résultats du test
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue'
      setError(errorMessage)
      return {
        success: false,
        error: errorMessage
      }
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    loading,
    error,
    knowledgeBases,
    createKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase,
    refreshKnowledgeBases,
    testKnowledgeBase
  }
}
