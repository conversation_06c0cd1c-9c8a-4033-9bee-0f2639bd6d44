// Hook personnalisé pour utiliser le service de rôles dans les composants React

import { useState, useEffect } from 'react'
import { roleService, Role } from '../services/RoleService'

export const useRoles = () => {
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // S'abonner aux changements de rôles
    const unsubscribe = roleService.subscribe((updatedRoles) => {
      setRoles(updatedRoles)
      setLoading(false)
    })

    // Nettoyer l'abonnement au démontage
    return unsubscribe
  }, [])

  return {
    roles,
    loading,
    // Méthodes utilitaires
    getRolesForSelect: () => roleService.getRolesForSelect(),
    addRole: (name: string, displayName: string) => roleService.addRole(name, displayName),
    updateRole: (roleId: string, displayName: string) => roleService.updateRole(roleId, displayName),
    deleteRole: (roleId: string) => roleService.deleteRole(roleId),
    reload: () => roleService.reload()
  }
}
