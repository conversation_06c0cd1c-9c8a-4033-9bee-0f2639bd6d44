/**
 * Hook personnalisé pour contrôler l'affichage du splash screen
 * Détermine immédiatement si le splash doit être affiché ou non
 */

import { useEffect, useState } from 'react'
import { useAtom } from 'jotai'
import { splashCompletedAtom } from '@/stores/atoms'
import { SplashService } from '@/services/SplashService'

interface UseSplashControlOptions {
  isAuthenticated: boolean
  authInitialized: boolean
}

export function useSplashControl({ isAuthenticated, authInitialized }: UseSplashControlOptions) {
  const [splashCompleted, setSplashCompleted] = useAtom(splashCompletedAtom)
  const [initialCheckDone, setInitialCheckDone] = useState(false)

  // Vérification immédiate au montage du composant
  useEffect(() => {
    if (initialCheckDone) return

    // Vérifications multiples pour détecter un rafraîchissement
    const isPageRefresh = SplashService.isPageRefresh()
    const hasActiveSession = SplashService.hasActiveSession()

    if ((isPageRefresh || hasActiveSession) && isAuthenticated) {
      // C'est un rafraîchissement avec utilisateur authentifié - pas de splash
      setSplashCompleted(true)
      console.log('🔄 Rafraîchissement détecté (Performance API ou session active) - splash désactivé')
    } else if (!isAuthenticated) {
      // Pas authentifié - pas de splash
      setSplashCompleted(true)
    }
    // Si authentifié mais pas de session active, laisser la logique de connexion gérer le splash

    setInitialCheckDone(true)
  }, [isAuthenticated, setSplashCompleted, initialCheckDone])

  // Gestion des changements d'authentification
  useEffect(() => {
    if (!authInitialized || !initialCheckDone) return

    // Ne pas interférer avec la logique de connexion
    // La logique de connexion gère déjà l'affichage du splash
  }, [authInitialized, initialCheckDone])

  return {
    splashCompleted,
    setSplashCompleted,
    shouldShowSplash: isAuthenticated && !splashCompleted && authInitialized
  }
}
