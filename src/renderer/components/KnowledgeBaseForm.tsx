import {
  Box,
  Button,
  Flex,
  Stack,
  Text,
  TextInput,
  Textarea,
  Title,
  Switch,
  Group,
  Pill,
  ActionIcon,
  Collapse,
  Divider,
  FileInput,
  Loader,
  Alert,
  Select,
  ComboboxItem
} from '@mantine/core'
import {
  IconX,
  IconPlus,
  IconChevronDown,
  IconChevronUp,
  IconUpload,
  IconFile,
  IconCheck,
  IconAlertCircle
} from '@tabler/icons-react'
import { useState } from 'react'
import { useKnowledgeBase, KnowledgeBaseData, SerializableFile } from '../hooks/useKnowledgeBase'
import { useCategories } from '../hooks/useCategories'
import { notifications } from '@mantine/notifications'

interface KnowledgeBaseFormProps {
  onClose: () => void
  onSave: (data: KnowledgeBaseData) => void
  editingKB?: KnowledgeBaseData | null
}

const PERSONALITY_TAGS = [
  'Sceptique',
  'Traditionnel', 
  'Visionnaire',
  'Poétique',
  'Conversationnel',
  'Plein d\'esprit',
  'Franc',
  'Motivant',
  'Génération Z'
]

export default function KnowledgeBaseForm({ onClose, onSave, editingKB }: KnowledgeBaseFormProps) {
  const { createKnowledgeBase, updateKnowledgeBase, testKnowledgeBase, loading, error } = useKnowledgeBase()
  const { categories, getOrCreateCategory, DEFAULT_CATEGORY } = useCategories()

  const [formData, setFormData] = useState<KnowledgeBaseData>({
    name: editingKB?.name || '',
    description: editingKB?.description || '',
    personalityTags: editingKB?.personalityTags || [],
    additionalInfo: editingKB?.additionalInfo || '',
    files: editingKB?.files || [],
    isActive: editingKB?.isActive ?? true,
    category: editingKB?.category || DEFAULT_CATEGORY.id
  })

  const [advancedOpen, setAdvancedOpen] = useState(false)
  const [isTestMode, setIsTestMode] = useState(false)
  const [categorySearchValue, setCategorySearchValue] = useState('')
  const isEditing = !!editingKB

  // Préparer les options de catégories pour le Select
  const categoryOptions = categories.map(cat => ({
    value: cat.id,
    label: cat.name
  }))

  // Gérer la sélection/création de catégorie
  const handleCategoryChange = (value: string | null) => {
    if (!value) return

    // Si la valeur correspond à une catégorie existante
    const existingCategory = categories.find(cat => cat.id === value || cat.name === value)

    if (existingCategory) {
      setFormData(prev => ({ ...prev, category: existingCategory.id }))
    } else {
      // Créer une nouvelle catégorie
      const newCategory = getOrCreateCategory(value)
      setFormData(prev => ({ ...prev, category: newCategory.id }))
    }
  }

  const handleTagToggle = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      personalityTags: prev.personalityTags.includes(tag)
        ? prev.personalityTags.filter(t => t !== tag)
        : [...prev.personalityTags, tag]
    }))
  }

  const handleFileUpload = async (files: File[]) => {
    // Convertir les fichiers en format sérialisable
    const serializableFiles = await Promise.all(
      files.map(async (file) => {
        try {
          console.log('🔍 DEBUG - handleFileUpload: Traitement du fichier:', file.name, 'Type:', file.type, 'Taille:', file.size)

          let content = ''

          // Gestion spécifique selon le type de fichier
          if (file.type === 'application/pdf') {
            // Pour les PDF, on ne peut pas les lire directement avec file.text()
            console.log('⚠️ ATTENTION - handleFileUpload: Fichier PDF détecté, contenu non extractible directement')
            content = `[FICHIER PDF: ${file.name} - Taille: ${(file.size / 1024).toFixed(1)} KB. Note: L'extraction du contenu PDF n'est pas encore supportée. Veuillez convertir en format texte (.txt) pour une meilleure compatibilité.]`
          } else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
            // Pour les fichiers Word
            console.log('⚠️ ATTENTION - handleFileUpload: Fichier Word détecté, contenu non extractible directement')
            content = `[FICHIER WORD: ${file.name} - Taille: ${(file.size / 1024).toFixed(1)} KB. Note: L'extraction du contenu Word n'est pas encore supportée. Veuillez convertir en format texte (.txt) pour une meilleure compatibilité.]`
          } else {
            // Pour les fichiers texte (txt, md, etc.)
            content = await file.text()
            console.log('✅ SUCCESS - handleFileUpload: Contenu extrait avec succès, longueur:', content.length)
          }

          // Vérifier que le contenu n'est pas vide
          if (!content.trim()) {
            console.log('❌ ERREUR - handleFileUpload: Contenu vide pour le fichier:', file.name)
            content = `[ERREUR: Le fichier ${file.name} semble être vide ou n'a pas pu être lu correctement.]`
          }

          return {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
            content: content
          }
        } catch (error) {
          console.error('❌ ERREUR - handleFileUpload: Erreur lors du traitement du fichier:', file.name, error)
          return {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
            content: `[ERREUR: Impossible de lire le fichier ${file.name}. Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}]`
          }
        }
      })
    )

    console.log('🔍 DEBUG - handleFileUpload: Fichiers traités:', serializableFiles.length)
    serializableFiles.forEach((file, index) => {
      console.log(`📄 Fichier ${index + 1}:`, {
        name: file.name,
        contentLength: file.content.length,
        contentPreview: file.content.substring(0, 100)
      })
    })

    setFormData(prev => ({
      ...prev,
      files: [...prev.files, ...serializableFiles]
    }))

    // Notification de succès
    notifications.show({
      title: 'Fichiers ajoutés',
      message: `${serializableFiles.length} fichier(s) ajouté(s) avec succès`,
      color: 'green',
      icon: <IconCheck size={16} />
    })
  }

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }))
  }

  const handleSave = async () => {
    if (!formData.name.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le nom de la base de connaissances est requis',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
      return
    }

    try {
      let result

      if (isEditing && editingKB?.id) {
        result = await updateKnowledgeBase(editingKB.id, formData)
      } else {
        result = await createKnowledgeBase(formData)
      }

      if (result.success) {
        notifications.show({
          title: 'Succès',
          message: isEditing
            ? 'Base de connaissances mise à jour avec succès'
            : 'Base de connaissances créée avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
        onSave(formData)
      } else {
        notifications.show({
          title: 'Erreur',
          message: result.error || `Erreur lors de ${isEditing ? 'la mise à jour' : 'la création'}`,
          color: 'red',
          icon: <IconAlertCircle size={16} />
        })
      }
    } catch (err) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur inattendue lors de la sauvegarde',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
    }
  }

  const handleTest = async () => {
    if (!formData.name.trim()) {
      notifications.show({
        title: 'Erreur',
        message: 'Le nom de la base de connaissances est requis pour le test',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
      return
    }

    setIsTestMode(true)

    try {
      const result = await testKnowledgeBase(formData)

      if (result.success) {
        notifications.show({
          title: 'Test réussi',
          message: 'La configuration fonctionne correctement',
          color: 'green',
          icon: <IconCheck size={16} />
        })
      } else {
        notifications.show({
          title: 'Test échoué',
          message: result.error || 'Erreur lors du test',
          color: 'orange',
          icon: <IconAlertCircle size={16} />
        })
      }
    } catch (err) {
      notifications.show({
        title: 'Erreur de test',
        message: 'Erreur inattendue lors du test',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
    } finally {
      setIsTestMode(false)
    }
  }

  return (
    <Box
      style={{
        backgroundColor: 'var(--mantine-color-dark-7)',
        borderRadius: '8px',
        border: '1px solid var(--mantine-color-dark-4)',
        minHeight: '500px',
        padding: '1.5rem',
        position: 'relative'
      }}
    >
      {/* Header */}
      <Flex justify="space-between" align="center" mb="lg">
        <Title order={4} c="var(--mantine-color-gray-0)" fw={500}>
          {isEditing ? 'Modifier la Base de connaissances' : 'Personnaliser la Base de connaissances'}
        </Title>
        <ActionIcon
          variant="subtle"
          color="gray"
          size="lg"
          onClick={onClose}
        >
          <IconX size={20} />
        </ActionIcon>
      </Flex>

      {/* Sous-titre */}
      <Text size="sm" c="var(--mantine-color-gray-4)" mb="xl">
        Présentez-vous pour obtenir des réponses plus personnalisées.
      </Text>

      {/* Affichage des erreurs */}
      {error && (
        <Alert
          icon={<IconAlertCircle size={16} />}
          title="Erreur"
          color="red"
          mb="lg"
        >
          {error}
        </Alert>
      )}

      <Stack gap="lg">
        {/* Nom de la base */}
        <TextInput
          label="Nom de la base de connaissances"
          placeholder="Ma base de connaissances personnelle"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          styles={{
            label: { color: 'var(--mantine-color-gray-0)', marginBottom: '8px' },
            input: {
              backgroundColor: 'var(--mantine-color-dark-6)',
              borderColor: 'var(--mantine-color-dark-4)',
              color: 'var(--mantine-color-gray-0)',
              '&::placeholder': { color: 'var(--mantine-color-gray-5)' }
            }
          }}
        />

        {/* Catégorie */}
        <Select
          label="Catégorie"
          placeholder="Sélectionnez ou créez une catégorie"
          value={formData.category}
          onChange={handleCategoryChange}
          data={categoryOptions}
          searchable
          creatable
          getCreateLabel={(query) => `+ Créer "${query}"`}
          onCreate={(query) => {
            const newCategory = getOrCreateCategory(query)
            const newOption = { value: newCategory.id, label: newCategory.name }
            return newOption
          }}
          styles={{
            label: { color: 'var(--mantine-color-gray-0)', marginBottom: '8px' },
            input: {
              backgroundColor: 'var(--mantine-color-dark-6)',
              borderColor: 'var(--mantine-color-dark-4)',
              color: 'var(--mantine-color-gray-0)',
              '&::placeholder': { color: 'var(--mantine-color-gray-5)' }
            },
            dropdown: {
              backgroundColor: 'var(--mantine-color-dark-6)',
              borderColor: 'var(--mantine-color-dark-4)',
            },
            option: {
              color: 'var(--mantine-color-gray-0)',
              '&[data-selected]': {
                backgroundColor: 'var(--mantine-color-blue-9)',
              },
              '&:hover': {
                backgroundColor: 'var(--mantine-color-dark-5)',
              }
            }
          }}
        />

        {/* Instructions */}
        <Textarea
          label="Instructions"
          placeholder="Décrivez comment l'IA doit utiliser cette base de connaissances..."
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          minRows={3}
          styles={{
            label: { color: 'var(--mantine-color-gray-0)', marginBottom: '8px' },
            input: {
              backgroundColor: 'var(--mantine-color-dark-6)',
              borderColor: 'var(--mantine-color-dark-4)',
              color: 'var(--mantine-color-gray-0)',
              '&::placeholder': { color: 'var(--mantine-color-gray-5)' }
            }
          }}
        />

        {/* Section Fichiers de base de connaissances */}
        <Box>
          <Text size="lg" c="var(--mantine-color-gray-0)" mb="md" fw={500}>
            Fichiers de base de connaissances
          </Text>

          {/* Input de fichier avec bouton personnalisé */}
          <Box mb="md">
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.txt,.md"
              onChange={async (e) => {
                const files = Array.from(e.target.files || [])
                await handleFileUpload(files)
              }}
              style={{ display: 'none' }}
              id="file-upload-input"
            />
            <Button
              component="label"
              htmlFor="file-upload-input"
              leftSection={<IconUpload size={16} />}
              variant="outline"
              color="blue"
              styles={{
                root: {
                  borderColor: 'var(--mantine-color-blue-6)',
                  color: 'var(--mantine-color-blue-4)',
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: 'var(--mantine-color-blue-9)',
                    borderColor: 'var(--mantine-color-blue-5)',
                  },
                },
              }}
            >
              Ajouter des fichiers
            </Button>
          </Box>

          <Text size="xs" c="var(--mantine-color-gray-5)" mb="md">
            Formats supportés : PDF, DOC, DOCX, TXT, MD
          </Text>

          {/* Liste des fichiers uploadés */}
          {formData.files.length > 0 && (
            <Stack gap="xs" mt="md">
              {formData.files.map((file, index) => (
                <Flex
                  key={index}
                  justify="space-between"
                  align="center"
                  p="sm"
                  style={{
                    backgroundColor: 'var(--mantine-color-dark-6)',
                    borderRadius: '4px',
                    border: '1px solid var(--mantine-color-dark-4)'
                  }}
                >
                  <Flex align="center" gap="sm">
                    <IconFile size={16} color="var(--mantine-color-blue-4)" />
                    <Text size="sm" c="var(--mantine-color-gray-0)">
                      {file.name}
                    </Text>
                    <Text size="xs" c="var(--mantine-color-gray-5)">
                      ({(file.size / 1024).toFixed(1)} KB)
                    </Text>
                  </Flex>
                  <ActionIcon
                    variant="subtle"
                    color="red"
                    size="sm"
                    onClick={() => removeFile(index)}
                  >
                    <IconX size={14} />
                  </ActionIcon>
                </Flex>
              ))}
            </Stack>
          )}
        </Box>

        {/* Tags de personnalité */}
        <Box>
          <Text size="sm" c="var(--mantine-color-gray-0)" mb="sm">
            Style de réponse souhaité
          </Text>
          <Group gap="sm">
            {PERSONALITY_TAGS.map((tag) => (
              <Box
                key={tag}
                onClick={() => handleTagToggle(tag)}
                style={{
                  backgroundColor: formData.personalityTags.includes(tag)
                    ? 'var(--mantine-color-blue-6)'
                    : 'var(--mantine-color-dark-6)',
                  color: formData.personalityTags.includes(tag)
                    ? 'var(--mantine-color-white)'
                    : 'var(--mantine-color-gray-4)',
                  border: `1px solid ${formData.personalityTags.includes(tag)
                    ? 'var(--mantine-color-blue-5)'
                    : 'var(--mantine-color-dark-4)'}`,
                  borderRadius: '8px',
                  padding: '10px 16px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  transition: 'all 0.2s ease',
                  minHeight: '40px',
                  userSelect: 'none'
                }}
                onMouseEnter={(e) => {
                  if (formData.personalityTags.includes(tag)) {
                    e.currentTarget.style.backgroundColor = 'var(--mantine-color-blue-5)'
                  } else {
                    e.currentTarget.style.backgroundColor = 'var(--mantine-color-dark-5)'
                  }
                  e.currentTarget.style.transform = 'translateY(-1px)'
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)'
                }}
                onMouseLeave={(e) => {
                  if (formData.personalityTags.includes(tag)) {
                    e.currentTarget.style.backgroundColor = 'var(--mantine-color-blue-6)'
                  } else {
                    e.currentTarget.style.backgroundColor = 'var(--mantine-color-dark-6)'
                  }
                  e.currentTarget.style.transform = 'translateY(0)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
              >
                {formData.personalityTags.includes(tag) ? (
                  <IconX size={16} />
                ) : (
                  <IconPlus size={16} />
                )}
                <Text size="sm" fw={500}>
                  {tag}
                </Text>
              </Box>
            ))}
          </Group>
        </Box>

        {/* Informations supplémentaires */}
        <Textarea
          label="Avez-vous d'autres informations à fournir ?"
          placeholder="Des centres d'intérêt, des valeurs ou des préférences à prendre en compte"
          value={formData.additionalInfo}
          onChange={(e) => setFormData(prev => ({ ...prev, additionalInfo: e.target.value }))}
          minRows={4}
          styles={{
            label: { color: 'var(--mantine-color-gray-0)', marginBottom: '8px' },
            input: {
              backgroundColor: 'var(--mantine-color-dark-6)',
              borderColor: 'var(--mantine-color-dark-4)',
              color: 'var(--mantine-color-gray-0)',
              '&::placeholder': { color: 'var(--mantine-color-gray-5)' }
            }
          }}
        />

        {/* Section Avancé */}
        <Box>
          <Button
            variant="subtle"
            leftSection={advancedOpen ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
            onClick={() => setAdvancedOpen(!advancedOpen)}
            color="gray"
            size="sm"
          >
            Avancé
          </Button>
          
          <Collapse in={advancedOpen}>
            <Box mt="md" p="md" style={{
              backgroundColor: 'var(--mantine-color-dark-6)',
              borderRadius: '4px',
              border: '1px solid var(--mantine-color-dark-4)'
            }}>
              <Text size="sm" c="var(--mantine-color-gray-4)">
                Options avancées de configuration...
              </Text>
            </Box>
          </Collapse>
        </Box>

        <Divider color="var(--mantine-color-dark-4)" />

        {/* Actions */}
        <Flex justify="space-between" align="center">
          <Switch
            label="Activer pour les nouveaux chats"
            checked={formData.isActive}
            onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
            color="blue"
            styles={{
              label: { color: 'var(--mantine-color-gray-0)' }
            }}
          />
          
          <Group gap="sm">
            <Button
              variant="outline"
              color="gray"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              variant="outline"
              color="orange"
              onClick={handleTest}
              loading={isTestMode}
              disabled={loading}
            >
              Tester
            </Button>
            <Button
              color="teal"
              onClick={handleSave}
              loading={loading && !isTestMode}
              disabled={loading}
            >
              {isEditing ? 'Mettre à jour' : 'Enregistrer'}
            </Button>
          </Group>
        </Flex>
      </Stack>
    </Box>
  )
}
