import React from 'react'
import * as Sentry from '@sentry/react'
import {
  Box,
  Button,
  Card,
  Center,
  Container,
  Group,
  Stack,
  Text,
  Title,
  Collapse,
  ActionIcon,
  ThemeIcon,
  Divider,
  Code
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconRefresh,
  IconReload,
  IconChevronDown,
  IconChevronUp,
  IconBug,
  IconHome
} from '@tabler/icons-react'
import { useDisclosure } from '@mantine/hooks'
import { getLogger } from '../lib/utils'

const log = getLogger('ErrorBoundary')

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error details
    log.error('ErrorBoundary caught an error:', error, errorInfo)

    // Capture exception in Sentry with additional context
    Sentry.withScope((scope) => {
      scope.setTag('errorBoundary', true)
      scope.setLevel('error')
      scope.setContext('errorInfo', {
        componentStack: errorInfo.componentStack,
        errorBoundary: this.constructor.name,
      })
      Object.keys(errorInfo).forEach((key) => {
        scope.setExtra(key, errorInfo[key as keyof React.ErrorInfo])
      })
      Sentry.captureException(error)
    })

    this.setState({
      error,
      errorInfo,
    })
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props
      const { error } = this.state

      if (Fallback && error) {
        return <Fallback error={error} retry={this.handleRetry} />
      }

      // Default error UI
      return <DefaultErrorFallback error={error} errorInfo={this.state.errorInfo} retry={this.handleRetry} />
    }

    return this.props.children
  }
}

interface DefaultErrorFallbackProps {
  error: Error | null
  errorInfo: React.ErrorInfo | null
  retry: () => void
}

function DefaultErrorFallback({ error, errorInfo, retry }: DefaultErrorFallbackProps) {
  const [opened, { toggle }] = useDisclosure(false)

  const handleReload = () => {
    window.location.reload()
  }

  const handleGoHome = () => {
    window.location.href = '/'
  }

  return (
    <Box
      style={{
        minHeight: '100vh',
        background: 'var(--mantine-color-body)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 'var(--mantine-spacing-xl)'
      }}
    >
      <Container size="sm">
        <Center>
          <Card
            shadow="xl"
            padding="xl"
            radius="lg"
            style={{
              maxWidth: '500px',
              width: '100%',
              border: '1px solid var(--mantine-color-red-3)',
              background: 'var(--mantine-color-body)'
            }}
          >
            <Stack align="center" gap="lg">
              {/* Icône d'erreur avec animation */}
              <ThemeIcon
                size={80}
                radius="xl"
                variant="light"
                color="red"
                style={{
                  animation: 'pulse 2s infinite'
                }}
              >
                <IconAlertTriangle size={40} />
              </ThemeIcon>

              {/* Titre principal */}
              <Stack align="center" gap="xs">
                <Title
                  order={2}
                  ta="center"
                  c="var(--mantine-color-text)"
                  style={{ fontWeight: 600 }}
                >
                  Quelque chose s'est mal passé !
                </Title>
                <Text
                  size="md"
                  ta="center"
                  c="dimmed"
                  style={{ maxWidth: '400px' }}
                >
                  L'application a rencontré une erreur inattendue. Cette erreur a été automatiquement signalée à notre équipe.
                </Text>
              </Stack>

              <Divider style={{ width: '100%' }} />

              {/* Boutons d'action */}
              <Group justify="center" gap="md" style={{ width: '100%' }}>
                <Button
                  leftSection={<IconRefresh size={16} />}
                  onClick={retry}
                  variant="filled"
                  color="blue"
                  size="md"
                  style={{ flex: 1 }}
                >
                  Réessayer
                </Button>
                <Button
                  leftSection={<IconReload size={16} />}
                  onClick={handleReload}
                  variant="light"
                  color="gray"
                  size="md"
                  style={{ flex: 1 }}
                >
                  Recharger
                </Button>
              </Group>

              <Button
                leftSection={<IconHome size={16} />}
                onClick={handleGoHome}
                variant="subtle"
                color="gray"
                size="sm"
                fullWidth
              >
                Retour à l'accueil
              </Button>

              {/* Bouton pour afficher les détails */}
              <Group justify="center" style={{ width: '100%' }}>
                <ActionIcon
                  onClick={toggle}
                  variant="subtle"
                  color="gray"
                  size="lg"
                  radius="xl"
                >
                  {opened ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
                </ActionIcon>
                <Text
                  size="sm"
                  c="dimmed"
                  style={{ cursor: 'pointer' }}
                  onClick={toggle}
                >
                  {opened ? 'Masquer' : 'Afficher'} les détails de l'erreur
                </Text>
              </Group>

              {/* Détails de l'erreur */}
              <Collapse in={opened} style={{ width: '100%' }}>
                <Card
                  padding="md"
                  radius="md"
                  style={{
                    background: 'var(--mantine-color-gray-0)',
                    border: '1px solid var(--mantine-color-gray-3)'
                  }}
                >
                  <Stack gap="sm">
                    <Group gap="xs">
                      <IconBug size={16} color="var(--mantine-color-red-6)" />
                      <Text size="sm" fw={600} c="red">
                        Détails techniques
                      </Text>
                    </Group>

                    {error && (
                      <Box>
                        <Text size="xs" fw={500} mb="xs">
                          Erreur :
                        </Text>
                        <Code
                          block
                          style={{
                            fontSize: '11px',
                            maxHeight: '100px',
                            overflow: 'auto',
                            background: 'var(--mantine-color-red-0)',
                            border: '1px solid var(--mantine-color-red-2)'
                          }}
                        >
                          {error.name}: {error.message}
                        </Code>
                      </Box>
                    )}

                    {error?.stack && (
                      <Box>
                        <Text size="xs" fw={500} mb="xs">
                          Stack trace :
                        </Text>
                        <Code
                          block
                          style={{
                            fontSize: '10px',
                            maxHeight: '150px',
                            overflow: 'auto',
                            background: 'var(--mantine-color-gray-0)',
                            border: '1px solid var(--mantine-color-gray-3)'
                          }}
                        >
                          {error.stack}
                        </Code>
                      </Box>
                    )}

                    {errorInfo?.componentStack && (
                      <Box>
                        <Text size="xs" fw={500} mb="xs">
                          Composant :
                        </Text>
                        <Code
                          block
                          style={{
                            fontSize: '10px',
                            maxHeight: '120px',
                            overflow: 'auto',
                            background: 'var(--mantine-color-blue-0)',
                            border: '1px solid var(--mantine-color-blue-2)'
                          }}
                        >
                          {errorInfo.componentStack}
                        </Code>
                      </Box>
                    )}
                  </Stack>
                </Card>
              </Collapse>
            </Stack>
          </Card>
        </Center>
      </Container>

      {/* Animation CSS */}
      <style>{`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.7;
          }
        }
      `}</style>
    </Box>
  )
}

// Sentry Error Boundary (alternative approach using Sentry's built-in ErrorBoundary)
export const SentryErrorBoundary = Sentry.withErrorBoundary(
  ({ children }: { children: React.ReactNode }) => <>{children}</>,
  {
    fallback: ({ error, resetError }) => (
      <DefaultErrorFallback
        error={error}
        errorInfo={null}
        retry={resetError}
      />
    ),
    beforeCapture: (scope, error, errorInfo) => {
      scope.setTag('errorBoundary', 'sentry')
      scope.setLevel('error')
      if (errorInfo) {
        scope.setContext('errorInfo', {
          componentStack: (errorInfo as any).componentStack || 'Unknown component stack',
        })
      }
    },
  }
)
