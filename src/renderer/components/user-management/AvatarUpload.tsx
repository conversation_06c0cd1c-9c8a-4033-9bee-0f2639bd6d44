import React, { useState, useRef } from 'react'
import {
  Box,
  Avatar,
  Button,
  Group,
  Text,
  ActionIcon,
  Tooltip,
  Stack
} from '@mantine/core'
import { IconCamera, IconTrash, IconUpload } from '@tabler/icons-react'
import { notifications } from '@mantine/notifications'

interface AvatarUploadProps {
  value?: string // URL de l'image actuelle
  onChange?: (imageUrl: string | null) => void
  size?: number
  disabled?: boolean
  userName?: string
}

export const AvatarUpload: React.FC<AvatarUploadProps> = ({
  value,
  onChange,
  size = 120,
  disabled = false,
  userName = 'User'
}) => {
  const [isHovered, setIsHovered] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validation du fichier
    if (!file.type.startsWith('image/')) {
      notifications.show({
        title: 'Erreur',
        message: '<PERSON><PERSON>illez sélectionner un fichier image valide',
        color: 'red'
      })
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB max
      notifications.show({
        title: 'Erreur',
        message: 'La taille du fichier ne doit pas dépasser 5MB',
        color: 'red'
      })
      return
    }

    // Créer une URL temporaire pour l'aperçu
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string
      onChange?.(imageUrl)
      
      notifications.show({
        title: 'Succès',
        message: 'Image téléchargée avec succès',
        color: 'green'
      })
    }
    reader.readAsDataURL(file)
  }

  const handleRemoveImage = () => {
    onChange?.(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const getUserInitial = () => {
    return userName.charAt(0).toUpperCase()
  }

  return (
    <Box>
      <Stack gap="xs" align="center">
        <Box
          pos="relative"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          style={{ cursor: disabled ? 'default' : 'pointer' }}
          onClick={disabled ? undefined : handleUploadClick}
        >
          <Avatar
            size={size}
            radius="md"
            src={value}
            style={{
              border: '3px solid var(--mantine-color-dark-4)',
              transition: 'all 0.2s ease',
              transform: isHovered && !disabled ? 'scale(1.05)' : 'scale(1)',
            }}
          >
            {getUserInitial()}
          </Avatar>

          {/* Overlay au hover */}
          {isHovered && !disabled && (
            <Box
              pos="absolute"
              top={0}
              left={0}
              right={0}
              bottom={0}
              bg="rgba(0, 0, 0, 0.6)"
              style={{
                borderRadius: 'var(--mantine-radius-md)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.2s ease'
              }}
            >
              <IconCamera size={24} color="white" />
            </Box>
          )}

          {/* Badge pour indiquer qu'on peut cliquer */}
          {!disabled && (
            <ActionIcon
              pos="absolute"
              bottom={-8}
              right={-8}
              size="sm"
              radius="xl"
              variant="filled"
              color="blue"
              style={{
                border: '2px solid var(--mantine-color-dark-7)',
              }}
            >
              <IconUpload size={12} />
            </ActionIcon>
          )}
        </Box>

        {/* Actions */}
        {!disabled && (
          <Group gap="xs">
            <Tooltip label="Télécharger une image">
              <Button
                variant="light"
                size="xs"
                leftSection={<IconUpload size={14} />}
                onClick={handleUploadClick}
              >
                Changer
              </Button>
            </Tooltip>
            
            {value && (
              <Tooltip label="Supprimer l'image">
                <ActionIcon
                  variant="light"
                  color="red"
                  size="sm"
                  onClick={handleRemoveImage}
                >
                  <IconTrash size={14} />
                </ActionIcon>
              </Tooltip>
            )}
          </Group>
        )}

        {size > 80 && (
          <Text size="xs" c="dimmed" ta="center">
            {disabled ? 'Image de profil' : 'Cliquez pour changer l\'image'}
          </Text>
        )}
      </Stack>

      {/* Input file caché */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
        onChange={handleFileSelect}
        disabled={disabled}
      />
    </Box>
  )
}
