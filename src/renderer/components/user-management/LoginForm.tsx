import React, { useState } from 'react'
import {
  But<PERSON>,
  Card,
  Checkbox,
  Group,
  PasswordInput,
  Stack,
  Text,
  TextInput,
  Title
} from '@mantine/core'
import { useForm } from '@mantine/form'
import { notifications } from '@mantine/notifications'
import { IconCheck, IconX, Icon<PERSON>ser, IconLock } from '@tabler/icons-react'
import { useAtom } from 'jotai'
import { loginAtom } from '../../stores/atoms/authAtoms'
import { LoginRequest } from '../../../shared/types/user'

interface LoginFormProps {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  onError
}) => {
  const [loading, setLoading] = useState(false)
  const [, login] = useAtom(loginAtom)

  const form = useForm({
    initialValues: {
      username: '',
      password: '',
      rememberMe: false
    },
    validate: {
      username: (value) => {
        if (!value) return 'Le nom d\'utilisateur est requis'
        if (value.length < 3) return 'Le nom d\'utilisateur doit contenir au moins 3 caractères'
        return null
      },
      password: (value) => {
        if (!value) return 'Le mot de passe est requis'
        if (value.length < 6) return 'Le mot de passe doit contenir au moins 6 caractères'
        return null
      }
    }
  })

  const handleSubmit = async (values: typeof form.values) => {
    try {
      setLoading(true)

      const credentials: LoginRequest = {
        username: values.username,
        password: values.password,
        rememberMe: values.rememberMe
      }

      const result = await login(credentials)

      if (result.success) {
        notifications.show({
          title: 'Connexion réussie',
          message: `Bienvenue ${result.user?.displayName || values.username} !`,
          color: 'green',
          icon: <IconCheck size={16} />
        })
        onSuccess?.()
      } else {
        const errorMessage = result.error || 'Erreur de connexion'
        notifications.show({
          title: 'Erreur de connexion',
          message: errorMessage,
          color: 'red',
          icon: <IconX size={16} />
        })
        onError?.(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Une erreur est survenue'
      notifications.show({
        title: 'Erreur',
        message: errorMessage,
        color: 'red',
        icon: <IconX size={16} />
      })
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card
      shadow="md"
      padding="xl"
      radius="md"
      style={{
        maxWidth: 400,
        margin: '0 auto',
        backgroundColor: 'var(--mantine-color-dark-7)',
        borderColor: 'var(--mantine-color-dark-5)',
      }}
    >
      <Stack gap="md">
        <div style={{ textAlign: 'center' }}>
          <Title order={2} c="white" mb="xs">
            Connexion DataTec
          </Title>
          <Text c="dimmed" size="sm">
            Connectez-vous à votre compte
          </Text>
        </div>

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Nom d'utilisateur"
              placeholder="Votre nom d'utilisateur"
              leftSection={<IconUser size={16} />}
              required
              {...form.getInputProps('username')}
            />

            <PasswordInput
              label="Mot de passe"
              placeholder="Votre mot de passe"
              leftSection={<IconLock size={16} />}
              required
              {...form.getInputProps('password')}
            />

            <Checkbox
              label="Se souvenir de moi"
              {...form.getInputProps('rememberMe', { type: 'checkbox' })}
            />

            <Button
              type="submit"
              fullWidth
              loading={loading}
              size="md"
              mt="md"
            >
              Se connecter
            </Button>
          </Stack>
        </form>

        <div style={{ marginTop: '1rem' }}>
          <Text size="sm" c="dimmed" ta="center" mb="xs">
            Comptes de démonstration :
          </Text>
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="xs" c="dimmed">Admin:</Text>
              <Text size="xs" c="blue">admin / admin123</Text>
            </Group>
            <Group justify="space-between">
              <Text size="xs" c="dimmed">Utilisateur:</Text>
              <Text size="xs" c="blue">demo / demo123</Text>
            </Group>
            <Group justify="space-between">
              <Text size="xs" c="dimmed">Modérateur:</Text>
              <Text size="xs" c="blue">maggie.davidson / password123</Text>
            </Group>
          </Stack>
        </div>
      </Stack>
    </Card>
  )
}

// Composant de page de connexion complète
export const LoginPage: React.FC = () => {
  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '2rem'
      }}
    >
      <LoginForm
        onSuccess={() => {
          // Redirection sera gérée par le routeur
          console.log('Connexion réussie')
        }}
        onError={(error) => {
          console.error('Erreur de connexion:', error)
        }}
      />
    </div>
  )
}
