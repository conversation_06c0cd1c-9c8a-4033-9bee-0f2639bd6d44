import React, { useEffect, useState } from 'react'
import { Center, Loader, Text } from '@mantine/core'
import { useAtom } from 'jotai'
import { initAuthAtom } from '../../stores/atoms/authAtoms'

interface AuthInitializerProps {
  children: React.ReactNode
}

export const AuthInitializer: React.FC<AuthInitializerProps> = ({ children }) => {
  const [, initAuth] = useAtom(initAuthAtom)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const initialize = async () => {
      try {
        setIsLoading(true)
        await initAuth()
        setIsInitialized(true)
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'authentification:', error)
        setIsInitialized(true) // Continuer même en cas d'erreur
      } finally {
        setIsLoading(false)
      }
    }

    initialize()
  }, [initAuth])

  if (isLoading) {
    return (
      <Center h="100vh">
        <div style={{ textAlign: 'center' }}>
          <Loader size="lg" mb="md" />
          <Text c="dimmed">Initialisation de l'authentification...</Text>
        </div>
      </Center>
    )
  }

  return <>{children}</>
}
