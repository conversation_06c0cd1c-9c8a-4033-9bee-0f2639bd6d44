import React, { useEffect, useState } from 'react'
import {
  Badge,
  Card,
  Grid,
  Group,
  Progress,
  Stack,
  Text,
  Title,
  SimpleGrid,
  RingProgress,
  Center
} from '@mantine/core'
import {
  IconUsers,
  IconUserCheck,
  IconUserX,
  IconShield,
  IconActivity,
  IconTrendingUp
} from '@tabler/icons-react'
import { userManagementService } from '../../services/UserManagementService'
import { UserStats, UserRole, UserStatus } from '../../../shared/types/user'

export const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      setLoading(true)

      // S'assurer que le service est initialisé
      if (!userManagementService.isInitialized()) {
        console.log('🔧 Initialisation du service de gestion des utilisateurs...')
        await userManagementService.initialize()
      }

      const userStats = await userManagementService.getUserStats()
      setStats(userStats)
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Stack p="md" gap="xl">
        <Title order={3}>Tableau de bord administrateur</Title>
        <Text c="dimmed">Chargement des statistiques...</Text>
      </Stack>
    )
  }

  if (!stats) {
    return (
      <Stack p="md" gap="xl">
        <Title order={3}>Tableau de bord administrateur</Title>
        <Text c="red">Erreur lors du chargement des statistiques</Text>
      </Stack>
    )
  }

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN: return 'red'
      case UserRole.MODERATOR: return 'blue'
      case UserRole.USER: return 'green'
      case UserRole.GUEST: return 'gray'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE: return 'green'
      case UserStatus.INACTIVE: return 'gray'
      case UserStatus.SUSPENDED: return 'red'
      case UserStatus.PENDING: return 'yellow'
      default: return 'gray'
    }
  }

  const activePercentage = stats.totalUsers > 0 ? (stats.activeUsers / stats.totalUsers) * 100 : 0

  return (
    <Stack p="md" gap="xl">
      <Title order={3}>Tableau de bord administrateur</Title>

      {/* Statistiques principales */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md">
        <Card withBorder padding="lg" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                Total utilisateurs
              </Text>
              <Text fw={700} size="xl">
                {stats.totalUsers}
              </Text>
            </div>
            <IconUsers size={32} color="var(--mantine-color-blue-6)" />
          </Group>
        </Card>

        <Card withBorder padding="lg" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                Utilisateurs actifs
              </Text>
              <Text fw={700} size="xl">
                {stats.activeUsers}
              </Text>
            </div>
            <IconUserCheck size={32} color="var(--mantine-color-green-6)" />
          </Group>
        </Card>

        <Card withBorder padding="lg" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                Nouveaux ce mois
              </Text>
              <Text fw={700} size="xl">
                {stats.newUsersThisMonth}
              </Text>
            </div>
            <IconTrendingUp size={32} color="var(--mantine-color-orange-6)" />
          </Group>
        </Card>

        <Card withBorder padding="lg" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
                Taux d'activité
              </Text>
              <Text fw={700} size="xl">
                {activePercentage.toFixed(1)}%
              </Text>
            </div>
            <IconActivity size={32} color="var(--mantine-color-violet-6)" />
          </Group>
        </Card>
      </SimpleGrid>

      <Grid>
        {/* Répartition par rôles */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder padding="lg" radius="md" h="100%">
            <Title order={4} mb="md">Répartition par rôles</Title>
            <Stack gap="md">
              {Object.entries(stats.usersByRole).map(([role, count]) => {
                const percentage = stats.totalUsers > 0 ? (count / stats.totalUsers) * 100 : 0
                return (
                  <div key={role}>
                    <Group justify="space-between" mb="xs">
                      <Group gap="xs">
                        <Badge color={getRoleColor(role as UserRole)} size="sm">
                          {role}
                        </Badge>
                        <Text size="sm">{count} utilisateurs</Text>
                      </Group>
                      <Text size="sm" c="dimmed">
                        {percentage.toFixed(1)}%
                      </Text>
                    </Group>
                    <Progress
                      value={percentage}
                      color={getRoleColor(role as UserRole)}
                      size="sm"
                    />
                  </div>
                )
              })}
            </Stack>
          </Card>
        </Grid.Col>

        {/* Répartition par statuts */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder padding="lg" radius="md" h="100%">
            <Title order={4} mb="md">Répartition par statuts</Title>
            <Stack gap="md">
              {Object.entries(stats.usersByStatus).map(([status, count]) => {
                const percentage = stats.totalUsers > 0 ? (count / stats.totalUsers) * 100 : 0
                return (
                  <div key={status}>
                    <Group justify="space-between" mb="xs">
                      <Group gap="xs">
                        <Badge color={getStatusColor(status as UserStatus)} size="sm">
                          {status}
                        </Badge>
                        <Text size="sm">{count} utilisateurs</Text>
                      </Group>
                      <Text size="sm" c="dimmed">
                        {percentage.toFixed(1)}%
                      </Text>
                    </Group>
                    <Progress
                      value={percentage}
                      color={getStatusColor(status as UserStatus)}
                      size="sm"
                    />
                  </div>
                )
              })}
            </Stack>
          </Card>
        </Grid.Col>

        {/* Graphique circulaire des utilisateurs actifs */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder padding="lg" radius="md" h="100%">
            <Title order={4} mb="md">Activité des utilisateurs</Title>
            <Center>
              <RingProgress
                size={180}
                thickness={16}
                sections={[
                  { value: activePercentage, color: 'green', tooltip: `${stats.activeUsers} actifs` },
                  { 
                    value: 100 - activePercentage, 
                    color: 'gray', 
                    tooltip: `${stats.totalUsers - stats.activeUsers} inactifs` 
                  }
                ]}
                label={
                  <Center>
                    <div style={{ textAlign: 'center' }}>
                      <Text fw={700} size="xl">
                        {activePercentage.toFixed(1)}%
                      </Text>
                      <Text c="dimmed" size="sm">
                        Actifs
                      </Text>
                    </div>
                  </Center>
                }
              />
            </Center>
          </Card>
        </Grid.Col>

        {/* Utilisateurs les plus actifs */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card withBorder padding="lg" radius="md" h="100%">
            <Title order={4} mb="md">Utilisateurs les plus actifs</Title>
            <Stack gap="sm">
              {stats.mostActiveUsers.slice(0, 5).map((userActivity, index) => (
                <Group key={userActivity.user.id} justify="space-between">
                  <Group gap="sm">
                    <Text fw={500} size="sm">
                      #{index + 1}
                    </Text>
                    <div>
                      <Text size="sm" fw={500}>
                        {userActivity.user.displayName}
                      </Text>
                      <Text size="xs" c="dimmed">
                        @{userActivity.user.username}
                      </Text>
                    </div>
                  </Group>
                  <Badge variant="light" size="sm">
                    {userActivity.activityScore} connexions
                  </Badge>
                </Group>
              ))}
              {stats.mostActiveUsers.length === 0 && (
                <Text c="dimmed" size="sm" ta="center">
                  Aucune donnée d'activité disponible
                </Text>
              )}
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>
    </Stack>
  )
}
