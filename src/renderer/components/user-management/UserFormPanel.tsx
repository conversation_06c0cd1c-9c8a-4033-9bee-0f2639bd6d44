import React, { useState, useEffect } from 'react'
import {
  Button,
  Group,
  Select,
  Stack,
  Switch,
  TextInput,
  Title,
  Text,
  Box,
  Divider,
  ActionIcon,
  Menu,
  Tabs,
  Grid,
  Paper,
  Breadcrumbs,
  Anchor
} from '@mantine/core'
import { useIsSmallScreen } from '@/hooks/useScreenChange'
import { useForm } from '@mantine/form'
import { notifications } from '@mantine/notifications'
import { IconCheck, IconX, IconUser, IconDots, IconEdit, IconKey, IconTrash, IconShield, IconSettings, IconLock, IconChevronRight, IconPrinter, IconDownload } from '@tabler/icons-react'
import { AvatarUpload } from './AvatarUpload'
import { CreateUserRequest, PublicUser, UpdateUserRequest, UserRole } from '../../../shared/types/user'
import { userManagementService } from '@/services/UserManagementService'
import { useRoles } from '../../hooks/useRoles'

interface UserFormPanelProps {
  user?: PublicUser // Si fourni, mode édition
  onSuccess?: (user: PublicUser) => void
  onCancel?: () => void
}

export const UserFormPanel: React.FC<UserFormPanelProps> = ({
  user,
  onSuccess,
  onCancel
}) => {
  const [loading, setLoading] = useState(false)

  // Vérification de sécurité pour useRoles
  let getRolesForSelect: () => any[] = () => []
  try {
    const rolesHook = useRoles()
    getRolesForSelect = rolesHook.getRolesForSelect || (() => [])
  } catch (error) {
    console.error('❌ Erreur avec useRoles:', error)
  }

  const [activeTab, setActiveTab] = useState<string>('access-rights')

  // Vérification de sécurité pour useIsSmallScreen
  let isSmallScreen = false
  try {
    isSmallScreen = useIsSmallScreen()
  } catch (error) {
    console.error('❌ Erreur avec useIsSmallScreen:', error)
  }

  const isEditing = !!user && !!user.id

  const form = useForm({
    initialValues: {
      username: user?.username || '',
      email: user?.email || '',
      displayName: user?.displayName || '',
      role: user?.role || 'user',
      language: user?.language || 'fr',
      isActive: user?.isActive ?? true,
      avatar: user?.avatar || '',
      password: '',
      confirmPassword: ''
    },
    validate: {
      username: (value) => {
        if (!value) return 'Le nom d\'utilisateur est requis'
        if (value.length < 3) return 'Le nom d\'utilisateur doit contenir au moins 3 caractères'
        if (!/^[a-zA-Z0-9._-]+$/.test(value)) return 'Le nom d\'utilisateur ne peut contenir que des lettres, chiffres, points, tirets et underscores'
        return null
      },
      email: (value) => {
        if (!value) return 'L\'email est requis'
        if (!/^\S+@\S+\.\S+$/.test(value)) return 'Format d\'email invalide'
        return null
      },
      displayName: (value) => {
        if (!value) return 'Le nom d\'affichage est requis'
        if (value.length < 2) return 'Le nom d\'affichage doit contenir au moins 2 caractères'
        return null
      },
      password: (value) => {
        if (!isEditing && !value) return 'Le mot de passe est requis'
        if (value && value.length < 6) return 'Le mot de passe doit contenir au moins 6 caractères'
        return null
      },
      confirmPassword: (value, values) => {
        if (!isEditing && value !== values.password) return 'Les mots de passe ne correspondent pas'
        if (isEditing && values.password && value !== values.password) return 'Les mots de passe ne correspondent pas'
        return null
      }
    }
  })

  // Réinitialiser le formulaire quand l'utilisateur change
  useEffect(() => {
    if (user && user.id) {
      // Mode édition - charger les données de l'utilisateur
      form.setValues({
        username: user.username || '',
        email: user.email || '',
        displayName: user.displayName || '',
        role: user.role || 'user',
        language: user.language || 'fr',
        isActive: user.isActive ?? true,
        password: '',
        confirmPassword: ''
      })
    } else {
      // Mode création - valeurs par défaut
      form.setValues({
        username: '',
        email: '',
        displayName: '',
        role: 'user',
        language: 'fr',
        isActive: true,
        password: '',
        confirmPassword: ''
      })
    }
  }, [user])

  const handleSubmit = async (values: typeof form.values) => {
    try {
      setLoading(true)

      // S'assurer que le service est initialisé
      if (!userManagementService.isInitialized()) {
        console.log('🔧 Initialisation du service de gestion des utilisateurs...')
        await userManagementService.initialize()
      }

      if (isEditing && user?.id) {
        // Mode édition
        const updateData: UpdateUserRequest = {
          email: values.email,
          displayName: values.displayName,
          role: values.role as any,
          language: values.language,
          isActive: values.isActive,
          avatar: values.avatar
        }

        // Ajouter le mot de passe seulement s'il est fourni
        if (values.password) {
          updateData.password = values.password
        }

        const updatedUser = await userManagementService.updateUser(user.id, updateData)
        notifications.show({
          title: 'Succès',
          message: 'Utilisateur mis à jour avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
        onSuccess?.(updatedUser)
        form.reset()
      } else {
        // Mode création
        const createData: CreateUserRequest = {
          username: values.username,
          email: values.email,
          displayName: values.displayName,
          password: values.password,
          role: values.role as any,
          language: values.language,
          avatar: values.avatar
        }

        const result = await userManagementService.createUser(createData)
        const newUser = result.user
        notifications.show({
          title: 'Succès',
          message: 'Utilisateur créé avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
        onSuccess?.(newUser)
        form.reset()
      }
    } catch (error: any) {
      console.error('❌ Erreur lors de la soumission:', error)
      notifications.show({
        title: 'Erreur',
        message: error.message || 'Une erreur est survenue',
        color: 'red',
        icon: <IconX size={16} />
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    form.reset()
    onCancel?.()
  }

  const handleEditInfo = () => {
    // Action par défaut - le formulaire est déjà en mode édition
  }

  const handleChangePassword = () => {
    // Changer vers l'onglet Sécurité
    setActiveTab('security')
    // Focus sur le champ mot de passe après un délai
    setTimeout(() => {
      const passwordInput = document.querySelector('input[placeholder="••••••••"]') as HTMLInputElement
      if (passwordInput) {
        passwordInput.focus()
      }
    }, 300)
  }

  const handleDeleteUser = async () => {
    if (!user?.id) return

    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${user.displayName}" ? Cette action est irréversible.`)) {
      try {
        await userManagementService.deleteUser(user.id)
        notifications.show({
          title: 'Succès',
          message: 'Utilisateur supprimé avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
        onCancel?.() // Retourner à la liste
      } catch (error) {
        notifications.show({
          title: 'Erreur',
          message: 'Impossible de supprimer l\'utilisateur',
          color: 'red',
          icon: <IconX size={16} />
        })
      }
    }
  }

  return (
    <Box h="100%" px="md" py="sm">
      <Stack gap="md" h="100%">
        {/* Header style Odoo avec breadcrumb */}
        <Box>
          {/* Breadcrumb navigation style Odoo */}
          <Group justify="space-between" align="center" mb="sm">
            <Breadcrumbs
              separator={<Text size="xs" c="dimmed">/</Text>}
              separatorMargin={4}
              styles={{
                root: {
                  fontSize: '0.875rem'
                }
              }}
            >
              <Anchor
                size="sm"
                c="blue.4"
                onClick={handleCancel}
                style={{
                  cursor: 'pointer',
                  textDecoration: 'none',
                  fontSize: '0.875rem'
                }}
                __hover={{
                  textDecoration: 'underline'
                }}
              >
                Utilisateurs
              </Anchor>
              <Text size="sm" fw={500} c="white">
                {isEditing ? (user?.displayName || user?.username) : 'Nouveau'}
              </Text>
            </Breadcrumbs>

            {/* Actions contextuelles style Odoo */}
            <Group gap="xs">
              <Button
                variant="default"
                size="xs"
                leftSection={<IconPrinter size={12} />}
                styles={{
                  root: {
                    border: '1px solid var(--mantine-color-dark-4)',
                    backgroundColor: 'var(--mantine-color-dark-6)',
                    fontSize: '0.75rem'
                  }
                }}
              >
                Imprimer
              </Button>

              <Menu shadow="md" width={200}>
                <Menu.Target>
                  <Button
                    variant="default"
                    size="xs"
                    rightSection={<IconChevronRight size={10} />}
                    styles={{
                      root: {
                        border: '1px solid var(--mantine-color-dark-4)',
                        backgroundColor: 'var(--mantine-color-dark-6)',
                        fontSize: '0.75rem'
                      }
                    }}
                  >
                    Action
                  </Button>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item
                    leftSection={<IconEdit size={14} />}
                    onClick={handleEditInfo}
                  >
                    Modifier les informations
                  </Menu.Item>
                  <Menu.Item
                    leftSection={<IconKey size={14} />}
                    onClick={handleChangePassword}
                  >
                    Changer le mot de passe
                  </Menu.Item>
                  {isEditing && (
                    <>
                      <Menu.Divider />
                      <Menu.Item
                        leftSection={<IconTrash size={14} />}
                        color="red"
                        onClick={handleDeleteUser}
                      >
                        Supprimer
                      </Menu.Item>
                    </>
                  )}
                </Menu.Dropdown>
              </Menu>

              {isEditing && (
                <Group gap={4}>
                  <Text size="xs" c="dimmed">
                    1/1
                  </Text>
                  <ActionIcon variant="subtle" size="xs" color="gray">
                    <IconChevronRight size={10} />
                  </ActionIcon>
                </Group>
              )}

              <Button
                size="xs"
                variant="filled"
                color="blue"
                styles={{
                  root: {
                    fontSize: '0.75rem'
                  }
                }}
              >
                {isEditing ? 'Nouveau' : 'Nouveau'}
              </Button>
            </Group>
          </Group>
        </Box>

        <Divider mx={0} />

        {/* Formulaire style Odoo - Aligné avec le header */}
        <Box flex={1} className="overflow-auto">
          <form onSubmit={form.onSubmit(handleSubmit)}>
            <Stack gap="md" px={0} py="sm">
              {/* En-tête principal style Odoo - Version compacte */}
              <Paper p="md" radius="md" withBorder>
                <Grid>
                  <Grid.Col span={9}>
                    <Stack gap="md">
                      {/* Nom d'utilisateur - Taille réduite */}
                      <Box>
                        <Text size="xs" fw={500} c="dimmed" mb={3}>
                          Name
                        </Text>
                        <TextInput
                          placeholder="John Smith"
                          size="md"
                          variant="unstyled"
                          styles={{
                            input: {
                              fontSize: '1.25rem',
                              fontWeight: 600,
                              padding: '2px 0',
                              borderBottom: '1px solid var(--mantine-color-dark-4)',
                              borderRadius: 0,
                              backgroundColor: 'transparent'
                            }
                          }}
                          {...form.getInputProps('displayName')}
                          required
                        />
                      </Box>

                      {/* Email - Taille réduite */}
                      <Box>
                        <Text size="xs" fw={500} c="dimmed" mb={3}>
                          Email Address
                        </Text>
                        <TextInput
                          placeholder="<EMAIL>"
                          size="sm"
                          variant="unstyled"
                          type="email"
                          styles={{
                            input: {
                              fontSize: '0.95rem',
                              padding: '2px 0',
                              borderBottom: '1px solid var(--mantine-color-dark-4)',
                              borderRadius: 0,
                              backgroundColor: 'transparent'
                            }
                          }}
                          {...form.getInputProps('email')}
                          required
                        />
                      </Box>

                      {/* Username - Compact */}
                      <Box>
                        <Text size="xs" fw={500} c="dimmed" mb={3}>
                          Username
                        </Text>
                        <TextInput
                          placeholder="john.doe"
                          size="sm"
                          variant="unstyled"
                          disabled={isEditing}
                          styles={{
                            input: {
                              fontSize: '0.875rem',
                              padding: '2px 0',
                              borderBottom: '1px solid var(--mantine-color-dark-5)',
                              borderRadius: 0,
                              backgroundColor: 'transparent',
                              opacity: isEditing ? 0.6 : 1
                            }
                          }}
                          {...form.getInputProps('username')}
                          required
                        />
                      </Box>
                    </Stack>
                  </Grid.Col>

                  <Grid.Col span={3}>
                    <Box ta="center">
                      <AvatarUpload
                        value={form.values.avatar}
                        onChange={(imageUrl) => form.setFieldValue('avatar', imageUrl || '')}
                        userName={form.values.displayName || form.values.username}
                        size={90}
                        disabled={loading}
                      />
                    </Box>
                  </Grid.Col>
                </Grid>
              </Paper>

              {/* Système d'onglets style Odoo - Version compacte */}
              <Tabs value={activeTab} onChange={setActiveTab}>
                <Tabs.List>
                  <Tabs.Tab
                    value="access-rights"
                    leftSection={<IconShield size={14} />}
                    style={{ fontSize: '0.875rem' }}
                  >
                    Droits d'accès
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="preferences"
                    leftSection={<IconSettings size={14} />}
                    style={{ fontSize: '0.875rem' }}
                  >
                    Préférences
                  </Tabs.Tab>
                  <Tabs.Tab
                    value="security"
                    leftSection={<IconLock size={14} />}
                    style={{ fontSize: '0.875rem' }}
                  >
                    Sécurité
                  </Tabs.Tab>
                </Tabs.List>

                {/* Onglet Droits d'accès */}
                <Tabs.Panel value="access-rights" pt="md">
                  <Paper p="md" radius="md" withBorder>
                    <Stack gap="md">
                      <Title order={5} size="sm" fw={600}>Droits d'accès</Title>

                      <Select
                        label="Rôle"
                        placeholder="Sélectionner un rôle"
                        required
                        size="sm"
                        data={getRolesForSelect()}
                        {...form.getInputProps('role')}
                      />

                      {isEditing && (
                        <Switch
                          label="Compte actif"
                          description="Désactiver empêche l'utilisateur de se connecter"
                          size="sm"
                          {...form.getInputProps('isActive', { type: 'checkbox' })}
                        />
                      )}
                    </Stack>
                  </Paper>
                </Tabs.Panel>

                {/* Onglet Préférences */}
                <Tabs.Panel value="preferences" pt="md">
                  <Paper p="md" radius="md" withBorder>
                    <Stack gap="md">
                      <Title order={5} size="sm" fw={600}>Préférences</Title>

                      <Select
                        label="Langue"
                        placeholder="Sélectionner une langue"
                        required
                        size="sm"
                        data={[
                          { value: 'fr', label: '🇫🇷 Français' },
                          { value: 'en', label: '🇺🇸 English (US)' },
                          { value: 'es', label: '🇪🇸 Español' },
                          { value: 'de', label: '🇩🇪 Deutsch' },
                          { value: 'it', label: '🇮🇹 Italiano' },
                          { value: 'pt', label: '🇵🇹 Português' },
                          { value: 'ar', label: '🇸🇦 العربية' }
                        ]}
                        {...form.getInputProps('language')}
                      />
                    </Stack>
                  </Paper>
                </Tabs.Panel>

                {/* Onglet Sécurité */}
                <Tabs.Panel value="security" pt="md">
                  <Paper p="md" radius="md" withBorder>
                    <Stack gap="md">
                      <Title order={5} size="sm" fw={600}>Sécurité</Title>

                      {!isEditing ? (
                        <>
                          <TextInput
                            label="Mot de passe"
                            placeholder="••••••••"
                            required
                            type="password"
                            size="sm"
                            {...form.getInputProps('password')}
                          />

                          <TextInput
                            label="Confirmer le mot de passe"
                            placeholder="••••••••"
                            required
                            type="password"
                            size="sm"
                            {...form.getInputProps('confirmPassword')}
                          />
                        </>
                      ) : (
                        <Box data-password-section>
                          <Divider
                            label="Changer le mot de passe (optionnel)"
                            labelPosition="left"
                            mb="sm"
                            styles={{
                              label: { fontSize: '0.75rem' }
                            }}
                          />
                          <Stack gap="sm">
                            <TextInput
                              label="Nouveau mot de passe"
                              placeholder="••••••••"
                              type="password"
                              size="sm"
                              {...form.getInputProps('password')}
                            />

                            <TextInput
                              label="Confirmer le nouveau mot de passe"
                              placeholder="••••••••"
                              type="password"
                              size="sm"
                              {...form.getInputProps('confirmPassword')}
                            />
                          </Stack>
                        </Box>
                      )}
                    </Stack>
                  </Paper>
                </Tabs.Panel>
              </Tabs>
            </Stack>
          </form>
        </Box>

        {/* Actions - Alignées avec le header */}
        <Box>
          <Divider mb="md" mx={0} />
          <Group justify="flex-end" gap="sm" px={0}>
            <Button
              variant="subtle"
              color="gray"
              onClick={handleCancel}
              disabled={loading}
              size="sm"
            >
              Annuler
            </Button>
            <Button
              type="submit"
              loading={loading}
              onClick={form.onSubmit(handleSubmit)}
              leftSection={<IconCheck size={16} />}
              size="sm"
            >
              {isEditing ? 'Mettre à jour' : 'Créer'}
            </Button>
          </Group>
        </Box>
      </Stack>
    </Box>
  )
}
