import React, { useEffect, useState } from 'react'
import { Box, Button, Center, Stack, Text, Title } from '@mantine/core'
import { IconLock, IconShieldX, IconUser } from '@tabler/icons-react'
import { useAtomValue } from 'jotai'
import { useNavigate } from '@tanstack/react-router'
import { currentUserAtom, isAuthenticatedAtom } from '../../stores/atoms/authAtoms'
import { permissionService } from '../../services/PermissionService'
import { PermissionAction, PermissionResource, UserRole } from '../../../shared/types/user'

// ===== INTERFACES =====

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: {
    action: PermissionAction
    resource: PermissionResource
  }
  requiredRole?: UserRole
  fallback?: React.ReactNode
  redirectTo?: string
}

interface PermissionGuardProps {
  children: React.ReactNode
  permission: {
    action: PermissionAction
    resource: PermissionResource
  }
  fallback?: React.ReactNode
  showError?: boolean
}

interface RoleGuardProps {
  children: React.ReactNode
  requiredRole: UserRole
  fallback?: React.ReactNode
  showError?: boolean
}

// ===== COMPOSANT PRINCIPAL DE PROTECTION =====

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  fallback,
  redirectTo
}) => {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const currentUser = useAtomValue(currentUserAtom)
  const navigate = useNavigate()
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAccess()
  }, [isAuthenticated, currentUser, requiredPermission, requiredRole])

  const checkAccess = async () => {
    try {
      setLoading(true)

      // Vérifier l'authentification
      if (!isAuthenticated || !currentUser) {
        setHasAccess(false)
        if (redirectTo) {
          navigate({ to: redirectTo, replace: true })
          return
        }
        setLoading(false)
        return
      }

      // Vérifier le rôle si requis
      if (requiredRole) {
        const userRole = currentUser.role as 'admin' | 'user' | 'guest'
        const mappedRole = userRole === 'admin' ? UserRole.ADMIN : 
                          userRole === 'user' ? UserRole.USER : UserRole.GUEST
        
        if (mappedRole !== requiredRole && !permissionService.isRoleHigherThan(mappedRole, requiredRole)) {
          setHasAccess(false)
          setLoading(false)
          return
        }
      }

      // Vérifier la permission si requise
      if (requiredPermission) {
        const hasPermission = await permissionService.currentUserHasPermission(
          requiredPermission.action,
          requiredPermission.resource
        )
        setHasAccess(hasPermission)
      } else {
        setHasAccess(true)
      }
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error)
      setHasAccess(false)
    } finally {
      setLoading(false)
    }
  }

  // Affichage pendant le chargement
  if (loading) {
    return (
      <Center h="100vh">
        <Text c="dimmed">Vérification des permissions...</Text>
      </Center>
    )
  }

  // Affichage si pas d'accès
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    return <AccessDeniedMessage />
  }

  // Affichage du contenu protégé
  return <>{children}</>
}

// ===== GARDE DE PERMISSION =====

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  fallback,
  showError = false
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)

  useEffect(() => {
    checkPermission()
  }, [permission])

  const checkPermission = async () => {
    try {
      const result = await permissionService.currentUserHasPermission(
        permission.action,
        permission.resource
      )
      setHasPermission(result)
    } catch (error) {
      console.error('Erreur lors de la vérification de permission:', error)
      setHasPermission(false)
    }
  }

  if (hasPermission === null) {
    return null // Ou un loader
  }

  if (!hasPermission) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    if (showError) {
      return <PermissionDeniedMessage />
    }
    
    return null
  }

  return <>{children}</>
}

// ===== GARDE DE RÔLE =====

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  requiredRole,
  fallback,
  showError = false
}) => {
  const currentUser = useAtomValue(currentUserAtom)
  const [hasRole, setHasRole] = useState<boolean | null>(null)

  useEffect(() => {
    checkRole()
  }, [currentUser, requiredRole])

  const checkRole = async () => {
    if (!currentUser) {
      setHasRole(false)
      return
    }

    try {
      const userRole = currentUser.role as 'admin' | 'user' | 'guest'
      const mappedRole = userRole === 'admin' ? UserRole.ADMIN : 
                        userRole === 'user' ? UserRole.USER : UserRole.GUEST

      const hasRequiredRole = mappedRole === requiredRole || 
                             permissionService.isRoleHigherThan(mappedRole, requiredRole)
      
      setHasRole(hasRequiredRole)
    } catch (error) {
      console.error('Erreur lors de la vérification du rôle:', error)
      setHasRole(false)
    }
  }

  if (hasRole === null) {
    return null
  }

  if (!hasRole) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    if (showError) {
      return <RoleDeniedMessage requiredRole={requiredRole} />
    }
    
    return null
  }

  return <>{children}</>
}

// ===== MESSAGES D'ERREUR =====

const AccessDeniedMessage: React.FC = () => {
  const navigate = useNavigate()

  return (
    <Center h="100vh">
      <Stack align="center" gap="md">
        <IconShieldX size={64} color="var(--mantine-color-red-6)" />
        <Title order={2} c="red">
          Accès refusé
        </Title>
        <Text c="dimmed" ta="center" maw={400}>
          Vous n'avez pas les permissions nécessaires pour accéder à cette page.
        </Text>
        <Button
          variant="light"
          onClick={() => navigate({ to: '/', replace: true })}
        >
          Retour à l'accueil
        </Button>
      </Stack>
    </Center>
  )
}

const PermissionDeniedMessage: React.FC = () => (
  <Box p="md" style={{ textAlign: 'center' }}>
    <IconLock size={32} color="var(--mantine-color-orange-6)" />
    <Text c="orange" size="sm" mt="xs">
      Permission insuffisante
    </Text>
  </Box>
)

const RoleDeniedMessage: React.FC<{ requiredRole: UserRole }> = ({ requiredRole }) => (
  <Box p="md" style={{ textAlign: 'center' }}>
    <IconUser size={32} color="var(--mantine-color-orange-6)" />
    <Text c="orange" size="sm" mt="xs">
      Rôle {requiredRole} requis
    </Text>
  </Box>
)

// ===== HOOKS UTILITAIRES =====

export const usePermission = (action: PermissionAction, resource: PermissionResource) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)

  useEffect(() => {
    const checkPermission = async () => {
      try {
        const result = await permissionService.currentUserHasPermission(action, resource)
        setHasPermission(result)
      } catch (error) {
        console.error('Erreur lors de la vérification de permission:', error)
        setHasPermission(false)
      }
    }

    checkPermission()
  }, [action, resource])

  return hasPermission
}

export const useRole = (requiredRole: UserRole) => {
  const currentUser = useAtomValue(currentUserAtom)
  const [hasRole, setHasRole] = useState<boolean | null>(null)

  useEffect(() => {
    const checkRole = async () => {
      if (!currentUser) {
        setHasRole(false)
        return
      }

      try {
        const userRole = currentUser.role as 'admin' | 'user' | 'guest'
        const mappedRole = userRole === 'admin' ? UserRole.ADMIN : 
                          userRole === 'user' ? UserRole.USER : UserRole.GUEST

        const hasRequiredRole = mappedRole === requiredRole || 
                               permissionService.isRoleHigherThan(mappedRole, requiredRole)
        
        setHasRole(hasRequiredRole)
      } catch (error) {
        console.error('Erreur lors de la vérification du rôle:', error)
        setHasRole(false)
      }
    }

    checkRole()
  }, [currentUser, requiredRole])

  return hasRole
}
