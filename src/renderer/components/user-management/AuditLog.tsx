import React, { useState, useEffect } from 'react'
import {
  <PERSON>ge,
  Box,
  Button,
  Card,
  Flex,
  Group,
  Pagination,
  Select,
  Stack,
  Table,
  Text,
  TextInput,
  Title
} from '@mantine/core'
import {
  IconCalendar,
  IconFilter,
  IconRefresh,
  IconSearch,
  IconUser
} from '@tabler/icons-react'
import { AuditLog } from '../../../shared/types/user'

// Données de démonstration pour le journal d'audit
const DEMO_AUDIT_LOGS: AuditLog[] = [
  {
    id: 1,
    userId: 1,
    action: 'USER_LOGIN',
    resource: 'auth',
    resourceId: '1',
    details: { ip: '*************', userAgent: 'Chrome/120.0' },
    success: true,
    timestamp: new Date('2024-01-16T10:30:00'),
    ipAddress: '*************',
    userAgent: 'Chrome/120.0'
  },
  {
    id: 2,
    userId: 4,
    action: 'USER_CREATED',
    resource: 'user',
    resourceId: '5',
    details: { username: 'nouveau.user', role: 'Utilisateur' },
    success: true,
    timestamp: new Date('2024-01-16T09:15:00'),
    ipAddress: '*************',
    userAgent: 'Firefox/121.0'
  },
  {
    id: 3,
    userId: 2,
    action: 'USER_UPDATED',
    resource: 'user',
    resourceId: '3',
    details: { field: 'role', oldValue: 'Utilisateur', newValue: 'Modérateur' },
    success: true,
    timestamp: new Date('2024-01-16T08:45:00'),
    ipAddress: '*************',
    userAgent: 'Safari/17.0'
  },
  {
    id: 4,
    userId: 1,
    action: 'SETTINGS_UPDATED',
    resource: 'settings',
    resourceId: 'general',
    details: { setting: 'theme', value: 'dark' },
    success: true,
    timestamp: new Date('2024-01-15T16:20:00'),
    ipAddress: '*************',
    userAgent: 'Chrome/120.0'
  },
  {
    id: 5,
    userId: 3,
    action: 'USER_LOGIN_FAILED',
    resource: 'auth',
    resourceId: '3',
    details: { reason: 'Invalid password', attempts: 3 },
    success: false,
    timestamp: new Date('2024-01-15T14:10:00'),
    ipAddress: '*************',
    userAgent: 'Edge/120.0',
    error: 'Mot de passe incorrect'
  }
]

interface AuditLogProps {
  userId?: number // Pour filtrer par utilisateur spécifique
}

export const AuditLogComponent: React.FC<AuditLogProps> = ({ userId }) => {
  const [logs, setLogs] = useState<AuditLog[]>([])
  const [filteredLogs, setFilteredLogs] = useState<AuditLog[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [actionFilter, setActionFilter] = useState<string>('')
  const [successFilter, setSuccessFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  useEffect(() => {
    loadAuditLogs()
  }, [userId])

  useEffect(() => {
    applyFilters()
  }, [logs, searchQuery, actionFilter, successFilter])

  const loadAuditLogs = async () => {
    try {
      setLoading(true)
      // Simulation du chargement depuis une API
      let data = [...DEMO_AUDIT_LOGS]
      
      if (userId) {
        data = data.filter(log => log.userId === userId)
      }
      
      // Trier par date décroissante
      data.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      
      setLogs(data)
    } catch (error) {
      console.error('Erreur lors du chargement du journal d\'audit:', error)
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...logs]

    // Filtre par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(log =>
        log.action.toLowerCase().includes(query) ||
        log.resource.toLowerCase().includes(query) ||
        (log.resourceId && log.resourceId.toLowerCase().includes(query)) ||
        (log.ipAddress && log.ipAddress.includes(query))
      )
    }

    // Filtre par action
    if (actionFilter) {
      filtered = filtered.filter(log => log.action === actionFilter)
    }

    // Filtre par succès/échec
    if (successFilter) {
      const isSuccess = successFilter === 'success'
      filtered = filtered.filter(log => log.success === isSuccess)
    }

    setFilteredLogs(filtered)
    setCurrentPage(1) // Reset pagination
  }

  const getActionColor = (action: string) => {
    if (action.includes('LOGIN')) return 'blue'
    if (action.includes('CREATE')) return 'green'
    if (action.includes('UPDATE')) return 'yellow'
    if (action.includes('DELETE')) return 'red'
    if (action.includes('FAILED')) return 'red'
    return 'gray'
  }

  const getSuccessColor = (success: boolean) => {
    return success ? 'green' : 'red'
  }

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleString('fr-FR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatDetails = (details: any) => {
    if (!details) return '-'
    
    if (typeof details === 'object') {
      return Object.entries(details)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ')
    }
    
    return String(details)
  }

  // Pagination
  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentLogs = filteredLogs.slice(startIndex, endIndex)

  // Actions uniques pour le filtre
  const uniqueActions = Array.from(new Set(logs.map(log => log.action)))

  return (
    <Stack gap="md">
      <Flex justify="space-between" align="center">
        <Title order={4}>
          Journal d'activité {userId && `(Utilisateur #${userId})`}
        </Title>
        <Button
          leftSection={<IconRefresh size={16} />}
          variant="light"
          onClick={loadAuditLogs}
          loading={loading}
        >
          Actualiser
        </Button>
      </Flex>

      {/* Filtres */}
      <Card withBorder padding="md">
        <Flex gap="md" wrap="wrap" align="end">
          <TextInput
            placeholder="Rechercher dans les logs..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ flex: 1, minWidth: 200 }}
          />
          
          <Select
            placeholder="Toutes les actions"
            data={[
              { value: '', label: 'Toutes les actions' },
              ...uniqueActions.map(action => ({ value: action, label: action }))
            ]}
            value={actionFilter}
            onChange={(value) => setActionFilter(value || '')}
            w={200}
          />
          
          <Select
            placeholder="Tous les résultats"
            data={[
              { value: '', label: 'Tous les résultats' },
              { value: 'success', label: 'Succès uniquement' },
              { value: 'failure', label: 'Échecs uniquement' }
            ]}
            value={successFilter}
            onChange={(value) => setSuccessFilter(value || '')}
            w={180}
          />
        </Flex>
      </Card>

      {/* Tableau des logs */}
      <Card withBorder padding={0}>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Date/Heure</Table.Th>
              <Table.Th>Utilisateur</Table.Th>
              <Table.Th>Action</Table.Th>
              <Table.Th>Ressource</Table.Th>
              <Table.Th>Détails</Table.Th>
              <Table.Th>Résultat</Table.Th>
              <Table.Th>IP</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {currentLogs.map((log) => (
              <Table.Tr key={log.id}>
                <Table.Td>
                  <Text size="sm">
                    {formatTimestamp(log.timestamp)}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <IconUser size={16} />
                    <Text size="sm">#{log.userId}</Text>
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Badge color={getActionColor(log.action)} size="sm">
                    {log.action}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">
                    {log.resource}
                    {log.resourceId && ` (${log.resourceId})`}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm" lineClamp={2} maw={200}>
                    {formatDetails(log.details)}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Badge color={getSuccessColor(log.success)} size="sm">
                    {log.success ? 'Succès' : 'Échec'}
                  </Badge>
                  {log.error && (
                    <Text size="xs" c="red" mt="xs">
                      {log.error}
                    </Text>
                  )}
                </Table.Td>
                <Table.Td>
                  <Text size="sm" c="dimmed">
                    {log.ipAddress || '-'}
                  </Text>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {currentLogs.length === 0 && (
          <Box p="xl" ta="center">
            <Text c="dimmed">
              {loading ? 'Chargement...' : 'Aucun log trouvé'}
            </Text>
          </Box>
        )}
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Flex justify="center">
          <Pagination
            value={currentPage}
            onChange={setCurrentPage}
            total={totalPages}
            size="sm"
          />
        </Flex>
      )}

      {/* Informations */}
      <Text size="sm" c="dimmed" ta="center">
        Affichage de {startIndex + 1}-{Math.min(endIndex, filteredLogs.length)} sur {filteredLogs.length} entrées
      </Text>
    </Stack>
  )
}
