import React from 'react'
import {
  Button,
  Flex,
  Group,
  Menu,
  Select,
  TextInput
} from '@mantine/core'
import {
  IconFilter,
  IconSearch,
  IconSortAscending,
  IconSortDescending,
  IconX
} from '@tabler/icons-react'
import { UserFilters as UserFiltersType, UserRole, UserStatus } from '../../../shared/types/user'
import { useRoles } from '../../hooks/useRoles'

interface UserFiltersProps {
  filters: UserFiltersType
  onFiltersChange: (filters: UserFiltersType) => void
  totalUsers: number
  displayedUsers: number
}

export const UserFilters: React.FC<UserFiltersProps> = ({
  filters,
  onFiltersChange,
  totalUsers,
  displayedUsers
}) => {
  const { getRolesForSelect } = useRoles()
  const handleSearchChange = (value: string) => {
    onFiltersChange({
      ...filters,
      search: value || undefined,
      offset: 0 // Reset pagination
    })
  }

  const handleRoleChange = (value: string | null) => {
    onFiltersChange({
      ...filters,
      role: value as UserRole || undefined,
      offset: 0
    })
  }

  const handleStatusChange = (value: string | null) => {
    onFiltersChange({
      ...filters,
      status: value as UserStatus || undefined,
      offset: 0
    })
  }

  const handleActiveChange = (value: string | null) => {
    let isActive: boolean | undefined
    if (value === 'active') isActive = true
    else if (value === 'inactive') isActive = false
    else isActive = undefined

    onFiltersChange({
      ...filters,
      isActive,
      offset: 0
    })
  }

  const handleSortChange = (sortBy: string, sortOrder: 'asc' | 'desc') => {
    onFiltersChange({
      ...filters,
      sortBy: sortBy as any,
      sortOrder,
      offset: 0
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      limit: filters.limit,
      offset: 0
    })
  }

  const hasActiveFilters = !!(
    filters.search ||
    filters.role ||
    filters.status ||
    filters.isActive !== undefined
  )

  const getSortIcon = () => {
    return filters.sortOrder === 'desc' ? 
      <IconSortDescending size={16} /> : 
      <IconSortAscending size={16} />
  }

  const getSortLabel = () => {
    const sortLabels = {
      username: 'Nom d\'utilisateur',
      displayName: 'Nom d\'affichage',
      email: 'Email',
      createdAt: 'Date de création',
      lastLogin: 'Dernière connexion'
    }
    const label = sortLabels[filters.sortBy as keyof typeof sortLabels] || 'Nom d\'affichage'
    const order = filters.sortOrder === 'desc' ? 'décroissant' : 'croissant'
    return `${label} (${order})`
  }

  return (
    <Flex gap="md" align="center" wrap="wrap">
      {/* Barre de recherche */}
      <TextInput
        placeholder="Rechercher par nom, email ou username..."
        leftSection={<IconSearch size={16} />}
        value={filters.search || ''}
        onChange={(e) => handleSearchChange(e.target.value)}
        style={{ flex: 1, minWidth: 250 }}
      />

      {/* Filtre par rôle */}
      <Select
        placeholder="Tous les rôles"
        data={[
          { value: '', label: 'Tous les rôles' },
          ...getRolesForSelect()
        ]}
        value={filters.role || ''}
        onChange={handleRoleChange}
        clearable
        w={150}
      />

      {/* Filtre par statut */}
      <Select
        placeholder="Tous les statuts"
        data={[
          { value: '', label: 'Tous les statuts' },
          { value: UserStatus.ACTIVE, label: 'Actif' },
          { value: UserStatus.INACTIVE, label: 'Inactif' },
          { value: UserStatus.SUSPENDED, label: 'Suspendu' },
          { value: UserStatus.PENDING, label: 'En attente' }
        ]}
        value={filters.status || ''}
        onChange={handleStatusChange}
        clearable
        w={150}
      />

      {/* Filtre actif/inactif */}
      <Select
        placeholder="État du compte"
        data={[
          { value: '', label: 'Tous les comptes' },
          { value: 'active', label: 'Comptes actifs' },
          { value: 'inactive', label: 'Comptes inactifs' }
        ]}
        value={
          filters.isActive === true ? 'active' :
          filters.isActive === false ? 'inactive' : ''
        }
        onChange={handleActiveChange}
        clearable
        w={150}
      />

      {/* Menu de tri */}
      <Menu>
        <Menu.Target>
          <Button variant="light" leftSection={getSortIcon()}>
            Trier
          </Button>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Label>Trier par</Menu.Label>
          <Menu.Item onClick={() => handleSortChange('displayName', 'asc')}>
            Nom d'affichage (A-Z)
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('displayName', 'desc')}>
            Nom d'affichage (Z-A)
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('username', 'asc')}>
            Nom d'utilisateur (A-Z)
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('username', 'desc')}>
            Nom d'utilisateur (Z-A)
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('email', 'asc')}>
            Email (A-Z)
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('email', 'desc')}>
            Email (Z-A)
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('createdAt', 'desc')}>
            Plus récents
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('createdAt', 'asc')}>
            Plus anciens
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('lastLogin', 'desc')}>
            Dernière connexion (récente)
          </Menu.Item>
          <Menu.Item onClick={() => handleSortChange('lastLogin', 'asc')}>
            Dernière connexion (ancienne)
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>

      {/* Bouton pour effacer les filtres */}
      {hasActiveFilters && (
        <Button
          variant="subtle"
          color="gray"
          leftSection={<IconX size={16} />}
          onClick={clearFilters}
        >
          Effacer
        </Button>
      )}

      {/* Compteur de résultats */}
      <Group gap="xs" style={{ marginLeft: 'auto' }}>
        <span style={{ fontSize: '0.875rem', color: 'var(--mantine-color-dimmed)' }}>
          {displayedUsers} / {totalUsers} utilisateurs
        </span>
      </Group>
    </Flex>
  )
}
