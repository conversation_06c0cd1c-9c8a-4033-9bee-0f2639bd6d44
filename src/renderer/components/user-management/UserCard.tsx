import React from 'react'
import {
  Ava<PERSON>,
  Badge,
  Card,
  Flex,
  Group,
  Text
} from '@mantine/core'
import { PublicUser, UserRole } from '../../../shared/types/user'

interface UserCardProps {
  user: PublicUser
  onEdit?: (user: PublicUser) => void
  onDelete?: (user: PublicUser) => void
  onToggleStatus?: (user: PublicUser) => void
  onChangeRole?: (user: PublicUser) => void
  isSelected?: boolean
}

export const UserCard: React.FC<UserCardProps> = ({
  user,
  onEdit,
  onDelete,
  onToggleStatus,
  onChangeRole,
  isSelected = false
}) => {
  const handleCardClick = () => {
    if (onEdit) {
      onEdit(user)
    }
  }
  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN: return 'red'
      case UserRole.MODERATOR: return 'blue'
      case UserRole.USER: return 'green'
      case UserRole.GUEST: return 'gray'
      default: return 'gray'
    }
  }

  const getUserInitial = (user: PublicUser) => {
    return (user.displayName || user.username || 'U').charAt(0).toUpperCase()
  }

  const formatLastLogin = (date?: Date) => {
    if (!date) return 'Jamais connecté'
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return 'Aujourd\'hui'
    if (diffDays === 1) return 'Hier'
    if (diffDays < 7) return `Il y a ${diffDays} jours`
    return date.toLocaleDateString('fr-FR')
  }

  return (
    <Card
      padding="md"
      radius="md"
      withBorder
      onClick={handleCardClick}
      style={{
        backgroundColor: 'var(--mantine-color-dark-7)',
        borderColor: isSelected
          ? 'var(--mantine-color-blue-5)'
          : 'var(--mantine-color-dark-5)',
        borderWidth: isSelected ? '2px' : '1px',
        boxShadow: isSelected
          ? '0 0 0 1px var(--mantine-color-blue-5)'
          : undefined,
        cursor: onEdit ? 'pointer' : 'default',
        transition: 'all 0.2s ease',
      }}
      __hover={{
        transform: onEdit ? 'translateY(-2px)' : undefined,
        boxShadow: onEdit ? '0 4px 12px rgba(0, 0, 0, 0.15)' : undefined,
      }}
    >
      <Flex justify="space-between" align="flex-start" mb="sm">
        <Group gap="sm">
          <Avatar
            size={40}
            radius="sm"
            color={getRoleColor(user.role)}
            src={user.avatar}
          >
            {getUserInitial(user)}
          </Avatar>
          <div>
            <Text fw={600} size="sm" c="white">
              {user.displayName}
            </Text>
            <Text size="xs" c="dimmed">
              {user.email}
            </Text>
            <Text size="xs" c="dimmed">
              @{user.username}
            </Text>
          </div>
        </Group>
        
        {/* Indicateur de statut en ligne */}
        <div
          style={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            backgroundColor: user.isActive ? '#4CAF50' : '#757575',
            marginTop: 4,
          }}
          title={user.isActive ? 'Actif' : 'Inactif'}
        />
      </Flex>

      <Flex justify="space-between" align="center" mb="sm">
        <Badge 
          size="sm" 
          color={getRoleColor(user.role)}
          variant="light"
        >
          {user.role}
        </Badge>
        
        <Text size="xs" c="dimmed">
          {user.language}
        </Text>
      </Flex>

      <Flex justify="space-between" align="center" mb="sm">
        <Text size="xs" c="dimmed">
          Dernière connexion: {formatLastLogin(user.lastLogin)}
        </Text>
        <Text size="xs" c="dimmed">
          {user.loginCount} connexions
        </Text>
      </Flex>


    </Card>
  )
}
