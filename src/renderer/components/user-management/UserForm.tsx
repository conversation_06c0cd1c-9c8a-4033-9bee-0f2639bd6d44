import React, { useState, useEffect } from 'react'
import {
  Button,
  Group,
  Modal,
  Select,
  Stack,
  Switch,
  TextInput,
  Title,
  Text,
  Box
} from '@mantine/core'
import { useForm } from '@mantine/form'
import { notifications } from '@mantine/notifications'
import { I<PERSON><PERSON>he<PERSON>, IconX } from '@tabler/icons-react'
import { CreateUserRequest, PublicUser, UpdateUserRequest, UserRole } from '../../../shared/types/user'
import { userManagementService } from '@/services/UserManagementService'
import { useRoles } from '../../hooks/useRoles'

interface UserFormProps {
  opened: boolean
  onClose: () => void
  user?: PublicUser // Si fourni, mode édition
  onSuccess?: (user: PublicUser) => void
}

export const UserForm: React.FC<UserFormProps> = ({
  opened,
  onClose,
  user,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false)
  const isEditing = !!user && !!user.id
  const { getRolesForSelect } = useRoles()

  const form = useForm({
    initialValues: {
      username: user?.username || '',
      email: user?.email || '',
      displayName: user?.displayName || '',
      role: user?.role || 'user',
      language: user?.language || 'fr',
      isActive: user?.isActive ?? true,
      password: '',
      confirmPassword: ''
    },
    validate: {
      username: (value) => {
        if (!value) return 'Le nom d\'utilisateur est requis'
        if (value.length < 3) return 'Le nom d\'utilisateur doit contenir au moins 3 caractères'
        if (!/^[a-zA-Z0-9._-]+$/.test(value)) return 'Le nom d\'utilisateur ne peut contenir que des lettres, chiffres, points, tirets et underscores'
        return null
      },
      email: (value) => {
        if (!value) return 'L\'email est requis'
        if (!/^\S+@\S+\.\S+$/.test(value)) return 'Format d\'email invalide'
        return null
      },
      displayName: (value) => {
        if (!value) return 'Le nom d\'affichage est requis'
        if (value.length < 2) return 'Le nom d\'affichage doit contenir au moins 2 caractères'
        return null
      },
      password: (value) => {
        if (!isEditing && !value) return 'Le mot de passe est requis'
        if (value && value.length < 6) return 'Le mot de passe doit contenir au moins 6 caractères'
        return null
      },
      confirmPassword: (value, values) => {
        if (!isEditing && value !== values.password) return 'Les mots de passe ne correspondent pas'
        if (isEditing && values.password && value !== values.password) return 'Les mots de passe ne correspondent pas'
        return null
      }
    }
  })

  // Réinitialiser le formulaire quand le modal s'ouvre/se ferme
  useEffect(() => {
    if (opened) {
      if (user && user.id) {
        // Mode édition - charger les données de l'utilisateur
        form.setValues({
          username: user.username || '',
          email: user.email || '',
          displayName: user.displayName || '',
          role: user.role || 'user',
          language: user.language || 'fr',
          isActive: user.isActive ?? true,
          password: '',
          confirmPassword: ''
        })
      } else {
        // Mode création - valeurs par défaut
        form.setValues({
          username: '',
          email: '',
          displayName: '',
          role: 'user',
          language: 'fr',
          isActive: true,
          password: '',
          confirmPassword: ''
        })
      }
    }
    console.log('🔍 Modal ouvert:', opened, 'Mode édition:', isEditing, 'Utilisateur:', user)
  }, [opened, user])

  // Debug des valeurs du formulaire
  useEffect(() => {
    console.log('🔍 Valeurs du formulaire UserForm:', form.values)
    console.log('🔍 Mode édition:', isEditing)
  }, [form.values, isEditing])

  const handleSubmit = async (values: typeof form.values) => {
    try {
      setLoading(true)
      console.log('🚀 Début de la soumission du formulaire:', { isEditing, values })

      if (isEditing && user) {
        // Mode édition
        console.log('📝 Mode édition pour utilisateur:', user.id)
        const updateData: UpdateUserRequest = {
          displayName: values.displayName,
          email: values.email,
          role: values.role as any,
          language: values.language,
          isActive: values.isActive
        }

        console.log('📤 Données de mise à jour:', updateData)
        const updatedUser = await userManagementService.updateUser(user.id, updateData)
        if (updatedUser) {
          console.log('✅ Utilisateur mis à jour avec succès:', updatedUser)
          notifications.show({
            title: 'Succès',
            message: 'Utilisateur mis à jour avec succès',
            color: 'green',
            icon: <IconCheck size={16} />
          })
          onSuccess?.(updatedUser)
          onClose()
        }
      } else {
        // Mode création
        console.log('➕ Mode création d\'utilisateur')
        const createData: CreateUserRequest = {
          username: values.username,
          email: values.email,
          displayName: values.displayName,
          password: values.password,
          role: values.role as any,
          language: values.language
        }

        console.log('📤 Données de création:', { ...createData, password: '***' })
        const result = await userManagementService.createUser(createData)
        const newUser = result.user
        console.log('✅ Utilisateur créé avec succès:', newUser)
        notifications.show({
          title: 'Succès',
          message: 'Utilisateur créé avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
        onSuccess?.(newUser)
        onClose()
        form.reset()
      }
    } catch (error) {
      console.error('❌ Erreur lors de la soumission:', error)
      notifications.show({
        title: 'Erreur',
        message: error instanceof Error ? error.message : 'Une erreur est survenue',
        color: 'red',
        icon: <IconX size={16} />
      })
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    // Réinitialiser complètement le formulaire
    form.reset()
    form.clearErrors()
    setLoading(false)
    onClose()
  }

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={
        <Title order={4}>
          {isEditing ? 'Modifier l\'utilisateur' : 'Nouvel utilisateur'}
        </Title>
      }
      size="md"
      closeOnClickOutside={false}
      trapFocus={false}
      lockScroll={false}
      withinPortal={true}
      zIndex={1000}
    >
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Nom d'utilisateur"
            placeholder="john.doe"
            required
            disabled={isEditing} // Ne pas permettre de changer le username
            {...form.getInputProps('username')}
          />

          <TextInput
            label="Email"
            placeholder="<EMAIL>"
            required
            type="email"
            {...form.getInputProps('email')}
          />

          <TextInput
            label="Nom d'affichage"
            placeholder="John Doe"
            required
            {...form.getInputProps('displayName')}
          />

          <Select
            label="Rôle"
            placeholder="Sélectionner un rôle"
            required
            data={getRolesForSelect()}
            {...form.getInputProps('role')}
            comboboxProps={{
              withinPortal: true,
              zIndex: 10000,
              position: 'bottom-start',
              offset: 5
            }}
          />

          <Select
            label="Langue"
            placeholder="Sélectionner une langue"
            required
            data={[
              { value: 'fr', label: '🇫🇷 Français' },
              { value: 'en', label: '🇺🇸 English (US)' },
              { value: 'es', label: '🇪🇸 Español' },
              { value: 'de', label: '🇩🇪 Deutsch' },
              { value: 'it', label: '🇮🇹 Italiano' },
              { value: 'pt', label: '🇵🇹 Português' },
              { value: 'ar', label: '🇸🇦 العربية' }
            ]}
            {...form.getInputProps('language')}
            comboboxProps={{
              withinPortal: true,
              zIndex: 10000,
              position: 'bottom-start',
              offset: 5
            }}
          />

          {isEditing && (
            <Switch
              label="Compte actif"
              description="Désactiver empêche l'utilisateur de se connecter"
              {...form.getInputProps('isActive', { type: 'checkbox' })}
            />
          )}

          {!isEditing && (
            <>
              <TextInput
                label="Mot de passe"
                placeholder="••••••••"
                required
                type="password"
                {...form.getInputProps('password')}
              />

              <TextInput
                label="Confirmer le mot de passe"
                placeholder="••••••••"
                required
                type="password"
                {...form.getInputProps('confirmPassword')}
              />
            </>
          )}

          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={handleClose} disabled={loading}>
              Annuler
            </Button>
            <Button type="submit" loading={loading}>
              {isEditing ? 'Mettre à jour' : 'Créer'}
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  )
}
