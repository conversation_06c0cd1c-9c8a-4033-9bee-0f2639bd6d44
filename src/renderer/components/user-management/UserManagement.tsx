import React, { useEffect, useState } from 'react'
import {
  Box,
  Button,
  Flex,
  Stack,
  Text,
  Title,
  Transition
} from '@mantine/core'
import { useIsSmallScreen } from '@/hooks/useScreenChange'
import {
  IconPlus,
  IconUser
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { userManagementService } from '@/services/UserManagementService'
import { PublicUser, UserFilters as UserFiltersType } from '../../../shared/types/user'
import { UserCard } from './UserCard'
import { UserFormPanel } from './UserFormPanel'
import { UserFilters } from './UserFilters'

export const UserManagement: React.FC = () => {
  const { t } = useTranslation()
  const isSmallScreen = useIsSmallScreen()
  const [users, setUsers] = useState<PublicUser[]>([])
  const [loading, setLoading] = useState(true)
  const [total, setTotal] = useState(0)
  const [filters, setFilters] = useState<UserFiltersType>({
    limit: 50,
    offset: 0,
    sortBy: 'displayName',
    sortOrder: 'asc'
  })
  const [userFormOpened, setUserFormOpened] = useState(false)
  const [editingUser, setEditingUser] = useState<PublicUser | undefined>()



  // Charger les utilisateurs
  useEffect(() => {
    loadUsers()
  }, [filters])

  const loadUsers = async () => {
    try {
      setLoading(true)

      // S'assurer que le service est initialisé
      if (!userManagementService.isInitialized()) {
        console.log('🔧 Initialisation du service de gestion des utilisateurs...')
        await userManagementService.initialize()
      }

      const response = await userManagementService.listUsers(filters)
      setUsers(response.users)
      setTotal(response.total)
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUserSuccess = () => {
    loadUsers()
    setEditingUser(undefined)
    setUserFormOpened(false)
  }

  const handleEditUser = (user: PublicUser) => {
    setEditingUser(user)
    setUserFormOpened(true)
  }

  const handleCancelForm = () => {
    setUserFormOpened(false)
    setEditingUser(undefined)
  }

  const handleDeleteUser = async (user: PublicUser) => {
    try {
      await userManagementService.deleteUser(user.id)
      loadUsers()
    } catch (error) {
      console.error('Erreur lors de la suppression:', error)
    }
  }

  const handleToggleStatus = async (user: PublicUser) => {
    try {
      await userManagementService.updateUser(user.id, {
        isActive: !user.isActive
      })
      loadUsers()
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error)
    }
  }

  return (
    <Box pos="relative" style={{ minHeight: '500px' }}>
      {/* Affichage de la liste des utilisateurs */}
      <Transition
        mounted={!userFormOpened}
        transition="fade"
        duration={250}
        timingFunction="ease-in-out"
      >
        {(styles) => (
          <Box style={styles}>
            <Stack gap="xl">
              {/* Header avec titre et bouton nouveau */}
              <Flex justify="space-between" align="center">
                <Title order={4}>Gestion des Utilisateurs</Title>
                <Button
                  leftSection={<IconPlus size={16} />}
                  variant="filled"
                  color="blue"
                  size="sm"
                  onClick={() => {
                    setEditingUser(undefined)
                    setUserFormOpened(true)
                  }}
                >
                  Nouvel utilisateur
                </Button>
              </Flex>

              {/* Filtres */}
              <UserFilters
                filters={filters}
                onFiltersChange={setFilters}
                totalUsers={total}
                displayedUsers={users.length}
              />

              {/* Liste des utilisateurs en grille */}
              <Box>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: isSmallScreen
                    ? '1fr'
                    : 'repeat(auto-fill, minmax(320px, 1fr))',
                  gap: '16px'
                }}>
                  {users.map((user) => (
                    <UserCard
                      key={user.id}
                      user={user}
                      onEdit={handleEditUser}
                      onDelete={handleDeleteUser}
                      onToggleStatus={handleToggleStatus}
                      isSelected={editingUser?.id === user.id}
                    />
                  ))}
                </div>
              </Box>

              {/* Message si aucun utilisateur trouvé */}
              {!loading && users.length === 0 && (
                <Box ta="center" py="xl">
                  <IconUser size={48} color="var(--mantine-color-dimmed)" />
                  <Text c="dimmed" mt="md">
                    Aucun utilisateur trouvé
                  </Text>
                </Box>
              )}

              {/* Indicateur de chargement */}
              {loading && (
                <Box ta="center" py="xl">
                  <Text c="dimmed">
                    Chargement des utilisateurs...
                  </Text>
                </Box>
              )}
            </Stack>
          </Box>
        )}
      </Transition>

      {/* Affichage du formulaire utilisateur */}
      <Transition
        mounted={userFormOpened}
        transition="fade"
        duration={250}
        timingFunction="ease-in-out"
      >
        {(styles) => (
          <Box
            style={{
              ...styles,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              minHeight: '500px',
              backgroundColor: 'var(--mantine-color-body)',
              zIndex: 1
            }}
          >
            <UserFormPanel
              user={editingUser}
              onSuccess={handleUserSuccess}
              onCancel={handleCancelForm}
            />
          </Box>
        )}
      </Transition>
    </Box>
  )
}
