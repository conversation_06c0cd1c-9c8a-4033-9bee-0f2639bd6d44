import React, { useEffect, useState } from 'react'
import { Box, Button, Center, Stack, Text, Title } from '@mantine/core'
import { IconLock, IconSettings } from '@tabler/icons-react'
import { useAtomValue } from 'jotai'
import { useNavigate, useRouterState } from '@tanstack/react-router'
import { currentUserAtom, isAuthenticatedAtom } from '../../stores/atoms/authAtoms'

interface SettingsProtectionProps {
  children: React.ReactNode
}

// Fonction pour vérifier les permissions basée sur l'ancien système de rôles
const checkUserPermission = (currentUser: any, requiredRole: 'admin' | 'user' | 'guest' = 'user') => {
  if (!currentUser) return false

  // Hiérarchie des rôles : admin > user > guest
  const roleHierarchy = { admin: 3, user: 2, guest: 1 }
  const userRoleLevel = roleHierarchy[currentUser.role as keyof typeof roleHierarchy] || 0
  const requiredRoleLevel = roleHierarchy[requiredRole]

  return userRoleLevel >= requiredRoleLevel
}

// Mapping des pages vers les permissions requises (simplifié)
const PAGE_PERMISSIONS = {
  '/settings/admin': {
    canAccess: (currentUser: any) => checkUserPermission(currentUser, 'admin') // Seulement admin
  },
  '/settings/provider': {
    canAccess: (currentUser: any) => checkUserPermission(currentUser, 'user')
  },
  '/settings/default-models': {
    canAccess: (currentUser: any) => checkUserPermission(currentUser, 'user')
  },
  '/settings/knowledge-base': {
    canAccess: (currentUser: any) => checkUserPermission(currentUser, 'user')
  },
  '/settings/general': {
    canAccess: (currentUser: any) => checkUserPermission(currentUser, 'user')
  },
  '/settings/chat': {
    canAccess: (currentUser: any) => true // Accessible à tous les utilisateurs connectés
  },
  '/settings/hotkeys': {
    canAccess: (currentUser: any) => true // Accessible à tous les utilisateurs connectés
  },
  '/settings/web-search': {
    canAccess: (currentUser: any) => checkUserPermission(currentUser, 'user')
  },
  '/settings/mcp': {
    canAccess: (currentUser: any) => checkUserPermission(currentUser, 'user')
  }
}

export const SettingsProtection: React.FC<SettingsProtectionProps> = ({ children }) => {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const currentUser = useAtomValue(currentUserAtom)
  const navigate = useNavigate()
  const routerState = useRouterState()
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAccess()
  }, [isAuthenticated, currentUser, routerState.location.pathname])

  const checkAccess = async () => {
    try {
      setLoading(true)

      // Vérifier l'authentification
      if (!isAuthenticated || !currentUser) {
        setHasAccess(false)
        setLoading(false)
        return
      }

      const currentPath = routerState.location.pathname
      const pagePermission = PAGE_PERMISSIONS[currentPath as keyof typeof PAGE_PERMISSIONS]

      if (pagePermission) {
        const canAccess = pagePermission.canAccess(currentUser)
        setHasAccess(canAccess)
      } else {
        // Si la page n'est pas dans la liste, autoriser l'accès par défaut
        setHasAccess(true)
      }
    } catch (error) {
      console.error('Erreur lors de la vérification des permissions:', error)
      setHasAccess(false)
    } finally {
      setLoading(false)
    }
  }

  // Affichage pendant le chargement
  if (loading) {
    return (
      <Center h="50vh">
        <Text c="dimmed">Vérification des permissions...</Text>
      </Center>
    )
  }

  // Redirection si pas authentifié
  if (!isAuthenticated) {
    return (
      <Center h="50vh">
        <Stack align="center" gap="md">
          <IconLock size={64} color="var(--mantine-color-orange-6)" />
          <Title order={2} c="orange">
            Connexion requise
          </Title>
          <Text c="dimmed" ta="center" maw={400}>
            Vous devez être connecté pour accéder aux paramètres.
          </Text>
          <Button
            variant="filled"
            onClick={() => navigate({ to: '/login', replace: true })}
          >
            Se connecter
          </Button>
        </Stack>
      </Center>
    )
  }

  // Affichage si pas d'accès
  if (!hasAccess) {
    return (
      <Center h="50vh">
        <Stack align="center" gap="md">
          <IconSettings size={64} color="var(--mantine-color-red-6)" />
          <Title order={2} c="red">
            Accès restreint
          </Title>
          <Text c="dimmed" ta="center" maw={400}>
            Vous n'avez pas les permissions nécessaires pour accéder à cette section des paramètres.
          </Text>
          <Button
            variant="light"
            onClick={() => navigate({ to: '/settings', replace: true })}
          >
            Retour aux paramètres
          </Button>
        </Stack>
      </Center>
    )
  }

  // Affichage du contenu protégé
  return <>{children}</>
}

// Hook pour vérifier l'accès à une page spécifique
export const usePageAccess = (page: string) => {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const currentUser = useAtomValue(currentUserAtom)

  useEffect(() => {
    const checkAccess = async () => {
      if (!isAuthenticated || !currentUser) {
        setHasAccess(false)
        return
      }

      const pagePermission = PAGE_PERMISSIONS[page as keyof typeof PAGE_PERMISSIONS]
      if (pagePermission) {
        try {
          const canAccess = pagePermission.canAccess(currentUser)
          setHasAccess(canAccess)
        } catch (error) {
          console.error('Erreur lors de la vérification des permissions:', error)
          setHasAccess(false)
        }
      } else {
        setHasAccess(true)
      }
    }

    checkAccess()
  }, [page, isAuthenticated, currentUser])

  return hasAccess
}

// Composant pour masquer des éléments selon les permissions
interface ConditionalRenderProps {
  children: React.ReactNode
  condition: () => Promise<boolean>
  fallback?: React.ReactNode
}

export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  children,
  condition,
  fallback
}) => {
  const [shouldRender, setShouldRender] = useState<boolean | null>(null)

  useEffect(() => {
    const checkCondition = async () => {
      try {
        const result = await condition()
        setShouldRender(result)
      } catch (error) {
        console.error('Erreur lors de la vérification de condition:', error)
        setShouldRender(false)
      }
    }

    checkCondition()
  }, [condition])

  if (shouldRender === null) {
    return null // Ou un loader
  }

  if (!shouldRender) {
    return fallback ? <>{fallback}</> : null
  }

  return <>{children}</>
}

// Composant pour les boutons conditionnels
interface ConditionalButtonProps {
  children: React.ReactNode
  requireAdmin?: boolean
  requireModerator?: boolean
  customCondition?: () => Promise<boolean>
}

export const ConditionalButton: React.FC<ConditionalButtonProps> = ({
  children,
  requireAdmin = false,
  requireModerator = false,
  customCondition
}) => {
  const currentUser = useAtomValue(currentUserAtom)

  const getCondition = () => {
    if (customCondition) {
      return customCondition
    }
    if (requireAdmin) {
      return async () => checkUserPermission(currentUser, 'admin')
    }
    if (requireModerator) {
      return async () => checkUserPermission(currentUser, 'user') // Modérateur = user dans l'ancien système
    }
    return () => Promise.resolve(true)
  }

  return (
    <ConditionalRender condition={getCondition()}>
      {children}
    </ConditionalRender>
  )
}
