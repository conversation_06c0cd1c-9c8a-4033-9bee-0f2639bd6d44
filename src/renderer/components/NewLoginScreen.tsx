// Nouveau composant de connexion utilisant le système Dexie.js
// Remplace progressivement l'ancien LoginScreen

import React, { useState, useEffect } from 'react'
import { useAtom } from 'jotai'
import {
  newAuth<PERSON>tom,
  loginAtom,
  authLoading<PERSON>tom,
  authErrorAtom,
  authInitializedAtom
} from '../stores/atoms/newAuthAtoms'
import { splashCompletedAtom } from '../stores/atoms/newAuthAtoms'
import { LoginRequest } from '../../shared/types/database'
import { DataTecLogo } from './DataTecLogo'

interface NewLoginScreenProps {
  onLoginSuccess?: () => void
  showLogo?: boolean
  allowGuestAccess?: boolean
}

export const NewLoginScreen: React.FC<NewLoginScreenProps> = ({
  onLoginSuccess,
  showLogo = true,
  allowGuestAccess = false
}) => {
  const [authState] = useAtom(newAuth<PERSON>tom)
  const [, login] = useAtom(loginAtom)
  const [isLoading] = useAtom(authLoadingAtom)
  const [error] = useAtom(authErrorAtom)
  const [initialized] = useAtom(authInitializedAtom)
  const [, setSplashCompleted] = useAtom(splashCompletedAtom)

  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  })

  const [showPassword, setShowPassword] = useState(false)
  const [localError, setLocalError] = useState<string | null>(null)

  // Rediriger si déjà connecté
  useEffect(() => {
    if (authState.isAuthenticated && authState.user) {
      setSplashCompleted(true)
      onLoginSuccess?.()
    }
  }, [authState.isAuthenticated, authState.user, onLoginSuccess, setSplashCompleted])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLocalError(null)

    if (!formData.username.trim()) {
      setLocalError('Nom d\'utilisateur requis')
      return
    }

    if (!formData.password.trim()) {
      setLocalError('Mot de passe requis')
      return
    }

    const credentials: LoginRequest = {
      username: formData.username.trim(),
      password: formData.password,
      rememberMe: formData.rememberMe
    }

    try {
      const result = await login(credentials)
      
      if (result.success) {
        // La redirection sera gérée par l'useEffect
        console.log('✅ Connexion réussie')
      } else {
        setLocalError(result.error || 'Erreur de connexion')
      }
    } catch (error) {
      setLocalError(`Erreur: ${error}`)
    }
  }

  const handleGuestLogin = async () => {
    if (!allowGuestAccess) return

    const guestCredentials: LoginRequest = {
      username: 'guest',
      password: 'guest123',
      rememberMe: false
    }

    try {
      await login(guestCredentials)
    } catch (error) {
      setLocalError('Impossible de se connecter en tant qu\'invité')
    }
  }

  const handleInputChange = (field: keyof typeof formData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (localError) setLocalError(null)
  }

  // Affichage de chargement si pas encore initialisé
  if (!initialized) {
    return (
      <div className="login-loading">
        <div className="loading-spinner">🔄</div>
        <p>Initialisation du système d'authentification...</p>
      </div>
    )
  }

  const displayError = localError || error

  return (
    <div className="new-login-screen">
      <div className="login-container">
        {/* Logo */}
        {showLogo && (
          <div className="login-logo">
            <div className="logo-icon">
              {/* Logo DataTec uniforme - utilise le composant réutilisable */}
              <DataTecLogo size={64} variant="gradient" />
            </div>
            <h1>DataTec Workspace</h1>
            <p>Système de gestion des données</p>
          </div>
        )}

        {/* Formulaire de connexion */}
        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username">Nom d'utilisateur</label>
            <input
              id="username"
              type="text"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder="Entrez votre nom d'utilisateur"
              disabled={isLoading}
              autoComplete="username"
              autoFocus
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Mot de passe</label>
            <div className="password-input">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="Entrez votre mot de passe"
                disabled={isLoading}
                autoComplete="current-password"
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={formData.rememberMe}
                onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                disabled={isLoading}
              />
              <span>Se souvenir de moi</span>
            </label>
          </div>

          {/* Erreur */}
          {displayError && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {displayError}
            </div>
          )}

          {/* Boutons */}
          <div className="form-actions">
            <button
              type="submit"
              className="login-button primary"
              disabled={isLoading || !formData.username.trim() || !formData.password.trim()}
            >
              {isLoading ? (
                <>
                  <span className="loading-spinner">🔄</span>
                  Connexion...
                </>
              ) : (
                'Se connecter'
              )}
            </button>

            {allowGuestAccess && (
              <button
                type="button"
                className="login-button secondary"
                onClick={handleGuestLogin}
                disabled={isLoading}
              >
                Accès invité
              </button>
            )}
          </div>
        </form>

        {/* Informations par défaut */}
        <div className="login-info">
          <details>
            <summary>Comptes par défaut</summary>
            <div className="default-accounts">
              <div className="account-info">
                <strong>Administrateur:</strong>
                <br />
                Utilisateur: <code>admin</code>
                <br />
                Mot de passe: <code>admin123</code>
              </div>
              <div className="account-info">
                <strong>Utilisateur standard:</strong>
                <br />
                Utilisateur: <code>user</code>
                <br />
                Mot de passe: <code>user123</code>
              </div>
            </div>
          </details>
        </div>
      </div>

      <style jsx>{`
        .new-login-screen {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .login-container {
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          padding: 40px;
          width: 100%;
          max-width: 400px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .login-logo {
          text-align: center;
          margin-bottom: 30px;
        }

        .logo-icon {
          margin-bottom: 16px;
        }

        .login-logo h1 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 24px;
          font-weight: 600;
        }

        .login-logo p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 6px;
          color: #333;
          font-weight: 500;
          font-size: 14px;
        }

        .form-group input {
          width: 100%;
          padding: 12px;
          border: 2px solid #e1e5e9;
          border-radius: 8px;
          font-size: 16px;
          transition: border-color 0.2s;
          box-sizing: border-box;
        }

        .form-group input:focus {
          outline: none;
          border-color: #667eea;
        }

        .form-group input:disabled {
          background-color: #f5f5f5;
          cursor: not-allowed;
        }

        .password-input {
          position: relative;
        }

        .password-toggle {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          cursor: pointer;
          font-size: 16px;
          opacity: 0.6;
        }

        .password-toggle:hover {
          opacity: 1;
        }

        .checkbox-group {
          margin-bottom: 24px;
        }

        .checkbox-label {
          display: flex;
          align-items: center;
          cursor: pointer;
          font-size: 14px;
          color: #666;
        }

        .checkbox-label input {
          margin-right: 8px;
          width: auto;
        }

        .error-message {
          background: #fee;
          border: 1px solid #fcc;
          border-radius: 6px;
          padding: 12px;
          margin-bottom: 20px;
          color: #c33;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .form-actions {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .login-button {
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        }

        .login-button.primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        .login-button.primary:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .login-button.secondary {
          background: transparent;
          color: #667eea;
          border: 2px solid #667eea;
        }

        .login-button.secondary:hover:not(:disabled) {
          background: #667eea;
          color: white;
        }

        .login-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
        }

        .loading-spinner {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .login-info {
          margin-top: 24px;
          text-align: center;
        }

        .login-info details {
          font-size: 12px;
          color: #666;
        }

        .login-info summary {
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          transition: background-color 0.2s;
        }

        .login-info summary:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .default-accounts {
          margin-top: 12px;
          text-align: left;
          background: rgba(0, 0, 0, 0.02);
          padding: 12px;
          border-radius: 6px;
        }

        .account-info {
          margin-bottom: 12px;
        }

        .account-info:last-child {
          margin-bottom: 0;
        }

        .account-info code {
          background: rgba(0, 0, 0, 0.1);
          padding: 2px 4px;
          border-radius: 3px;
          font-family: monospace;
        }

        .login-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          color: white;
          font-size: 18px;
        }

        .login-loading .loading-spinner {
          font-size: 48px;
          margin-bottom: 20px;
        }
      `}</style>
    </div>
  )
}

export default NewLoginScreen
