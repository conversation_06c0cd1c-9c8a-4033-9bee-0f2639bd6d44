import React from 'react'
import { Box } from '@mui/material'
import { useAtomValue } from 'jotai'
import { isAuthenticatedAtom } from '@/stores/atoms'
import UserMenu from './UserMenu'
import useNeedRoomForWinControls from '../hooks/useNeedRoomForWinControls'

/**
 * Avatar utilisateur en position fixe à l'extrême droite de l'écran
 * Affiché sur toutes les interfaces du système
 */
export default function FixedUserAvatar() {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const { needRoomForWindowsWindowControls } = useNeedRoomForWinControls()

  if (!isAuthenticated) {
    return null
  }

  return (
    <Box
      sx={{
        position: 'fixed',
        // ✅ Déplacement de 9px vers le haut : de 12px à 3px
        top: '3px', // Déplacé de 9px vers le haut pour un alignement parfait
        right: needRoomForWindowsWindowControls ? '152px' : '16px',
        zIndex: 9998, // Juste en dessous des menus (9999)
        transition: 'right 0.3s ease',
        display: 'flex',
        alignItems: 'center',
        height: '48px', // Même hauteur que le title-bar (h-12 = 48px)
        justifyContent: 'center',
      }}
    >
      <UserMenu />
    </Box>
  )
}
