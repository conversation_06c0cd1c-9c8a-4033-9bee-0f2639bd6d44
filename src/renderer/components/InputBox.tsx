import NiceModal from '@ebay/nice-modal-react'
import { ActionIcon, Box, Button, Flex, Menu, Stack, Text, Textarea, Tooltip } from '@mantine/core'
import { useViewportSize } from '@mantine/hooks'
import {
  IconAdjustmentsHorizontal,
  IconArrowBackUp,
  IconArrowUp,
  IconCirclePlus,
  IconCode,
  IconDatabase,
  IconFilePencil,
  IconFolder,
  IconHammer,
  IconLink,
  IconPhoto,
  IconPlayerStopFilled,
  IconSearch,
  IconVideo,
  IconWorld,
} from '@tabler/icons-react'
import { useAtom, useAtomValue } from 'jotai'
import _ from 'lodash'
import type React from 'react'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { useTranslation } from 'react-i18next'
import type { SessionType, ShortcutSendValue } from '@/../shared/types'
import * as dom from '@/hooks/dom'
import useInputBoxHistory from '@/hooks/useInputBoxHistory'

import { useIsSmallScreen } from '@/hooks/useScreenChange'
import { trackingEvent } from '@/packages/event'
import * as picUtils from '@/packages/pic_utils'
import platform from '@/platform'
import storage from '@/storage'
import { StorageKeyGenerator } from '@/storage/StoreStorage'
import * as atoms from '@/stores/atoms'
import * as toastActions from '@/stores/toastActions'

import { featureFlags } from '@/utils/feature-flags'
import { FileMiniCard, ImageMiniCard, LinkMiniCard } from './Attachments'
import KnowledgeBasePills from './KnowledgeBasePills'
import { KnowledgeBaseData } from '../hooks/useKnowledgeBase'

import MCPMenu from './mcp/MCPMenu'
import { Keys } from './Shortcut'

export type InputBoxPayload = {
  input: string
  pictureKeys?: string[]
  attachments?: File[]
  links?: { url: string }[]
  webBrowsing?: boolean
  needGenerating?: boolean
  selectedKnowledgeBase?: KnowledgeBaseData | null
}

export type InputBoxRef = {
  setQuote: (quote: string) => void
}

export type InputBoxProps = {
  sessionId?: string
  sessionType?: SessionType
  generating?: boolean

  onSubmit?(payload: InputBoxPayload): Promise<boolean>
  onStopGenerating?(): boolean
  onStartNewThread?(): boolean
  onRollbackThread?(): boolean
  onClickSessionSettings?(): boolean | Promise<boolean>
}

const InputBox = forwardRef<InputBoxRef, InputBoxProps>(
  (
    {
      // sessionId,
      sessionType = 'chat',
      generating = false,

      onSubmit,
      onStopGenerating,
      onStartNewThread,
      onRollbackThread,
      onClickSessionSettings,
    },
    ref
  ) => {
    const { t } = useTranslation()
    const isSmallScreen = useIsSmallScreen()
    const { height: viewportHeight } = useViewportSize()
    const pasteLongTextAsAFile = useAtomValue(atoms.pasteLongTextAsAFileAtom)
    const shortcuts = useAtomValue(atoms.shortcutsAtom)

    const [messageInput, setMessageInput] = useState('')
    const [pictureKeys, setPictureKeys] = useState<string[]>([])
    const [attachments, setAttachments] = useState<File[]>([])
    const [webBrowsingMode, setWebBrowsingMode] = useAtom(atoms.inputBoxWebBrowsingModeAtom)
    const [links, setLinks] = useAtom(atoms.inputBoxLinksAtom)
    const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<any>(null)
    const pictureInputRef = useRef<HTMLInputElement | null>(null)
    const fileInputRef = useRef<HTMLInputElement | null>(null)
    const disableSubmit = useMemo(
      () => !(messageInput.trim() || links?.length || attachments?.length || pictureKeys?.length),
      [messageInput, links, attachments, pictureKeys]
    )



    const [showRollbackThreadButton, setShowRollbackThreadButton] = useState(false)
    useEffect(() => {
      if (showRollbackThreadButton) {
        const tid = setTimeout(() => {
          setShowRollbackThreadButton(false)
        }, 5000)
        return () => {
          clearTimeout(tid)
        }
      }
    }, [showRollbackThreadButton])

    const inputRef = useRef<HTMLTextAreaElement | null>(null)

    useImperativeHandle(
      ref,
      () => ({
        // 暂时并没有用到，还是使用了之前atom的方案
        setQuote: (data) => {
          setMessageInput((prev) => `${prev}\n\n${data}`)
          dom.focusMessageInput()
          dom.setMessageInputCursorToEnd()
        },
      }),
      []
    )

    const { addInputBoxHistory, getPreviousHistoryInput, getNextHistoryInput, resetHistoryIndex } = useInputBoxHistory()

    const handleSubmit = async (needGenerating = true) => {
      if (disableSubmit || generating) {
        return
      }



      try {
        console.log('🔍 DEBUG - InputBox selectedKnowledgeBase avant envoi:', selectedKnowledgeBase)
        const res = await onSubmit?.({
          input: messageInput,
          pictureKeys,
          attachments,
          links,
          webBrowsing: webBrowsingMode,
          needGenerating,
          selectedKnowledgeBase,
        })

        if (!res) {
          return
        }

        // 重置输入内容
        setMessageInput('')
        setPictureKeys([])
        setAttachments([])
        setLinks([])
        // 重置清理上下文按钮
        setShowRollbackThreadButton(false)

        trackingEvent('send_message', { event_category: 'user' })

        // 如果提交成功，添加到输入历史 (非手机端)
        if (platform.type !== 'mobile') {
          addInputBoxHistory(messageInput)
        }
      } catch (e) {
        console.error('Error submitting message:', e)
        toastActions.add((e as Error)?.message || t('An error occurred while sending the message.'))
      }
    }

    const onMessageInput = useCallback(
      (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        const input = event.target.value
        setMessageInput(input)
        resetHistoryIndex()
      },
      [resetHistoryIndex]
    )

    const onKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      const isPressedHash: Record<ShortcutSendValue, boolean> = {
        '': false,
        Enter: event.keyCode === 13 && !event.shiftKey && !event.ctrlKey && !event.altKey && !event.metaKey,
        'CommandOrControl+Enter': event.keyCode === 13 && (event.ctrlKey || event.metaKey) && !event.shiftKey,
        'Ctrl+Enter': event.keyCode === 13 && event.ctrlKey && !event.shiftKey,
        'Command+Enter': event.keyCode === 13 && event.metaKey,
        'Shift+Enter': event.keyCode === 13 && event.shiftKey,
        'Ctrl+Shift+Enter': event.keyCode === 13 && event.ctrlKey && event.shiftKey,
      }

      // 发送消息
      if (isPressedHash[shortcuts.inpubBoxSendMessage]) {
        if (platform.type === 'mobile' && isSmallScreen && shortcuts.inpubBoxSendMessage === 'Enter') {
          // 移动端点击回车不会发送消息
          return
        }
        event.preventDefault()
        handleSubmit()
        return
      }

      // 发送消息但不生成回复
      if (isPressedHash[shortcuts.inpubBoxSendMessageWithoutResponse]) {
        event.preventDefault()
        handleSubmit(false)
        return
      }

      // 向上向下键翻阅历史消息
      if (
        (event.key === 'ArrowUp' || event.key === 'ArrowDown') &&
        inputRef.current &&
        inputRef.current === document.activeElement && // 聚焦在输入框
        (messageInput.length === 0 || window.getSelection()?.toString() === messageInput) // 要么为空，要么输入框全选
      ) {
        event.preventDefault()
        if (event.key === 'ArrowUp') {
          const previousInput = getPreviousHistoryInput()
          if (previousInput !== undefined) {
            setMessageInput(previousInput)
            setTimeout(() => inputRef.current?.select(), 10)
          }
        } else if (event.key === 'ArrowDown') {
          const nextInput = getNextHistoryInput()
          if (nextInput !== undefined) {
            setMessageInput(nextInput)
            setTimeout(() => inputRef.current?.select(), 10)
          }
        }
      }
    }

    const startNewThread = () => {
      const res = onStartNewThread?.()
      if (res) {
        setShowRollbackThreadButton(true)
      }
    }

    const rollbackThread = () => {
      const res = onRollbackThread?.()
      if (res) {
        setShowRollbackThreadButton(false)
      }
    }

    const insertLinks = (urls: string[]) => {
      let newLinks = [...(links || []), ...urls.map((u) => ({ url: u }))]
      newLinks = _.uniqBy(newLinks, 'url')
      newLinks = newLinks.slice(-6) // 最多插入 6 个链接
      setLinks(newLinks)
    }

    const insertFiles = async (files: File[]) => {
      for (const file of files) {
        // 文件和图片插入方法复用，会导致 svg、gif 这类不支持的图片也被插入，但暂时没看到有什么问题
        if (file.type.startsWith('image/')) {
          const base64 = await picUtils.getImageBase64AndResize(file)
          const key = StorageKeyGenerator.picture('input-box')
          await storage.setBlob(key, base64)
          setPictureKeys((prev) => [...prev, key].slice(-8)) // 最多插入 8 个图片
        } else {
          setAttachments((prev) => [...prev, file].slice(-10)) // 最多插入 10 个附件
        }
      }
    }

    const onFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (!event.target.files) {
        return
      }
      insertFiles(Array.from(event.target.files))
      event.target.value = ''
      dom.focusMessageInput()
    }

    const onImageUploadClick = () => {
      pictureInputRef.current?.click()
    }
    const onFileUploadClick = () => {
      fileInputRef.current?.click()
    }

    const onImageDeleteClick = async (picKey: string) => {
      setPictureKeys(pictureKeys?.filter((k) => k !== picKey))
      // 不删除图片数据，因为可能在其他地方引用，比如通过上下键盘的历史消息快捷输入、发送的消息中引用
      // await storage.delBlob(picKey)
    }

    const onPaste = (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
      if (sessionType === 'picture') {
        return
      }
      if (event.clipboardData?.items) {
        // 对于 Doc/PPT/XLS 等文件中的内容，粘贴时一般会有 4 个 items，分别是 text 文本、html、某格式和图片
        // 因为 getAsString 为异步操作，无法根据 items 中的内容来定制不同的粘贴行为，因此这里选择了最简单的做法：
        // 保持默认的粘贴行为，这时候会粘贴从文档中复制的文本和图片。我认为应该保留图片，因为文档中的表格、图表等图片信息也很重要，很难通过文本格式来表述。
        // 仅在只粘贴图片或文件时阻止默认行为，防止插入文件或图片的名字
        let hasText = false
        for (const item of event.clipboardData.items) {
          if (item.kind === 'file') {
            // 插入文件和图片
            const file = item.getAsFile()
            if (file) {
              insertFiles([file])
            }
            continue
          }
          hasText = true
          if (item.kind === 'string' && item.type === 'text/plain') {
            // 插入链接：如果复制的是链接，则插入链接
            item.getAsString((text) => {
              const raw = text.trim()
              if (raw.startsWith('http://') || raw.startsWith('https://')) {
                const urls = raw
                  .split(/\s+/)
                  .map((url) => url.trim())
                  .filter((url) => url.startsWith('http://') || url.startsWith('https://'))
                insertLinks(urls)
              }
              if (pasteLongTextAsAFile && raw.length > 3000) {
                const file = new File([text], `pasted_text_${attachments?.length || 0}.txt`, {
                  type: 'text/plain',
                })
                insertFiles([file])
                setMessageInput(messageInput) // 删除掉默认粘贴进去的长文本
              }
            })
          }
        }
        // 如果没有任何文本，则说明只是复制了图片或文件。这里阻止默认行为，防止插入文件或图片的名字
        if (!hasText) {
          event.preventDefault()
        }
      }
    }

    const handleAttachLink = async () => {
      const links: string[] = await NiceModal.show('attach-link')
      if (links) {
        insertLinks(links)
      }
    }

    // 拖拽上传
    const { getRootProps, getInputProps } = useDropzone({
      onDrop: (acceptedFiles: File[]) => {
        insertFiles(acceptedFiles)
      },
      noClick: true,
      noKeyboard: true,
    })

    // 引用消息
    const [quote, setQuote] = useAtom(atoms.quoteAtom)
    // biome-ignore lint/correctness/useExhaustiveDependencies: todo
    useEffect(() => {
      if (quote !== '') {
        // TODO: 支持引用消息中的图片
        // TODO: 支持引用消息中的文件
        setQuote('')
        setMessageInput((val) => {
          if (!val) {
            return quote
          } else {
            const newlines = val.match(/(\n)+$/)?.[0].length || 0
            console.log(newlines, Math.max(0, 2 - newlines))
            return val + '\n'.repeat(Math.max(0, 2 - newlines)) + quote
          }
        })
        // setPreviousMessageQuickInputMark('')
        dom.focusMessageInput()
        dom.setMessageInputCursorToEnd()
      }
    }, [quote])

    return (
      <Box
        pt={isSmallScreen ? 0 : 'sm'}
        pb={isSmallScreen ? 'md' : 'sm'}
        px={isSmallScreen ? 'xs' : 'sm'}
        id={dom.InputBoxID}
        {...getRootProps()}
      >
        <input className="hidden" {...getInputProps()} />

        <Stack
          className="rounded-lg sm:rounded-md bg-[var(--mantine-color-chatbox-background-primary)] border border-solid border-[var(--mantine-color-chatbox-border-primary-outline)]"
          gap={0}
          style={{ padding: 0, minHeight: 'auto', height: 'fit-content' }}
        >
          {/* Inputs cachés pour les fonctionnalités d'upload */}
          <input
            type="file"
            ref={pictureInputRef}
            className="hidden"
            onChange={onFileInputChange}
            accept="image/png, image/jpeg"
            multiple
          />
          <input type="file" ref={fileInputRef} className="hidden" onChange={onFileInputChange} multiple />

          {/* Zone de saisie EN HAUT */}
          <Textarea
            unstyled={true}
            classNames={{
              input:
                'block w-full outline-none border-none px-sm py-xs resize-none bg-transparent text-[var(--mantine-color-chatbox-primary-text)]',
            }}
            size="sm"
            id={dom.messageInputID}
            ref={inputRef}
            placeholder={t('Type your question here...') || ''}
            bg="transparent"
            autosize={true}
            minRows={1}
            maxRows={Math.max(3, Math.floor(viewportHeight / 100))}
            value={messageInput}
            autoFocus={!isSmallScreen}
            onChange={onMessageInput}
            onKeyDown={onKeyDown}
            onPaste={onPaste}
            style={{ flex: 1, marginBottom: 0 }}
          />

          {/* Ligne d'outils EN BAS */}
          <Flex align="center" gap="xs" style={{ padding: '8px 12px 6px 12px', marginTop: 0 }}>
            {/* Bouton + à gauche - aligné au centre des étiquettes */}
            <Menu shadow="md" position="top-start" offset={5} withinPortal={true}>
              <Menu.Target>
                <ActionIcon
                  variant="subtle"
                  size="sm"
                  style={{
                    alignSelf: 'flex-start',
                    marginTop: '8px',
                    color: '#9ca3af !important',
                    backgroundColor: 'transparent'
                  }}
                >
                  <IconCirclePlus size={18} style={{ color: '#9ca3af' }} />
                </ActionIcon>
              </Menu.Target>

              <Menu.Dropdown
                style={{
                  backgroundColor: 'var(--mantine-color-dark-6)',
                  border: '1px solid var(--mantine-color-dark-4)',
                  borderRadius: '8px',
                  minWidth: '200px',
                  zIndex: 1000,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
                }}
              >
                <Menu.Item
                  leftSection={<IconFolder size={16} />}
                  onClick={onFileUploadClick}
                  style={{
                    color: 'var(--mantine-color-gray-0)',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Importer des fichiers
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconDatabase size={16} />}
                  onClick={() => {
                    // TODO: Implémenter l'ajout depuis Drive
                    console.log('Ajouter depuis Drive')
                  }}
                  style={{
                    color: 'var(--mantine-color-gray-0)',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Ajouter depuis Drive
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconCode size={16} />}
                  onClick={() => {
                    // TODO: Implémenter l'import de code
                    console.log('Importer du code')
                  }}
                  style={{
                    color: 'var(--mantine-color-gray-0)',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Importer du code
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconWorld size={16} />}
                  onClick={() => {
                    setWebBrowsingMode(!webBrowsingMode)
                    dom.focusMessageInput()
                  }}
                  style={{
                    color: webBrowsingMode ? 'var(--mantine-color-blue-4)' : 'var(--mantine-color-gray-0)',
                    backgroundColor: webBrowsingMode ? 'var(--mantine-color-blue-9)' : 'transparent',
                    '&:hover': {
                      backgroundColor: webBrowsingMode ? 'var(--mantine-color-blue-8)' : 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Web Browsing {webBrowsingMode && '✓'}
                </Menu.Item>

                <Menu.Divider style={{ borderColor: 'var(--mantine-color-dark-4)' }} />

                <Menu.Item
                  leftSection={<IconVideo size={16} />}
                  onClick={() => {
                    // TODO: Implémenter vidéo
                    console.log('Vidéo')
                  }}
                  style={{
                    color: 'var(--mantine-color-gray-0)',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Vidéo
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconSearch size={16} />}
                  onClick={() => {
                    // TODO: Implémenter Deep Research
                    console.log('Deep Research')
                  }}
                  style={{
                    color: 'var(--mantine-color-gray-0)',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Deep Research
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconFilePencil size={16} />}
                  onClick={() => {
                    // TODO: Implémenter Canvas
                    console.log('Canvas')
                  }}
                  style={{
                    color: 'var(--mantine-color-gray-0)',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Canvas
                </Menu.Item>

                <Menu.Item
                  leftSection={<IconPhoto size={16} />}
                  onClick={onImageUploadClick}
                  style={{
                    color: 'var(--mantine-color-gray-0)',
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-dark-5)'
                    }
                  }}
                >
                  Image
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>

            {/* Étiquettes des bases de connaissances au milieu */}
            <div style={{ marginBottom: '8px' }}>
              <KnowledgeBasePills onKnowledgeBaseSelect={setSelectedKnowledgeBase} />
            </div>

            {/* Bouton d'envoi à l'extrême droite */}
            <ActionIcon
              disabled={disableSubmit && !generating}
              radius={18}
              size={isSmallScreen ? 28 : 36}
              onClick={generating ? onStopGenerating : () => handleSubmit()}
              className={
                disableSubmit && !generating
                  ? '!text-white !bg-[var(--mantine-color-chatbox-background-tertiary-text)]'
                  : '!bg-[var(--mantine-color-chatbox-background-primary)]'
              }
              style={{ marginLeft: 'auto' }}
            >
              {generating ? <IconPlayerStopFilled size={20} /> : <IconArrowUp size={20} />}
            </ActionIcon>
          </Flex>

          {(!!pictureKeys.length || !!attachments.length || !!links.length) && (
            <Flex px="sm" pb="xs" align="center" wrap="wrap" onClick={() => dom.focusMessageInput()}>
              {pictureKeys?.map((picKey, ix) => (
                <ImageMiniCard
                  // biome-ignore lint/suspicious/noArrayIndexKey: <todo>
                  key={ix}
                  storageKey={picKey}
                  onDelete={() => onImageDeleteClick(picKey)}
                />
              ))}
              {attachments?.map((file, ix) => (
                <FileMiniCard
                  // biome-ignore lint/suspicious/noArrayIndexKey: <todo>
                  key={ix}
                  name={file.name}
                  fileType={file.type}
                  onDelete={() => setAttachments(attachments.filter((f) => f.name !== file.name))}
                />
              ))}
              {links?.map((link, ix) => (
                <LinkMiniCard
                  // biome-ignore lint/suspicious/noArrayIndexKey: <todo>
                  key={ix}
                  url={link.url}
                  onDelete={() => setLinks(links.filter((l) => l.url !== link.url))}
                />
              ))}
            </Flex>
          )}
        </Stack>

        {/* Texte d'avertissement centré verticalement en bas */}
        <Box
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            marginTop: '12px',
            marginBottom: '8px',
            paddingTop: '8px',
            paddingBottom: '8px'
          }}
        >
          <Text
            size="xs"
            style={{
              fontSize: '11px',
              lineHeight: '1.3',
              color: '#a1a1aa',
              textAlign: 'center',
              opacity: 0.8
            }}
          >
            Vérifiez les réponses de DataTec, car il peut se tromper
          </Text>
        </Box>
      </Box>
    )
  }
)

export default InputBox
