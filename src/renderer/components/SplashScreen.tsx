import React, { useEffect } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import { realThemeAtom } from '@/stores/atoms'
import { splashCompletedAtom } from '@/stores/atoms'
import { SplashService } from '@/services/SplashService'

interface SplashScreenProps {
  onComplete: () => void
}

export default function SplashScreen({ onComplete }: SplashScreenProps) {
  const realTheme = useAtomValue(realThemeAtom)
  const [, setSplashCompleted] = useAtom(splashCompletedAtom)
  const isDark = realTheme === 'dark'

  useEffect(() => {
    // Démarrer l'animation après un court délai
    const startTimer = setTimeout(() => {
      const logoElement = document.querySelector('.splash-logo') as HTMLElement
      const titleElement = document.querySelector('.splash-title') as HTMLElement
      const progressElement = document.querySelector('.splash-progress-bar') as HTMLElement

      if (logoElement && titleElement && progressElement) {
        // Animation du logo
        logoElement.style.transform = 'scale(1)'
        logoElement.style.opacity = '1'

        // Animation du titre après 500ms
        setTimeout(() => {
          titleElement.style.opacity = '1'
          titleElement.style.transform = 'translateY(0)'
        }, 500)

        // Animation de la barre de progression après 800ms
        setTimeout(() => {
          progressElement.style.opacity = '1'
          progressElement.style.transform = 'scaleX(1)'
          
          // Démarrer l'animation de remplissage
          const fillElement = progressElement.querySelector('.splash-progress-fill') as HTMLElement
          if (fillElement) {
            fillElement.style.width = '100%'
          }
        }, 800)
      }
    }, 200)

    // Terminer l'animation après 3 secondes au total
    const completeTimer = setTimeout(() => {
      // Marquer le splash comme affiché
      SplashService.markSplashShown()
      setSplashCompleted(true)
      onComplete()
    }, 3000)

    return () => {
      clearTimeout(startTimer)
      clearTimeout(completeTimer)
    }
  }, [onComplete, setSplashCompleted])

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: isDark ? '#282828' : '#f5f7fa',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999,
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
        color: isDark ? '#F8F9FA' : '#333333',
        transition: 'background-color 0.3s ease, color 0.3s ease',
      }}
    >
      {/* Container principal */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'auto',
          height: 'auto',
        }}
      >
        {/* Logo */}
        <div style={{ marginBottom: '30px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <img
            className="splash-logo"
            src={isDark ? 'static/logo-light.svg' : 'static/logo-dark.svg'}
            alt="DataTec Logo"
            style={{
              width: '80px',
              height: '80px',
              transform: 'scale(0.6)',
              opacity: 0.1,
              transition: 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)',
              objectFit: 'contain',
              display: 'block',
            }}
          />
        </div>

        {/* Titre */}
        <h1
          className="splash-title"
          style={{
            fontSize: '18px',
            fontWeight: 500,
            marginBottom: '30px',
            color: isDark ? '#F8F9FA' : '#333333',
            opacity: 0,
            transform: 'translateY(10px)',
            transition: 'opacity 0.5s ease, transform 0.5s ease, color 0.3s ease',
            textAlign: 'center',
            whiteSpace: 'nowrap',
          }}
        >
          DataTec Workspace
        </h1>

        {/* Barre de progression */}
        <div
          className="splash-progress-bar"
          style={{
            width: '120px',
            height: '2px',
            backgroundColor: isDark ? '#333333' : '#e0e0e0',
            borderRadius: '1px',
            overflow: 'hidden',
            opacity: 0,
            transform: 'scaleX(0.9)',
            transition: 'opacity 0.5s ease, transform 0.5s ease, background-color 0.3s ease',
            position: 'relative',
          }}
        >
          <div
            className="splash-progress-fill"
            style={{
              height: '100%',
              width: '0%',
              backgroundColor: isDark ? '#FFFFFF' : '#333333',
              borderRadius: '1px',
              transition: 'width 2s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s ease',
            }}
          />
        </div>
      </div>
    </div>
  )
}
