import React from 'react'
import {
  Box,
  CircularProgress,
  Typography,
  useTheme,
} from '@mui/material'
import { useAtomValue } from 'jotai'
import { realThemeAtom } from '@/stores/atoms'

export default function AuthLoadingScreen() {
  const theme = useTheme()
  const realTheme = useAtomValue(realThemeAtom)
  const isDark = realTheme === 'dark'

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        background: isDark
          ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
          : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        padding: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 3,
        }}
      >
        {/* Logo */}
        <Box
          sx={{
            width: 80,
            height: 80,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <img
            src={isDark ? 'static/logo-light.svg' : 'static/logo-dark.svg'}
            alt="DataTec Logo"
            style={{
              width: '80px',
              height: '80px',
              objectFit: 'contain',
            }}
          />
        </Box>

        {/* Titre */}
        <Typography
          variant="h5"
          component="h1"
          sx={{
            fontWeight: 600,
            color: theme.palette.text.primary,
            textAlign: 'center',
          }}
        >
          DataTec Workspace
        </Typography>

        {/* Indicateur de chargement */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <CircularProgress
            size={40}
            sx={{
              color: isDark ? '#667eea' : '#764ba2',
            }}
          />
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              opacity: 0.8,
            }}
          >
            Initialisation...
          </Typography>
        </Box>
      </Box>
    </Box>
  )
}
