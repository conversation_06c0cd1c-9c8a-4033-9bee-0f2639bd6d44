import { useAtomValue } from 'jotai'
import { useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'
import { isAuthenticatedAtom } from '@/stores/atoms'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const navigate = useNavigate()

  useEffect(() => {
    if (!isAuthenticated) {
      navigate({ to: '/login', replace: true })
    }
  }, [isAuthenticated, navigate])

  if (!isAuthenticated) {
    return null // ou un composant de chargement
  }

  return <>{children}</>
}
