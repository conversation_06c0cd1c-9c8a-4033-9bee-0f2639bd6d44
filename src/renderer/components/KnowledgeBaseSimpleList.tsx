import {
  Box,
  Stack,
  Text,
  Card,
  Flex,
  Switch
} from '@mantine/core'
import {
  IconCircle,
  IconCircleFilled
} from '@tabler/icons-react'
import { KnowledgeBaseData } from '../hooks/useKnowledgeBase'
import { useState, useEffect } from 'react'

interface KnowledgeBaseSimpleListProps {
  knowledgeBases: KnowledgeBaseData[]
  onSelect?: (kb: KnowledgeBaseData) => void
  loading?: boolean
  selectedCategoryId?: string // Nouvelle prop pour filtrer par catégorie
}

export default function KnowledgeBaseSimpleList({
  knowledgeBases,
  onSelect,
  loading = false,
  selectedCategoryId
}: KnowledgeBaseSimpleListProps) {

  // État des switches pour chaque base de connaissances
  const [switchStates, setSwitchStates] = useState<Record<string, boolean>>({})

  // Filtrer les bases de connaissances par catégorie
  const filteredKnowledgeBases = selectedCategoryId
    ? knowledgeBases.filter(kb => kb.category === selectedCategoryId)
    : knowledgeBases

  // Charger les états des switches depuis localStorage au démarrage
  useEffect(() => {
    const savedStates: Record<string, boolean> = {}
    filteredKnowledgeBases.forEach(kb => {
      const saved = localStorage.getItem(`kb-switch-${kb.id}`)
      savedStates[kb.id] = saved ? JSON.parse(saved) : kb.isActive
    })
    setSwitchStates(savedStates)
  }, [filteredKnowledgeBases])

  // Gérer le changement d'état d'un switch
  const handleSwitchChange = (kbId: string, checked: boolean) => {
    setSwitchStates(prev => ({
      ...prev,
      [kbId]: checked
    }))
    // Sauvegarder dans localStorage
    localStorage.setItem(`kb-switch-${kbId}`, JSON.stringify(checked))

    // Déclencher la mise à jour de l'affichage des étiquettes
    const event = new CustomEvent('knowledgeBaseSwitchChanged', {
      detail: { kbId, enabled: checked }
    })
    window.dispatchEvent(event)
  }

  return (
    <Box>
      {/* Header supprimé complètement comme demandé */}

      {/* Liste simplifiée des bases de connaissances - alignement avec le titre "Mes Copilotes" */}
      <Stack gap="sm">
        {filteredKnowledgeBases.map((kb) => (
          <Card
            key={kb.id}
            padding="md"
            style={{
              backgroundColor: 'var(--mantine-color-dark-6)',
              border: '1px solid var(--mantine-color-dark-4)',
              borderRadius: '8px',
              cursor: onSelect ? 'pointer' : 'default',
              transition: 'all 0.2s ease',
              width: '300px', // Largeur fixe réduite (au lieu de 100%)
              '&:hover': onSelect ? {
                backgroundColor: 'var(--mantine-color-dark-5)',
                borderColor: 'var(--mantine-color-blue-6)',
              } : {}
            }}
            onClick={() => onSelect?.(kb)}
          >
            <Flex justify="space-between" align="center">
              {/* Informations principales - SEULEMENT nom avec indicateur */}
              <Flex align="center" gap="md" style={{ flex: 1 }}>
                {/* Indicateur de statut */}
                <Box style={{ position: 'relative' }}>
                  {switchStates[kb.id] ? (
                    <IconCircleFilled
                      size={12}
                      color="var(--mantine-color-blue-5)"
                    />
                  ) : (
                    <IconCircle
                      size={12}
                      color="var(--mantine-color-gray-5)"
                    />
                  )}
                </Box>

                {/* Nom seulement */}
                <Box style={{ flex: 1 }}>
                  <Text
                    size="sm"
                    fw={500}
                    c="var(--mantine-color-gray-0)"
                  >
                    {kb.name}
                  </Text>
                </Box>
              </Flex>

              {/* Switch remplace le badge */}
              <Switch
                checked={switchStates[kb.id] || false}
                onChange={(event) => {
                  event.stopPropagation() // Empêcher le clic sur la card
                  handleSwitchChange(kb.id, event.currentTarget.checked)
                }}
                color="blue"
                size="sm"
                styles={{
                  track: {
                    backgroundColor: switchStates[kb.id]
                      ? 'var(--mantine-color-blue-6)'
                      : 'var(--mantine-color-gray-6)',
                  }
                }}
              />
            </Flex>
          </Card>
        ))}

        {/* Message si aucune base de connaissances */}
        {filteredKnowledgeBases.length === 0 && (
          <Box
            style={{
              backgroundColor: 'var(--mantine-color-dark-7)',
              borderRadius: '8px',
              border: '1px solid var(--mantine-color-dark-4)',
              padding: '2rem',
              textAlign: 'center',
              width: '300px'
            }}
          >
            <Text size="sm" c="var(--mantine-color-gray-4)">
              {selectedCategoryId
                ? 'Aucune base de connaissances dans cette catégorie'
                : 'Aucune base de connaissances configurée'
              }
            </Text>
          </Box>
        )}
      </Stack>
    </Box>
  )
}
