import React, { useState } from 'react'
import {
  Avatar,
  Menu,
  <PERSON>uItem,
  ListItemIcon,
  ListItemText,
  IconButton,
} from '@mui/material'
import {
  Person,
  Logout,
} from '@mui/icons-material'
import { useAtom, useAtomValue } from 'jotai'
import { useNavigate } from '@tanstack/react-router'
import { currentUserAtom, logoutAtom } from '@/stores/atoms'
import { realThemeAtom } from '@/stores/atoms'
import platform from '@/platform'

export default function UserMenu() {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const currentUser = useAtomValue(currentUserAtom)
  const [, logout] = useAtom(logoutAtom)
  const navigate = useNavigate()
  const realTheme = useAtomValue(realThemeAtom)
  const isDark = realTheme === 'dark'
  const isDesktop = platform.type === 'desktop'

  const open = Boolean(anchorEl)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    console.log('UserMenu: handleClick called, isDesktop:', isDesktop)
    event.preventDefault()
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleLogout = () => {
    logout()
    navigate({ to: '/login', replace: true })
    handleClose()
  }

  // Si currentUser n'est pas encore chargé, afficher un avatar par défaut
  const userInitial = currentUser
    ? (currentUser.displayName || currentUser.username || 'U').charAt(0).toUpperCase()
    : 'U'

  const userAvatar = currentUser?.avatar

  // Version desktop avec zone de clic améliorée
  if (isDesktop) {
    return (
      <>
        {/* Wrapper pour augmenter la zone de clic */}
        <div
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            if (open) {
              setAnchorEl(null)
            } else {
              // Utilise l'IconButton comme ancre
              const iconButton = e.currentTarget.querySelector('button')
              if (iconButton) {
                setAnchorEl(iconButton)
              }
            }
          }}
          style={{
            padding: '4px',
            cursor: 'pointer',
            borderRadius: '50%',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            minWidth: '48px',
            minHeight: '48px',
            WebkitAppRegion: 'no-drag',
            pointerEvents: 'auto',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none'
          }}
        >
          <IconButton
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              if (open) {
                setAnchorEl(null)
              } else {
                setAnchorEl(e.currentTarget)
              }
            }}
            onMouseDown={(e) => {
              e.preventDefault()
              e.stopPropagation()
              if (!open) {
                setAnchorEl(e.currentTarget)
              }
            }}
            size="small"
            sx={{
              ml: 2,
              padding: '8px', // ✅ Padding pour la taille originale
              minWidth: '48px', // ✅ Ajusté pour la taille originale 32px + padding
              minHeight: '48px', // ✅ Ajusté pour la taille originale 32px + padding
              borderRadius: '8px', // ✅ BorderRadius original restauré
              position: 'relative',
              zIndex: 1000,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              // ✅ Hover supprimé comme demandé
              '&:active': {
                backgroundColor: isDark ? 'rgba(255,255,255,0.12)' : 'rgba(0,0,0,0.08)',
              },
              WebkitAppRegion: 'no-drag',
              pointerEvents: 'auto',
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none'
            }}
            aria-controls={open ? 'user-menu-desktop' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            disableRipple={false}
          >
            <Avatar
              sx={{
                width: 32, // ✅ Taille originale restaurée
                height: 32, // ✅ Taille originale restaurée
                bgcolor: '#495057', // Bleu-gris moyen exactement comme l'icône M
                color: '#4dabf7', // Bleu clair comme l'icône M
                fontSize: '0.875rem', // ✅ Taille de police originale restaurée
                borderRadius: '6px', // ✅ BorderRadius original restauré
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              src={userAvatar}
            >
              {userInitial}
            </Avatar>
          </IconButton>
        </div>

        <Menu
          anchorEl={anchorEl}
          id="user-menu-desktop"
          open={open}
          onClose={handleClose}
          disableAutoFocusItem
          disableRestoreFocus
          PaperProps={{
            elevation: 8,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              minWidth: 180,
              borderRadius: 2,
              bgcolor: isDark ? 'grey.900' : 'background.paper',
              zIndex: 999999,
              position: 'fixed',
              '& .MuiMenuItem-root': {
                py: 1.5,
                px: 2,
                fontSize: '0.875rem',
                '&:hover': {
                  bgcolor: isDark ? 'grey.800' : 'grey.100',
                },
              },
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          style={{
            zIndex: 999999,
            position: 'fixed'
          }}
          MenuListProps={{
            'aria-labelledby': 'user-menu-desktop',
            sx: { py: 1 }
          }}
        >
          <MenuItem
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              handleClose()
            }}
          >
            <ListItemIcon>
              <Person fontSize="small" />
            </ListItemIcon>
            <ListItemText>Profil</ListItemText>
          </MenuItem>

          <MenuItem
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              handleLogout()
            }}
          >
            <ListItemIcon>
              <Logout fontSize="small" />
            </ListItemIcon>
            <ListItemText>Déconnexion</ListItemText>
          </MenuItem>
        </Menu>
      </>
    )
  }

  // Version web avec IconButton Material-UI
  return (
    <>
      <IconButton
        onClick={handleClick}
        size="small"
        sx={{
          ml: 0,
          padding: '4px', // ✅ Padding réduit pour la taille originale
          borderRadius: '6px', // ✅ BorderRadius original restauré
          // ✅ Hover supprimé comme demandé
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        aria-controls={open ? 'user-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <Avatar
          sx={{
            width: 32, // ✅ Taille originale restaurée
            height: 32, // ✅ Taille originale restaurée
            bgcolor: '#495057', // Bleu-gris moyen exactement comme l'icône M
            color: '#4dabf7', // Bleu clair comme l'icône M
            fontSize: '0.875rem', // ✅ Taille de police originale restaurée
            borderRadius: '6px', // ✅ BorderRadius original restauré
            fontWeight: 600,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          src={userAvatar}
        >
          {userInitial}
        </Avatar>
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        id="user-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        PaperProps={{
          elevation: 8,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            minWidth: 180,
            borderRadius: 2,
            bgcolor: isDark ? 'grey.900' : 'background.paper',
            zIndex: 9999,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        style={{ zIndex: 9999 }}
      >
        <MenuItem onClick={handleClose}>
          <ListItemIcon>
            <Person fontSize="small" />
          </ListItemIcon>
          <ListItemText>Profil</ListItemText>
        </MenuItem>

        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          <ListItemText>Déconnexion</ListItemText>
        </MenuItem>
      </Menu>
    </>
  )
}
