import { Box, Flex } from '@mantine/core'
import { IconDatabase } from '@tabler/icons-react'
import { useState, useEffect } from 'react'
import { KnowledgeBaseData } from '../hooks/useKnowledgeBase'

interface KnowledgeBasePillsProps {
  onKnowledgeBaseSelect?: (knowledgeBase: KnowledgeBaseData | null) => void
}

export default function KnowledgeBasePills({ onKnowledgeBaseSelect }: KnowledgeBasePillsProps) {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBaseData[]>([])
  const [selectedKB, setSelectedKB] = useState<KnowledgeBaseData | null>(null)
  const [switchStates, setSwitchStates] = useState<Record<string, boolean>>({})

  // Charger l'état de sélection depuis localStorage au démarrage
  useEffect(() => {
    try {
      const savedSelection = localStorage.getItem('selectedKnowledgeBase')
      if (savedSelection) {
        const selectedData = JSON.parse(savedSelection)
        setSelectedKB(selectedData)
        onKnowledgeBaseSelect?.(selectedData)
        console.log('🔍 DEBUG - État de sélection restauré:', selectedData.name)
      }
    } catch (error) {
      console.error('Erreur lors du chargement de la sélection:', error)
    }
  }, [])

  // Écouter les changements des switches depuis KnowledgeBaseSimpleList
  useEffect(() => {
    const handleSwitchChange = (event: CustomEvent) => {
      const { kbId, enabled } = event.detail
      setSwitchStates(prev => ({
        ...prev,
        [kbId]: enabled
      }))
      console.log('🔍 DEBUG - Switch changé pour:', kbId, 'enabled:', enabled)

      // Recharger les étiquettes basées sur les nouveaux états des switches
      setTimeout(() => {
        try {
          const stored = localStorage.getItem('knowledgeBases')
          if (stored) {
            const allKBs: KnowledgeBaseData[] = JSON.parse(stored)
            const enabledKBs = allKBs.filter(kb => {
              const saved = localStorage.getItem(`kb-switch-${kb.id}`)
              return saved ? JSON.parse(saved) : kb.isActive
            })
            setKnowledgeBases(enabledKBs)
          }
        } catch (error) {
          console.error('Erreur lors du rechargement des étiquettes:', error)
        }
      }, 100) // Petit délai pour s'assurer que localStorage est mis à jour
    }

    window.addEventListener('knowledgeBaseSwitchChanged', handleSwitchChange as EventListener)

    return () => {
      window.removeEventListener('knowledgeBaseSwitchChanged', handleSwitchChange as EventListener)
    }
  }, [])

  // Charger les bases de connaissances depuis localStorage
  useEffect(() => {
    const loadKnowledgeBases = () => {
      try {
        const stored = localStorage.getItem('knowledgeBases')
        if (stored) {
          const allKBs: KnowledgeBaseData[] = JSON.parse(stored)

          // Charger les états des switches
          const switchStatesData: Record<string, boolean> = {}
          allKBs.forEach((kb: KnowledgeBaseData) => {
            const saved = localStorage.getItem(`kb-switch-${kb.id}`)
            switchStatesData[kb.id] = saved ? JSON.parse(saved) : kb.isActive
          })
          setSwitchStates(switchStatesData)

          // Filtrer seulement les bases de connaissances avec switch activé
          const enabledKBs = allKBs.filter(kb => {
            const saved = localStorage.getItem(`kb-switch-${kb.id}`)
            return saved ? JSON.parse(saved) : kb.isActive
          })
          setKnowledgeBases(enabledKBs)
        }
      } catch (error) {
        console.error('Erreur lors du chargement des bases de connaissances:', error)
      }
    }

    loadKnowledgeBases()

    // Écouter les changements dans localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'knowledgeBases') {
        loadKnowledgeBases()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    
    // Écouter les changements locaux (même onglet)
    const handleLocalChange = () => {
      loadKnowledgeBases()
    }
    
    window.addEventListener('knowledgeBasesChanged', handleLocalChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('knowledgeBasesChanged', handleLocalChange)
    }
  }, [])

  // Effet séparé pour vérifier la validité de la sélection quand les bases changent
  useEffect(() => {
    if (selectedKB && knowledgeBases.length > 0) {
      const isStillActive = knowledgeBases.find(kb => kb.id === selectedKB.id)
      if (!isStillActive) {
        console.log('🔍 DEBUG - Base de connaissances sélectionnée plus disponible, désélection automatique')
        setSelectedKB(null)
        onKnowledgeBaseSelect?.(null)
        localStorage.removeItem('selectedKnowledgeBase')
      }
    }
  }, [knowledgeBases, selectedKB, onKnowledgeBaseSelect])

  const handlePillClick = (kb: KnowledgeBaseData) => {
    console.log('🔍 DEBUG - Clic sur étiquette:', kb.name)
    if (selectedKB?.id === kb.id) {
      // Désélectionner si déjà sélectionné
      console.log('🔍 DEBUG - Désélection de:', kb.name)
      setSelectedKB(null)
      onKnowledgeBaseSelect?.(null)
      // Supprimer la sélection du localStorage
      localStorage.removeItem('selectedKnowledgeBase')
    } else {
      // Sélectionner la nouvelle base
      console.log('🔍 DEBUG - Sélection de:', kb.name, 'avec', kb.files?.length || 0, 'fichiers')
      setSelectedKB(kb)
      onKnowledgeBaseSelect?.(kb)
      // Sauvegarder la sélection dans localStorage
      localStorage.setItem('selectedKnowledgeBase', JSON.stringify(kb))
    }
  }

  // Ne rien afficher s'il n'y a pas de bases de connaissances actives
  if (knowledgeBases.length === 0) {
    return null
  }

  return (
    <Flex gap="xs" wrap="wrap" align="center">
      {knowledgeBases.map((kb) => (
        <Box
          key={kb.id}
          onClick={() => handlePillClick(kb)}
          style={{
            // Couleurs EXACTES du bouton "Nouvelle discussion" avec #64b5f6
            backgroundColor: selectedKB?.id === kb.id
              ? 'transparent'                           // Fond transparent (état normal du bouton)
              : 'transparent',                          // Image 01 : fond transparent
            color: selectedKB?.id === kb.id
              ? '#90caf9'                               // Couleur exacte du bouton "Nouvelle discussion" (blue 300)
              : '#9ca3af',                              // Image 01 : texte gris clair
            border: selectedKB?.id === kb.id
              ? '0.8px solid #90caf9'                   // Bordure 0.8px avec la couleur exacte du bouton
              : '1px solid #4b5563',                    // Image 01 : bordure grise fine
            borderRadius: '8px',            // Coins arrondis comme l'image
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            fontSize: '0.8125rem',                        // Taille réduite pour correspondre au bouton (13px)
            fontWeight: 400,                              // Même poids que bouton "Nouvelle discussion" (normal)
            fontFamily: 'inherit',                        // Même famille de police
            height: '36px',
            whiteSpace: 'nowrap'
          }}
          onMouseEnter={(e) => {
            if (selectedKB?.id === kb.id) {
              // Hover état activé : bleu plus foncé
              e.currentTarget.style.border = '0.8px solid #64b5f6'  // blue 400 (plus foncé)
              e.currentTarget.style.backgroundColor = 'rgba(144, 202, 249, 0.08)'
            } else {
              // Hover état non-activé : bordure bleue claire
              e.currentTarget.style.borderColor = '#6b7280'
              e.currentTarget.style.backgroundColor = 'rgba(75, 85, 99, 0.1)'
            }
          }}
          onMouseLeave={(e) => {
            if (selectedKB?.id === kb.id) {
              // Retour état activé : couleur exacte #90caf9 du bouton
              e.currentTarget.style.border = '0.8px solid #90caf9'
              e.currentTarget.style.backgroundColor = 'transparent'
            } else {
              // Retour état non-activé
              e.currentTarget.style.borderColor = '#4b5563'
              e.currentTarget.style.backgroundColor = 'transparent'
            }
          }}
        >
          <IconDatabase size={14} />
          {kb.name}
        </Box>
      ))}
    </Flex>
  )
}
