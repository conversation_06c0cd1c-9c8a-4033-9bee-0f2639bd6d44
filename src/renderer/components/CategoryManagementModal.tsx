import {
  Modal,
  Stack,
  Text,
  TextInput,
  Button,
  Group,
  ActionIcon,
  Box,
  Flex,
  Alert,
  Divider
} from '@mantine/core'
import {
  IconPlus,
  IconEdit,
  IconTrash,
  IconCheck,
  IconX,
  IconAlertCircle,
  IconFolder,
  IconFolderFilled
} from '@tabler/icons-react'
import { useState, useEffect } from 'react'
import { useCategories, Category } from '../hooks/useCategories'
import { useKnowledgeBase } from '../hooks/useKnowledgeBase'
import { notifications } from '@mantine/notifications'

interface CategoryManagementModalProps {
  opened: boolean
  onClose: () => void
}

export default function CategoryManagementModal({
  opened,
  onClose
}: CategoryManagementModalProps) {
  const { categories, createCategory, deleteCategory, loadCategories } = useCategories()
  const { knowledgeBases, updateKnowledgeBase } = useKnowledgeBase()
  
  const [newCategoryName, setNewCategoryName] = useState('')
  const [editingCategory, setEditingCategory] = useState<string | null>(null)
  const [editingName, setEditingName] = useState('')
  const [loading, setLoading] = useState(false)

  // Recharger les catégories quand le modal s'ouvre
  useEffect(() => {
    if (opened) {
      loadCategories()
    }
  }, [opened, loadCategories])

  // Créer une nouvelle catégorie
  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) return

    const trimmedName = newCategoryName.trim()
    
    // Vérifier si la catégorie existe déjà
    const exists = categories.some(cat => 
      cat.name.toLowerCase() === trimmedName.toLowerCase()
    )

    if (exists) {
      notifications.show({
        title: 'Erreur',
        message: 'Une catégorie avec ce nom existe déjà',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
      return
    }

    try {
      setLoading(true)
      createCategory(trimmedName)
      setNewCategoryName('')
      
      notifications.show({
        title: 'Succès',
        message: 'Catégorie créée avec succès',
        color: 'green',
        icon: <IconCheck size={16} />
      })
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la création de la catégorie',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
    } finally {
      setLoading(false)
    }
  }

  // Commencer l'édition d'une catégorie
  const handleStartEdit = (category: Category) => {
    if (category.isDefault) return // Ne pas permettre l'édition de "Général"
    
    setEditingCategory(category.id)
    setEditingName(category.name)
  }

  // Sauvegarder l'édition
  const handleSaveEdit = async () => {
    if (!editingName.trim() || !editingCategory) return

    const trimmedName = editingName.trim()
    
    // Vérifier si le nom existe déjà (sauf pour la catégorie actuelle)
    const exists = categories.some(cat => 
      cat.id !== editingCategory && 
      cat.name.toLowerCase() === trimmedName.toLowerCase()
    )

    if (exists) {
      notifications.show({
        title: 'Erreur',
        message: 'Une catégorie avec ce nom existe déjà',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
      return
    }

    try {
      setLoading(true)
      
      // Mettre à jour la catégorie dans le localStorage
      const updatedCategories = categories.map(cat =>
        cat.id === editingCategory
          ? { ...cat, name: trimmedName }
          : cat
      )
      
      localStorage.setItem('knowledgeBaseCategories', JSON.stringify(updatedCategories))
      window.dispatchEvent(new CustomEvent('categoriesChanged'))
      
      setEditingCategory(null)
      setEditingName('')
      
      notifications.show({
        title: 'Succès',
        message: 'Catégorie mise à jour avec succès',
        color: 'green',
        icon: <IconCheck size={16} />
      })
      
      // Recharger les catégories
      loadCategories()
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la mise à jour de la catégorie',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
    } finally {
      setLoading(false)
    }
  }

  // Annuler l'édition
  const handleCancelEdit = () => {
    setEditingCategory(null)
    setEditingName('')
  }

  // Supprimer une catégorie
  const handleDeleteCategory = async (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId)
    if (!category || category.isDefault) return

    // Vérifier si des bases de connaissances utilisent cette catégorie
    const basesUsingCategory = knowledgeBases.filter(kb => kb.category === categoryId)
    
    if (basesUsingCategory.length > 0) {
      // Déplacer toutes les bases vers "Général"
      try {
        setLoading(true)
        
        for (const kb of basesUsingCategory) {
          await updateKnowledgeBase(kb.id!, { ...kb, category: 'general' })
        }
        
        notifications.show({
          title: 'Information',
          message: `${basesUsingCategory.length} base(s) de connaissances déplacée(s) vers "Général"`,
          color: 'blue',
          icon: <IconAlertCircle size={16} />
        })
      } catch (error) {
        notifications.show({
          title: 'Erreur',
          message: 'Erreur lors du déplacement des bases de connaissances',
          color: 'red',
          icon: <IconAlertCircle size={16} />
        })
        setLoading(false)
        return
      }
    }

    // Supprimer la catégorie
    try {
      const success = deleteCategory(categoryId)
      
      if (success) {
        notifications.show({
          title: 'Succès',
          message: 'Catégorie supprimée avec succès',
          color: 'green',
          icon: <IconCheck size={16} />
        })
      }
    } catch (error) {
      notifications.show({
        title: 'Erreur',
        message: 'Erreur lors de la suppression de la catégorie',
        color: 'red',
        icon: <IconAlertCircle size={16} />
      })
    } finally {
      setLoading(false)
    }
  }

  // Compter les bases par catégorie
  const getKnowledgeBaseCount = (categoryId: string) => {
    return knowledgeBases.filter(kb => kb.category === categoryId).length
  }

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Gestion des Catégories"
      size="md"
      styles={{
        title: { 
          color: 'var(--mantine-color-gray-0)', 
          fontWeight: 500,
          fontSize: '18px'
        },
        header: { 
          backgroundColor: 'var(--mantine-color-dark-7)',
          borderBottom: '1px solid var(--mantine-color-dark-4)'
        },
        body: { 
          backgroundColor: 'var(--mantine-color-dark-7)',
          padding: '24px'
        },
        content: {
          backgroundColor: 'var(--mantine-color-dark-7)',
        }
      }}
    >
      <Stack gap="lg">
        {/* Créer une nouvelle catégorie */}
        <Box>
          <Text size="sm" fw={500} c="var(--mantine-color-gray-0)" mb="sm">
            Créer une nouvelle catégorie
          </Text>
          <Group gap="sm">
            <TextInput
              placeholder="Nom de la catégorie"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCreateCategory()
                }
              }}
              style={{ flex: 1 }}
              styles={{
                input: {
                  backgroundColor: 'var(--mantine-color-dark-6)',
                  borderColor: 'var(--mantine-color-dark-4)',
                  color: 'var(--mantine-color-gray-0)',
                  '&::placeholder': { color: 'var(--mantine-color-gray-5)' }
                }
              }}
            />
            <Button
              onClick={handleCreateCategory}
              disabled={!newCategoryName.trim() || loading}
              leftSection={<IconPlus size={16} />}
              color="teal"
            >
              Créer
            </Button>
          </Group>
        </Box>

        <Divider color="var(--mantine-color-dark-4)" />

        {/* Liste des catégories existantes */}
        <Box>
          <Text size="sm" fw={500} c="var(--mantine-color-gray-0)" mb="sm">
            Catégories existantes
          </Text>
          
          <Stack gap="xs">
            {categories.map((category) => (
              <Box
                key={category.id}
                style={{
                  padding: '12px',
                  backgroundColor: 'var(--mantine-color-dark-6)',
                  borderRadius: '8px',
                  border: '1px solid var(--mantine-color-dark-4)'
                }}
              >
                <Flex align="center" gap="sm">
                  {/* Icône */}
                  <IconFolder size={16} color="var(--mantine-color-gray-5)" />
                  
                  {/* Nom de la catégorie */}
                  {editingCategory === category.id ? (
                    <TextInput
                      value={editingName}
                      onChange={(e) => setEditingName(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          handleSaveEdit()
                        } else if (e.key === 'Escape') {
                          handleCancelEdit()
                        }
                      }}
                      style={{ flex: 1 }}
                      styles={{
                        input: {
                          backgroundColor: 'var(--mantine-color-dark-5)',
                          borderColor: 'var(--mantine-color-blue-6)',
                          color: 'var(--mantine-color-gray-0)',
                          fontSize: '14px'
                        }
                      }}
                      autoFocus
                    />
                  ) : (
                    <Text 
                      size="sm" 
                      c="var(--mantine-color-gray-0)"
                      style={{ flex: 1 }}
                    >
                      {category.name}
                    </Text>
                  )}

                  {/* Compteur de bases */}
                  <Text size="xs" c="var(--mantine-color-gray-5)">
                    {getKnowledgeBaseCount(category.id)} base(s)
                  </Text>

                  {/* Actions */}
                  <Group gap="xs">
                    {editingCategory === category.id ? (
                      <>
                        <ActionIcon
                          size="sm"
                          color="green"
                          onClick={handleSaveEdit}
                          disabled={loading}
                        >
                          <IconCheck size={14} />
                        </ActionIcon>
                        <ActionIcon
                          size="sm"
                          color="gray"
                          onClick={handleCancelEdit}
                          disabled={loading}
                        >
                          <IconX size={14} />
                        </ActionIcon>
                      </>
                    ) : (
                      <>
                        {!category.isDefault && (
                          <>
                            <ActionIcon
                              size="sm"
                              color="blue"
                              onClick={() => handleStartEdit(category)}
                              disabled={loading}
                            >
                              <IconEdit size={14} />
                            </ActionIcon>
                            <ActionIcon
                              size="sm"
                              color="red"
                              onClick={() => handleDeleteCategory(category.id)}
                              disabled={loading}
                            >
                              <IconTrash size={14} />
                            </ActionIcon>
                          </>
                        )}
                      </>
                    )}
                  </Group>
                </Flex>

                {/* Message pour la catégorie par défaut */}
                {category.isDefault && (
                  <Text size="xs" c="var(--mantine-color-gray-6)" mt="xs">
                    Catégorie par défaut - Non modifiable
                  </Text>
                )}
              </Box>
            ))}
          </Stack>
        </Box>

        {/* Informations */}
        <Alert
          icon={<IconAlertCircle size={16} />}
          color="blue"
          variant="light"
          styles={{
            root: {
              backgroundColor: 'rgba(34, 139, 230, 0.1)',
              borderColor: 'rgba(34, 139, 230, 0.3)'
            },
            message: { color: 'var(--mantine-color-gray-0)' }
          }}
        >
          <Text size="xs">
            • La catégorie "Général" ne peut pas être supprimée ou renommée<br/>
            • Supprimer une catégorie déplacera automatiquement ses bases vers "Général"
          </Text>
        </Alert>
      </Stack>
    </Modal>
  )
}
