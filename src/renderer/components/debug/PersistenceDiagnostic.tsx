import React, { useEffect, useState } from 'react'
import { useAtomValue } from 'jotai'
import { authAtom } from '@/stores/atoms/authAtoms'
import { newAuthAtom } from '@/stores/atoms/newAuthAtoms'
import { sessionsListAtom, currentSessionIdAtom } from '@/stores/atoms/sessionAtoms'
import { settingsAtom } from '@/stores/atoms/settingsAtoms'
import { useStorageDebug } from '@/hooks/useStorageDebug'
import { effectTracker, appInitTimer } from '@/utils/effectTracker'

interface DiagnosticReport {
  timestamp: string
  status: 'healthy' | 'warning' | 'critical'
  issues: Array<{
    type: 'auth_conflict' | 'data_loss' | 'timing_issue' | 'storage_corruption'
    severity: 'low' | 'medium' | 'high'
    description: string
    solution: string
  }>
  recommendations: string[]
  dataState: {
    auth: any
    newAuth: any
    sessions: number
    currentSession: string | null
    settings: any
  }
}

export const PersistenceDiagnostic: React.FC = () => {
  const [report, setReport] = useState<DiagnosticReport | null>(null)
  const [isVisible, setIsVisible] = useState(false)
  
  // États des atoms
  const authState = useAtomValue(authAtom)
  const newAuthState = useAtomValue(newAuthAtom)
  const sessionsList = useAtomValue(sessionsListAtom)
  const currentSessionId = useAtomValue(currentSessionIdAtom)
  const settings = useAtomValue(settingsAtom)
  
  // Hook de debug
  const storageDebug = useStorageDebug()

  const generateReport = () => {
    const report: DiagnosticReport = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      issues: [],
      recommendations: [],
      dataState: {
        auth: authState,
        newAuth: newAuthState,
        sessions: sessionsList.length,
        currentSession: currentSessionId,
        settings: settings
      }
    }

    // 1. Vérifier les conflits d'authentification
    if (authState.isAuthenticated !== newAuthState.isAuthenticated) {
      report.issues.push({
        type: 'auth_conflict',
        severity: 'high',
        description: 'Incohérence entre les systèmes d\'authentification',
        solution: 'Utiliser storageDebug.cleanupConflicts() pour synchroniser'
      })
      report.status = 'critical'
    }

    // 2. Vérifier la perte de données
    if (authState.isAuthenticated && sessionsList.length === 0) {
      report.issues.push({
        type: 'data_loss',
        severity: 'medium',
        description: 'Utilisateur authentifié mais aucune session trouvée',
        solution: 'Vérifier le storage et utiliser le système de récupération'
      })
      if (report.status === 'healthy') report.status = 'warning'
    }

    // 3. Vérifier les problèmes de timing
    const timingAnalysis = effectTracker.analyze()
    if (timingAnalysis && timingAnalysis.issues.length > 0) {
      report.issues.push({
        type: 'timing_issue',
        severity: 'medium',
        description: `Problèmes de timing détectés: ${timingAnalysis.issues.join(', ')}`,
        solution: 'Ajouter des délais d\'initialisation ou revoir l\'ordre des useEffect'
      })
      if (report.status === 'healthy') report.status = 'warning'
    }

    // 4. Vérifier la corruption du storage
    try {
      const authData = localStorage.getItem('auth')
      const newAuthData = localStorage.getItem('new-auth')
      
      if (authData) JSON.parse(authData)
      if (newAuthData) JSON.parse(newAuthData)
    } catch (error) {
      report.issues.push({
        type: 'storage_corruption',
        severity: 'high',
        description: 'Données corrompues dans localStorage',
        solution: 'Utiliser StorageRecovery.cleanupCorruptedData()'
      })
      report.status = 'critical'
    }

    // 5. Générer des recommandations
    if (report.issues.length === 0) {
      report.recommendations.push('✅ Aucun problème détecté')
    } else {
      if (report.issues.some(i => i.type === 'auth_conflict')) {
        report.recommendations.push('🔧 Nettoyer les conflits d\'authentification')
      }
      if (report.issues.some(i => i.type === 'data_loss')) {
        report.recommendations.push('💾 Exécuter la récupération des données')
      }
      if (report.issues.some(i => i.type === 'timing_issue')) {
        report.recommendations.push('⏱️ Optimiser l\'ordre d\'initialisation')
      }
      if (report.issues.some(i => i.type === 'storage_corruption')) {
        report.recommendations.push('🧹 Nettoyer les données corrompues')
      }
    }

    setReport(report)
    console.log('📋 [DIAGNOSTIC] Rapport généré:', report)
  }

  // Générer le rapport au montage et à chaque changement
  useEffect(() => {
    generateReport()
  }, [authState, newAuthState, sessionsList, currentSessionId])

  // Actions de réparation
  const executeRepair = async (issueType: string) => {
    console.log(`🔧 [REPAIR] Exécution de la réparation pour: ${issueType}`)
    
    try {
      switch (issueType) {
        case 'auth_conflict':
          storageDebug.cleanupConflicts()
          break
          
        case 'data_loss':
          const { StorageRecovery } = await import('@/utils/storageRecovery')
          await StorageRecovery.recoverLostData()
          break
          
        case 'storage_corruption':
          const { StorageRecovery: SR } = await import('@/utils/storageRecovery')
          await SR.cleanupCorruptedData()
          break
          
        case 'timing_issue':
          effectTracker.reset()
          break
      }
      
      // Régénérer le rapport après réparation
      setTimeout(generateReport, 1000)
      
    } catch (error) {
      console.error(`❌ [REPAIR] Erreur lors de la réparation:`, error)
    }
  }

  if (!isVisible) {
    const statusColor = report?.status === 'critical' ? '#ff4444' : 
                       report?.status === 'warning' ? '#ff9800' : '#4CAF50'
    
    return (
      <div
        style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          zIndex: 10000,
          backgroundColor: statusColor,
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          fontWeight: 'bold'
        }}
        onClick={() => setIsVisible(true)}
      >
        📋 DIAGNOSTIC ({report?.issues.length || 0})
      </div>
    )
  }

  if (!report) return null

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        width: '450px',
        maxHeight: '70vh',
        backgroundColor: 'rgba(0, 0, 0, 0.95)',
        color: 'white',
        padding: '16px',
        borderRadius: '8px',
        fontSize: '12px',
        fontFamily: 'monospace',
        overflow: 'auto',
        zIndex: 10000,
        border: `2px solid ${report.status === 'critical' ? '#ff4444' : 
                              report.status === 'warning' ? '#ff9800' : '#4CAF50'}`
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
        <h3 style={{ margin: 0, fontSize: '14px' }}>
          📋 Diagnostic de Persistance
          <span style={{ 
            marginLeft: '8px', 
            color: report.status === 'critical' ? '#ff4444' : 
                   report.status === 'warning' ? '#ff9800' : '#4CAF50'
          }}>
            [{report.status.toUpperCase()}]
          </span>
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            padding: '4px 8px',
            fontSize: '10px',
            backgroundColor: '#f44336',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          ✕
        </button>
      </div>

      {/* Issues */}
      {report.issues.length > 0 && (
        <div style={{ marginBottom: '12px' }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#ff6b6b' }}>🚨 Problèmes Détectés</h4>
          {report.issues.map((issue, index) => (
            <div key={index} style={{ 
              marginBottom: '8px', 
              padding: '8px', 
              backgroundColor: 'rgba(255,107,107,0.1)',
              borderRadius: '4px'
            }}>
              <div style={{ fontWeight: 'bold', color: '#ff6b6b' }}>
                {issue.type} ({issue.severity})
              </div>
              <div style={{ fontSize: '11px', margin: '4px 0' }}>
                {issue.description}
              </div>
              <div style={{ fontSize: '10px', color: '#ccc' }}>
                💡 {issue.solution}
              </div>
              <button
                onClick={() => executeRepair(issue.type)}
                style={{
                  marginTop: '4px',
                  padding: '2px 6px',
                  fontSize: '9px',
                  backgroundColor: '#4CAF50',
                  color: 'white',
                  border: 'none',
                  borderRadius: '2px',
                  cursor: 'pointer'
                }}
              >
                🔧 Réparer
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Recommendations */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#4CAF50' }}>💡 Recommandations</h4>
        {report.recommendations.map((rec, index) => (
          <div key={index} style={{ fontSize: '11px', marginBottom: '4px' }}>
            {rec}
          </div>
        ))}
      </div>

      {/* Data State Summary */}
      <div>
        <h4 style={{ margin: '0 0 8px 0', color: '#2196F3' }}>📊 État des Données</h4>
        <div style={{ fontSize: '10px' }}>
          <div>🔐 Auth: {report.dataState.auth.isAuthenticated ? '✅' : '❌'}</div>
          <div>🔐 NewAuth: {report.dataState.newAuth.isAuthenticated ? '✅' : '❌'}</div>
          <div>💬 Sessions: {report.dataState.sessions}</div>
          <div>📍 Session actuelle: {report.dataState.currentSession || 'Aucune'}</div>
          <div>⚙️ Paramètres: {report.dataState.settings ? '✅' : '❌'}</div>
        </div>
      </div>
    </div>
  )
}
