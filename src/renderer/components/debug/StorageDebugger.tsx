import React, { useEffect, useState } from 'react'
import { useAtomValue, useAtom } from 'jotai'
import { 
  authAtom, 
  splashCompletedAtom,
  initAuthAtom 
} from '@/stores/atoms/authAtoms'
import { 
  newAuthAtom, 
  authInitializationAtom 
} from '@/stores/atoms/newAuthAtoms'
import { 
  sessionsListAtom, 
  currentSessionIdAtom 
} from '@/stores/atoms/sessionAtoms'
import { settingsAtom } from '@/stores/atoms/settingsAtoms'
import storage from '@/storage'

interface StorageDebugInfo {
  localStorage: Record<string, any>
  atomStates: Record<string, any>
  storageKeys: string[]
  initOrder: string[]
  errors: string[]
}

export const StorageDebugger: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<StorageDebugInfo>({
    localStorage: {},
    atomStates: {},
    storageKeys: [],
    initOrder: [],
    errors: []
  })
  const [isVisible, setIsVisible] = useState(false)

  // Atoms à surveiller
  const authState = useAtomValue(authAtom)
  const newAuthState = useAtomValue(newAuthAtom)
  const splashCompleted = useAtomValue(splashCompletedAtom)
  const sessionsList = useAtomValue(sessionsListAtom)
  const currentSessionId = useAtomValue(currentSessionIdAtom)
  const settings = useAtomValue(settingsAtom)
  const [, initAuth] = useAtom(initAuthAtom)
  const [authInitialized] = useAtom(authInitializationAtom)

  const collectDebugInfo = async () => {
    const info: StorageDebugInfo = {
      localStorage: {},
      atomStates: {},
      storageKeys: [],
      initOrder: [],
      errors: []
    }

    try {
      // 1. Collecter toutes les clés du localStorage
      console.log('🔍 [DEBUG] Collecte des données localStorage...')
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key) {
          try {
            const value = localStorage.getItem(key)
            info.localStorage[key] = value ? JSON.parse(value) : value
          } catch (e) {
            info.localStorage[key] = localStorage.getItem(key) // Valeur brute si JSON invalide
          }
        }
      }

      // 2. Collecter les états des atoms
      console.log('🔍 [DEBUG] Collecte des états atoms...')
      info.atomStates = {
        authAtom: authState,
        newAuthAtom: newAuthState,
        splashCompleted,
        sessionsList: sessionsList?.length || 0,
        currentSessionId,
        settingsTheme: settings?.theme,
        authInitialized
      }

      // 3. Collecter les clés de storage personnalisé
      console.log('🔍 [DEBUG] Collecte des clés storage...')
      const allStorageData = await storage.getAll()
      info.storageKeys = Object.keys(allStorageData)

      // 4. Ordre d'initialisation (simulé)
      info.initOrder = [
        'localStorage load',
        'atoms initialization',
        'authService.initialize',
        'storage.getAll',
        'migration.migrate',
        'initData'
      ]

    } catch (error) {
      console.error('❌ [DEBUG] Erreur lors de la collecte:', error)
      info.errors.push(`Erreur de collecte: ${error}`)
    }

    setDebugInfo(info)
  }

  // Collecter les infos au montage et à chaque changement d'état
  useEffect(() => {
    collectDebugInfo()
  }, [authState, newAuthState, splashCompleted, sessionsList, currentSessionId])

  // Logs détaillés à chaque changement
  useEffect(() => {
    console.log('🔍 [DEBUG] État authAtom:', authState)
    console.log('🔍 [DEBUG] État newAuthAtom:', newAuthState)
    console.log('🔍 [DEBUG] splashCompleted:', splashCompleted)
    console.log('🔍 [DEBUG] sessionsList length:', sessionsList?.length)
    console.log('🔍 [DEBUG] currentSessionId:', currentSessionId)
  }, [authState, newAuthState, splashCompleted, sessionsList, currentSessionId])

  // Test de réinitialisation
  const testReinitialisation = async () => {
    console.log('🧪 [TEST] Test de réinitialisation...')
    try {
      await initAuth()
      console.log('✅ [TEST] Réinitialisation réussie')
    } catch (error) {
      console.error('❌ [TEST] Erreur de réinitialisation:', error)
    }
    collectDebugInfo()
  }

  if (!isVisible) {
    return (
      <div
        style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          zIndex: 10000,
          backgroundColor: '#ff6b6b',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          fontWeight: 'bold'
        }}
        onClick={() => setIsVisible(true)}
      >
        DEBUG
      </div>
    )
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        width: '400px',
        maxHeight: '80vh',
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        color: 'white',
        padding: '16px',
        borderRadius: '8px',
        fontSize: '11px',
        fontFamily: 'monospace',
        overflow: 'auto',
        zIndex: 10000,
        border: '1px solid #333'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
        <h3 style={{ margin: 0, fontSize: '14px' }}>🔍 Storage Debugger</h3>
        <div>
          <button
            onClick={testReinitialisation}
            style={{
              marginRight: '8px',
              padding: '4px 8px',
              fontSize: '10px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Test Reinit
          </button>
          <button
            onClick={() => setIsVisible(false)}
            style={{
              padding: '4px 8px',
              fontSize: '10px',
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            ✕
          </button>
        </div>
      </div>

      {/* États des atoms */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#4CAF50' }}>📊 États Atoms</h4>
        <pre style={{ margin: 0, fontSize: '10px', whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(debugInfo.atomStates, null, 2)}
        </pre>
      </div>

      {/* localStorage */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#2196F3' }}>💾 localStorage</h4>
        <div style={{ maxHeight: '200px', overflow: 'auto' }}>
          {Object.entries(debugInfo.localStorage).map(([key, value]) => (
            <div key={key} style={{ marginBottom: '4px', padding: '4px', backgroundColor: 'rgba(255,255,255,0.1)' }}>
              <strong style={{ color: '#FFD700' }}>{key}:</strong>
              <pre style={{ margin: '2px 0 0 0', fontSize: '9px', whiteSpace: 'pre-wrap' }}>
                {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
              </pre>
            </div>
          ))}
        </div>
      </div>

      {/* Clés de storage */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#FF9800' }}>🗂️ Storage Keys ({debugInfo.storageKeys.length})</h4>
        <div style={{ fontSize: '9px' }}>
          {debugInfo.storageKeys.join(', ')}
        </div>
      </div>

      {/* Erreurs */}
      {debugInfo.errors.length > 0 && (
        <div style={{ marginBottom: '12px' }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#f44336' }}>❌ Erreurs</h4>
          {debugInfo.errors.map((error, index) => (
            <div key={index} style={{ color: '#ff6b6b', fontSize: '10px' }}>
              {error}
            </div>
          ))}
        </div>
      )}

      {/* Ordre d'initialisation */}
      <div>
        <h4 style={{ margin: '0 0 8px 0', color: '#9C27B0' }}>🔄 Ordre d'initialisation</h4>
        <ol style={{ margin: 0, paddingLeft: '16px', fontSize: '10px' }}>
          {debugInfo.initOrder.map((step, index) => (
            <li key={index}>{step}</li>
          ))}
        </ol>
      </div>
    </div>
  )
}
