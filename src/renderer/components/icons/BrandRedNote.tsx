import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react'

export default function BrandRedNote(props: { className?: string; onClick?: <PERSON><PERSON><PERSON><PERSON>andler<SVGSVGElement> }) {
  const { className, onClick } = props
  return (
    <svg className={className} onClick={onClick} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.3161 10.5728C17.2274 10.58 17.1395 10.5871 17.0605 10.5758C17.0605 10.8935 17.0629 11.2112 17.0676 11.5289H17.7973C17.7962 11.435 17.7979 11.3411 17.7995 11.2474C17.8029 11.0542 17.8063 10.8615 17.7863 10.6696C17.6677 10.5442 17.4904 10.5586 17.3161 10.5728Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.3185 2H4.61299C3.23564 2.03906 2.03408 3.24453 2.00049 4.62187V19.3156C2.01519 20.0115 2.29364 20.6758 2.77952 21.1741C3.26539 21.6725 3.92242 21.9677 4.61768 22H19.3177C20.0237 21.9851 20.6968 21.6983 21.1966 21.1994C21.6964 20.7004 21.9843 20.0279 22.0005 19.3219V4.61563C21.9638 3.21641 20.7192 2 19.3185 2ZM5.91268 11.6617C5.91361 10.8222 5.91453 9.98288 5.92002 9.14375C6.27471 9.14062 6.63017 9.14141 6.98486 9.14297C6.99189 10.7203 6.9919 12.2984 6.9919 13.8758C6.99736 14.1984 6.91065 14.557 6.63955 14.7586C6.40361 14.9395 6.10551 14.9339 5.81782 14.9286C5.7378 14.9271 5.65858 14.9256 5.58174 14.9281C5.44111 14.6258 5.30518 14.3242 5.17939 14.0117C5.238 14.0112 5.2966 14.0115 5.35518 14.0118C5.48403 14.0124 5.61275 14.013 5.74111 14.0055C5.76657 14.0066 5.79194 14.0017 5.8152 13.9913C5.83846 13.9809 5.85898 13.9652 5.87512 13.9455C5.89127 13.9258 5.90261 13.9026 5.90823 13.8778C5.91385 13.8529 5.91361 13.8271 5.90752 13.8023C5.91111 13.0888 5.9119 12.3752 5.91268 11.6617ZM9.4919 10.8891C9.7669 10.2865 10.0367 9.68125 10.3013 9.07344C10.6334 9.06847 10.9656 9.06995 11.299 9.07143C11.3325 9.07158 11.3661 9.07173 11.3997 9.07187C11.3241 9.26491 11.236 9.45353 11.1479 9.64206C11.0138 9.92888 10.8799 10.2155 10.7903 10.5172C10.9784 10.5972 11.1942 10.5865 11.4103 10.5757C11.5442 10.5691 11.6783 10.5624 11.806 10.5773C11.6971 10.832 11.583 11.0845 11.469 11.3371C11.2975 11.7169 11.1259 12.0968 10.9716 12.4844C11.1593 12.5221 11.351 12.5192 11.5423 12.5164C11.593 12.5156 11.6437 12.5148 11.6942 12.5148C11.5897 12.754 11.4833 12.9917 11.3768 13.2295C11.348 13.2939 11.3191 13.3582 11.2903 13.4227C11.1206 13.4182 10.9508 13.4203 10.781 13.4223C10.5365 13.4253 10.292 13.4283 10.0481 13.4117C9.82002 13.4094 9.61142 13.1773 9.69502 12.9492C9.78542 12.6719 9.90616 12.4067 10.0269 12.1414C10.1076 11.9643 10.1882 11.7871 10.2599 11.6062C10.1942 11.6019 10.1252 11.6034 10.0554 11.6049C9.85123 11.6093 9.63993 11.6139 9.4833 11.4719C9.31924 11.3086 9.41377 11.0695 9.4919 10.8891ZM15.9849 9.48437V9.07734C16.0576 9.07703 16.1305 9.07674 16.2033 9.07646C16.4911 9.07534 16.7802 9.07421 17.0731 9.07109V9.47734C17.5919 9.45703 18.1958 9.45859 18.5708 9.87891C18.8833 10.2242 18.8744 10.6872 18.8658 11.1331C18.8633 11.2613 18.8609 11.388 18.8661 11.5102C19.2583 11.5172 19.6935 11.6094 19.9349 11.9484C20.1467 12.2472 20.138 12.611 20.1294 12.9664C20.1265 13.0868 20.1237 13.2062 20.1294 13.3219C20.1263 13.4116 20.1285 13.5053 20.1307 13.6005C20.1392 13.9656 20.1481 14.3516 19.863 14.6156C19.5675 14.9373 19.1328 14.9313 18.7201 14.9257C18.6047 14.9241 18.491 14.9225 18.3825 14.9281C18.2532 14.6635 18.14 14.3959 18.0266 14.128C18.0078 14.0835 17.9889 14.039 17.97 13.9945C18.0975 13.9902 18.225 13.9917 18.3524 13.9931C18.5264 13.9951 18.7003 13.997 18.8739 13.9844C18.9206 13.9816 18.9643 13.9603 18.9954 13.9253C19.0264 13.8903 19.0423 13.8444 19.0396 13.7977C19.054 13.5093 19.054 13.2204 19.0396 12.932C19.0458 12.7281 18.8341 12.5984 18.6489 12.6141C18.2979 12.6089 17.9472 12.6106 17.5965 12.6123C17.421 12.6132 17.2456 12.6141 17.07 12.6141V14.9242H15.988V12.6125C15.868 12.6125 15.7479 12.6129 15.6277 12.6134C15.3874 12.6142 15.1469 12.6151 14.9067 12.6125C14.9028 12.2508 14.9021 11.8891 14.9067 11.5266C15.0759 11.524 15.2451 11.5242 15.4144 11.5244C15.6049 11.5246 15.7954 11.5248 15.9856 11.5211C15.9919 11.2044 15.9919 10.8898 15.9856 10.5773C15.7466 10.5727 15.5075 10.5727 15.2677 10.5727V9.48437H15.9849ZM12.1239 10.5727V9.48516C12.6198 9.48328 13.1154 9.48309 13.6104 9.48291C13.9405 9.48278 14.2703 9.48266 14.5997 9.48203V10.5703H13.9185V13.8336C14.266 13.8375 14.6144 13.8375 14.962 13.8375L14.9622 14.9234C14.5605 14.9234 14.158 14.924 13.7553 14.9245C12.9487 14.9255 12.1407 14.9266 11.3341 14.9234C11.4974 14.5609 11.6614 14.1984 11.8294 13.8375C11.979 13.8357 12.1288 13.8359 12.2787 13.8361C12.4535 13.8363 12.6283 13.8365 12.8028 13.8336V10.5727H12.1239ZM19.0415 10.3396C19.0326 10.0621 19.0232 9.7651 19.2606 9.58516C19.5341 9.35859 19.988 9.50469 20.0903 9.84062C20.2208 10.1469 19.9763 10.5258 19.6513 10.5555C19.5044 10.5685 19.3572 10.5663 19.2102 10.5641C19.1546 10.5633 19.099 10.5625 19.0435 10.5625C19.0463 10.4916 19.0439 10.4164 19.0415 10.3396ZM4.08109 10.946C4.09069 10.8213 4.10029 10.6966 4.10986 10.5719C4.23647 10.5708 4.36328 10.5711 4.49033 10.5715C4.71918 10.5722 4.94881 10.5729 5.17939 10.5648C5.16609 10.9361 5.13572 11.3067 5.10535 11.6773C5.0858 11.9158 5.06626 12.1543 5.05127 12.393C5.00596 13.1094 4.84346 13.8469 4.40596 14.432C4.21065 14.018 4.02783 13.6 3.84502 13.182C3.90146 13.0298 3.93617 12.8705 3.94814 12.7086C3.99068 12.1208 4.03587 11.5336 4.08109 10.946ZM7.87002 12.5539L7.71377 10.5719H7.71143C8.0708 10.5703 8.4307 10.5706 8.79111 10.5727C8.80478 10.7501 8.81855 10.9276 8.83231 11.1051C8.87363 11.6378 8.91494 12.1705 8.95361 12.7031C8.96689 12.8662 9.00373 13.0264 9.06299 13.1789C8.88018 13.5984 8.6958 14.0156 8.50283 14.4289C8.09815 13.8914 7.92705 13.2164 7.87002 12.5539ZM10.1765 14.9284C9.79792 14.9403 9.41903 14.9522 9.0544 14.8445H9.05205C9.21663 14.4799 9.38408 14.1167 9.55439 13.7547C9.93595 13.8526 10.3274 13.8453 10.7187 13.8381C10.8987 13.8347 11.0786 13.8314 11.2575 13.8383C11.095 14.2023 10.9286 14.5648 10.7614 14.9266C10.5679 14.9162 10.3722 14.9223 10.1765 14.9284Z"
        fill="currentColor"
      />
    </svg>
  )
}
