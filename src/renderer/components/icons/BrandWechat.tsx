import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react'

export default function BrandWechat(props: { className?: string; onClick?: <PERSON><PERSON><PERSON>Handler<SVGSVGElement> }) {
  const { className, onClick } = props
  return (
    <svg className={className} onClick={onClick} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18.133 13.7056C18.3543 13.7027 18.5657 13.6135 18.7221 13.457C18.8786 13.3005 18.9678 13.089 18.9707 12.8677C18.971 12.7576 18.9496 12.6485 18.9077 12.5466C18.8657 12.4448 18.804 12.3523 18.7262 12.2744C18.6483 12.1966 18.5558 12.1349 18.454 12.0929C18.3522 12.0509 18.2431 12.0295 18.133 12.0299C18.0229 12.0294 17.9137 12.0507 17.8119 12.0926C17.71 12.1346 17.6175 12.1963 17.5396 12.2742C17.4617 12.3521 17.4 12.4446 17.3581 12.5465C17.3162 12.6484 17.2948 12.7575 17.2953 12.8677C17.2953 13.3342 17.6685 13.7056 18.133 13.7056ZM14.0053 13.7056C14.2266 13.7027 14.4379 13.6135 14.5944 13.457C14.7509 13.3005 14.8401 13.089 14.843 12.8677C14.843 12.4031 14.4698 12.0299 14.0053 12.0299C13.8952 12.0294 13.786 12.0507 13.6842 12.0926C13.5823 12.1346 13.4898 12.1963 13.4119 12.2742C13.334 12.3521 13.2723 12.4446 13.2304 12.5465C13.1885 12.6484 13.1671 12.7575 13.1676 12.8677C13.1676 13.3342 13.5398 13.7056 14.0053 13.7056ZM20.1311 18.408C20.0716 18.4416 20.0242 18.4929 19.9954 18.5548C19.9666 18.6168 19.958 18.6862 19.9707 18.7533C19.9707 18.798 19.9707 18.8447 19.994 18.8904C20.0854 19.2795 20.2682 19.899 20.2682 19.9223C20.2682 19.9904 20.2915 20.0362 20.2915 20.0828C20.2915 20.1099 20.2862 20.1366 20.2758 20.1616C20.2655 20.1866 20.2503 20.2092 20.2311 20.2283C20.212 20.2474 20.1892 20.2625 20.1642 20.2727C20.1392 20.283 20.1124 20.2882 20.0854 20.2881C20.0387 20.2881 20.0164 20.2657 19.9707 20.2433L18.619 19.4633C18.5203 19.409 18.4106 19.3777 18.2981 19.3719C18.23 19.3719 18.161 19.3719 18.1153 19.3942C17.4735 19.5781 16.8093 19.6695 16.0995 19.6695C12.6854 19.6695 9.93635 17.377 9.93635 14.5332C9.93635 11.6893 12.6854 9.39685 16.0995 9.39685C19.5126 9.39685 22.2616 11.6902 22.2616 14.5332C22.2616 16.0699 21.437 17.4685 20.1311 18.409M16.3597 8.46942C16.2727 8.46651 16.1856 8.46496 16.0985 8.46476C12.205 8.46476 9.00354 11.1332 9.00354 14.5341C9.00354 15.051 9.07816 15.5511 9.21622 16.0269H9.1332C8.31819 16.0183 7.50782 15.9029 6.7228 15.6836C6.65378 15.6603 6.58475 15.6603 6.51572 15.6603C6.37753 15.6629 6.24258 15.7026 6.12487 15.775L4.49524 16.7127C4.45332 16.7385 4.40613 16.7544 4.35718 16.7594C4.29061 16.7586 4.22697 16.7319 4.1799 16.6848C4.13282 16.6377 4.10605 16.574 4.10532 16.5075C4.10532 16.4384 4.12771 16.3927 4.15103 16.3236C4.17342 16.3013 4.3805 15.5455 4.49524 15.0883C4.49524 15.0417 4.51763 14.9736 4.51763 14.9278C4.51695 14.8481 4.49802 14.7695 4.46229 14.6982C4.42655 14.6269 4.37496 14.5647 4.31148 14.5164C2.72662 13.393 1.73877 11.7229 1.73877 9.86896C1.7397 6.45875 5.06985 3.71191 9.15559 3.71191C12.6676 3.71191 15.62 5.73565 16.3597 8.46849M11.552 8.85849C12.0865 8.85849 12.5091 8.41344 12.5091 7.90121C12.5091 7.36658 12.0865 6.94392 11.552 6.94392C11.0175 6.94392 10.5949 7.36658 10.5949 7.90121C10.5949 8.43583 11.0175 8.85849 11.552 8.85849ZM6.64538 8.85849C7.17988 8.85849 7.60338 8.41344 7.60338 7.90121C7.60338 7.36658 7.17988 6.94392 6.64538 6.94392C6.11181 6.94392 5.68831 7.36658 5.68831 7.90121C5.68831 8.43583 6.11181 8.85849 6.64538 8.85849Z"
        fill="currentColor"
      />
    </svg>
  )
}
