import React, { useState } from 'react'
import { Button, Group, Card, Stack, Text, Title } from '@mantine/core'
import { IconBug, IconAlertTriangle } from '@tabler/icons-react'

// Composant pour tester la nouvelle interface d'erreur
export function ErrorTestButton() {
  const [shouldError, setShouldError] = useState(false)

  if (shouldError) {
    // Simuler une erreur "cannot read properties of undefined"
    const obj: any = null
    return <div>{obj.nonExistentProperty.anotherProperty}</div>
  }

  const testGlobalError = () => {
    setTimeout(() => {
      throw new Error('Test global error handler - this error is intentional for testing the new UI')
    }, 100)
  }

  const testUnhandledPromise = () => {
    Promise.reject(new Error('Test unhandled promise rejection - this error is intentional for testing'))
  }

  return (
    <Card padding="md" radius="md" withBorder>
      <Stack gap="md">
        <Group gap="xs">
          <IconBug size={20} color="var(--mantine-color-orange-6)" />
          <Title order={4}>Test de l'interface d'erreur améliorée</Title>
        </Group>
        
        <Text size="sm" c="dimmed">
          Testez la nouvelle interface d'erreur qui respecte la charte graphique DataTec
        </Text>

        <Group gap="sm">
          <Button
            leftSection={<IconAlertTriangle size={16} />}
            onClick={() => setShouldError(true)}
            variant="filled"
            color="red"
            size="sm"
          >
            Test React Error
          </Button>
          
          <Button
            leftSection={<IconAlertTriangle size={16} />}
            onClick={testGlobalError}
            variant="light"
            color="orange"
            size="sm"
          >
            Test Global Error
          </Button>
          
          <Button
            leftSection={<IconAlertTriangle size={16} />}
            onClick={testUnhandledPromise}
            variant="subtle"
            color="yellow"
            size="sm"
          >
            Test Promise Error
          </Button>
        </Group>
      </Stack>
    </Card>
  )
}
