import React, { useState } from 'react'
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  InputAdornment,
  IconButton,
  Alert,
  CircularProgress,
  useTheme,
  alpha,
} from '@mui/material'
import {
  Visibility,
  VisibilityOff,
  Person,
  Lock,
  Login as LoginIcon,
} from '@mui/icons-material'
import { useAtomValue } from 'jotai'
import { realThemeAtom } from '@/stores/atoms'
import { useIsSmallScreen } from '@/hooks/useScreenChange'

interface LoginScreenProps {
  onLogin: (username: string, password: string) => Promise<boolean>
  isLoading?: boolean
  error?: string
}

export default function LoginScreen({ onLogin, isLoading = false, error }: LoginScreenProps) {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [localError, setLocalError] = useState('')
  
  const theme = useTheme()
  const realTheme = useAtomValue(realThemeAtom)
  const isSmallScreen = useIsSmallScreen()
  const isDark = realTheme === 'dark'

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLocalError('')

    if (!username.trim()) {
      setLocalError('Nom d\'utilisateur requis')
      return
    }

    if (!password.trim()) {
      setLocalError('Mot de passe requis')
      return
    }

    try {
      const success = await onLogin(username.trim(), password)
      if (!success) {
        setLocalError('Nom d\'utilisateur ou mot de passe incorrect')
      }
    } catch (err) {
      setLocalError('Erreur de connexion. Veuillez réessayer.')
    }
  }

  const handleClickShowPassword = () => setShowPassword(!showPassword)

  const displayError = error || localError

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: isDark
          ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
          : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        padding: 2,
      }}
    >
      <Paper
        elevation={isDark ? 8 : 4}
        sx={{
          padding: isSmallScreen ? 3 : 4,
          width: '100%',
          maxWidth: 400,
          borderRadius: 3,
          background: isDark
            ? alpha(theme.palette.background.paper, 0.95)
            : alpha(theme.palette.background.paper, 0.98),
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        }}
      >
        {/* Logo et titre */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Box
            sx={{
              width: 80,
              height: 80,
              margin: '0 auto 16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <img
              src={isDark ? 'static/logo-light.svg' : 'static/logo-dark.svg'}
              alt="DataTec Logo"
              style={{
                width: '80px',
                height: '80px',
                objectFit: 'contain',
              }}
            />
          </Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontWeight: 600,
              color: theme.palette.text.primary,
              mb: 1,
            }}
          >
            DataTec Workspace
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.text.secondary,
              opacity: 0.8,
            }}
          >
            Connectez-vous pour continuer
          </Typography>
        </Box>

        {/* Formulaire de connexion */}
        <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
          {displayError && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: '0.875rem',
                },
              }}
            >
              {displayError}
            </Alert>
          )}

          <TextField
            fullWidth
            label="Nom d'utilisateur"
            variant="outlined"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            disabled={isLoading}
            sx={{ mb: 3 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Person sx={{ color: theme.palette.text.secondary }} />
                </InputAdornment>
              ),
            }}
            autoComplete="username"
            autoFocus={!isSmallScreen}
          />

          <TextField
            fullWidth
            label="Mot de passe"
            type={showPassword ? 'text' : 'password'}
            variant="outlined"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
            sx={{ mb: 4 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock sx={{ color: theme.palette.text.secondary }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    disabled={isLoading}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            autoComplete="current-password"
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={isLoading || !username.trim() || !password.trim()}
            startIcon={
              isLoading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <LoginIcon />
              )
            }
            sx={{
              py: 1.5,
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 600,
              backgroundColor: 'var(--mantine-color-chatbox-brand-filled)',
              color: 'var(--mantine-color-chatbox-brand-filled-text)',
              border: 'none',
              boxShadow: isDark
                ? '0 4px 20px rgba(0, 123, 255, 0.3)'
                : '0 4px 20px rgba(0, 123, 255, 0.2)',
              '&:hover': {
                backgroundColor: 'var(--mantine-color-chatbox-brand-filled-hover)',
                boxShadow: isDark
                  ? '0 6px 25px rgba(0, 123, 255, 0.4)'
                  : '0 6px 25px rgba(0, 123, 255, 0.3)',
              },
              '&:disabled': {
                backgroundColor: 'var(--mantine-color-chatbox-background-disabled)',
                color: 'var(--mantine-color-chatbox-tertiary-text)',
                boxShadow: 'none',
              },
            }}
          >
            {isLoading ? 'Connexion...' : 'Se connecter'}
          </Button>
        </Box>

        {/* Footer */}
        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography
            variant="caption"
            sx={{
              color: theme.palette.text.secondary,
              opacity: 0.6,
            }}
          >
            © 2024 DataTec Workspace. Tous droits réservés.
          </Typography>
        </Box>
      </Paper>
    </Box>
  )
}
