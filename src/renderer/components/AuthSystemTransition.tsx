// Composant de transition entre l'ancien et le nouveau système d'authentification
// Gère le basculement progressif et le fallback

import React, { useEffect, useState } from 'react'
import { useAtom } from 'jotai'
import { 
  newAuth<PERSON>tom, 
  authInitializationAtom, 
  authDebugInfoAtom,
  switchToNewAuthSystem 
} from '../stores/atoms/newAuthAtoms'
import { authAtom } from '../stores/atoms/authAtoms' // Ancien système
import { migrationService } from '../services/MigrationService'
import { userManagementService } from '../services/UserManagementService'

interface AuthSystemTransitionProps {
  children: React.ReactNode
  enableNewSystem?: boolean
  showDebugInfo?: boolean
}

export const AuthSystemTransition: React.FC<AuthSystemTransitionProps> = ({
  children,
  enableNewSystem = true,
  showDebugInfo = false
}) => {
  const [newAuth, setNewAuth] = useAtom(newAuth<PERSON>tom)
  const [oldAuth] = useAtom(authAtom)
  const [, initializeAuth] = useAtom(authInitializationAtom)
  const [debugInfo] = useAtom(authDebugInfoAtom)
  
  const [transitionState, setTransitionState] = useState<{
    phase: 'checking' | 'migrating' | 'ready' | 'error' | 'fallback'
    message: string
    progress?: number
    error?: string
  }>({
    phase: 'checking',
    message: 'Vérification du système d\'authentification...'
  })

  const [useNewSystem, setUseNewSystem] = useState(enableNewSystem)

  useEffect(() => {
    initializeTransition()
  }, [])

  const initializeTransition = async () => {
    try {
      if (!enableNewSystem) {
        setTransitionState({
          phase: 'fallback',
          message: 'Utilisation de l\'ancien système d\'authentification'
        })
        return
      }

      setTransitionState({
        phase: 'checking',
        message: 'Vérification du système...'
      })

      // Vérifier si le nouveau système peut être utilisé
      const canUseNewSystem = await switchToNewAuthSystem()
      
      if (!canUseNewSystem) {
        console.log('⚠️ Impossible d\'utiliser le nouveau système, fallback vers l\'ancien')
        setUseNewSystem(false)
        setTransitionState({
          phase: 'fallback',
          message: 'Utilisation de l\'ancien système (fallback)'
        })
        return
      }

      setTransitionState({
        phase: 'migrating',
        message: 'Initialisation du nouveau système...',
        progress: 50
      })

      // Initialiser le nouveau système
      await initializeAuth()

      setTransitionState({
        phase: 'ready',
        message: 'Nouveau système d\'authentification prêt',
        progress: 100
      })

      console.log('✅ Transition vers le nouveau système terminée')

    } catch (error) {
      console.error('❌ Erreur lors de la transition:', error)
      
      setTransitionState({
        phase: 'error',
        message: 'Erreur lors de la transition',
        error: `${error}`
      })

      // Fallback vers l'ancien système après 3 secondes
      setTimeout(() => {
        setUseNewSystem(false)
        setTransitionState({
          phase: 'fallback',
          message: 'Fallback vers l\'ancien système après erreur'
        })
      }, 3000)
    }
  }

  const retryTransition = async () => {
    await initializeTransition()
  }

  const forceOldSystem = () => {
    setUseNewSystem(false)
    setTransitionState({
      phase: 'fallback',
      message: 'Basculement forcé vers l\'ancien système'
    })
  }

  const forceNewSystem = async () => {
    setUseNewSystem(true)
    await initializeTransition()
  }

  // Affichage de l'écran de transition
  if (useNewSystem && transitionState.phase !== 'ready') {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: 'var(--background-color, #282828)',
        color: 'var(--text-color, white)',
        fontFamily: 'Arial, sans-serif',
        padding: '20px'
      }}>
        <div style={{
          textAlign: 'center',
          maxWidth: '500px'
        }}>
          {/* Logo ou icône */}
          <div style={{
            fontSize: '48px',
            marginBottom: '20px'
          }}>
            {transitionState.phase === 'checking' && '🔍'}
            {transitionState.phase === 'migrating' && '🔄'}
            {transitionState.phase === 'error' && '❌'}
            {transitionState.phase === 'fallback' && '⚠️'}
          </div>

          {/* Message principal */}
          <h2 style={{ marginBottom: '10px' }}>
            DataTec - Système d'Authentification
          </h2>
          
          <p style={{ 
            marginBottom: '20px',
            opacity: 0.8
          }}>
            {transitionState.message}
          </p>

          {/* Barre de progression */}
          {transitionState.progress !== undefined && (
            <div style={{
              width: '100%',
              height: '4px',
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: '2px',
              marginBottom: '20px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${transitionState.progress}%`,
                height: '100%',
                backgroundColor: '#667eea',
                transition: 'width 0.3s ease'
              }} />
            </div>
          )}

          {/* Erreur */}
          {transitionState.error && (
            <div style={{
              backgroundColor: 'rgba(244, 67, 54, 0.1)',
              border: '1px solid rgba(244, 67, 54, 0.3)',
              borderRadius: '4px',
              padding: '10px',
              marginBottom: '20px',
              fontSize: '14px'
            }}>
              {transitionState.error}
            </div>
          )}

          {/* Actions */}
          <div style={{
            display: 'flex',
            gap: '10px',
            justifyContent: 'center',
            flexWrap: 'wrap'
          }}>
            {transitionState.phase === 'error' && (
              <>
                <button
                  onClick={retryTransition}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#667eea',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Réessayer
                </button>
                <button
                  onClick={forceOldSystem}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    color: 'white',
                    border: '1px solid rgba(255,255,255,0.3)',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  Ancien Système
                </button>
              </>
            )}

            {transitionState.phase === 'fallback' && enableNewSystem && (
              <button
                onClick={forceNewSystem}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#667eea',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Essayer le Nouveau Système
              </button>
            )}
          </div>

          {/* Informations de debug */}
          {showDebugInfo && (
            <details style={{
              marginTop: '20px',
              textAlign: 'left',
              fontSize: '12px',
              opacity: 0.7
            }}>
              <summary style={{ cursor: 'pointer' }}>
                Informations de Debug
              </summary>
              <pre style={{
                backgroundColor: 'rgba(0,0,0,0.3)',
                padding: '10px',
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '200px',
                marginTop: '10px'
              }}>
                {JSON.stringify({
                  transitionState,
                  useNewSystem,
                  newAuthInitialized: newAuth.initialized,
                  oldAuthUser: oldAuth.user?.username,
                  timestamp: new Date().toISOString()
                }, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    )
  }

  // Système prêt - afficher l'application
  return (
    <div data-auth-system={useNewSystem ? 'new' : 'old'}>
      {children}
      
      {/* Indicateur discret du système utilisé */}
      {showDebugInfo && (
        <div style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          backgroundColor: useNewSystem ? '#4CAF50' : '#FF9800',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '10px',
          zIndex: 9999,
          opacity: 0.7
        }}>
          Auth: {useNewSystem ? 'Nouveau' : 'Ancien'}
        </div>
      )}
    </div>
  )
}

export default AuthSystemTransition
