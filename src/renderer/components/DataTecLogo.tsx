// Composant Logo DataTec uniforme
// Garantit que le logo est identique partout dans l'application

import React from 'react'

export interface DataTecLogoProps {
  size?: number
  variant?: 'default' | 'gradient' | 'monochrome' | 'favicon'
  className?: string
  style?: React.CSSProperties
}

export const DataTecLogo: React.FC<DataTecLogoProps> = ({
  size = 64,
  variant = 'default',
  className,
  style
}) => {
  const getLogoContent = () => {
    switch (variant) {
      case 'gradient':
        return (
          <>
            <rect width="100" height="100" rx="22" fill="url(#gradient)"/>
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style={{stopColor:'#667eea'}} />
                <stop offset="100%" style={{stopColor:'#764ba2'}} />
              </linearGradient>
            </defs>
            <g transform="translate(50, 50)">
              {/* Logo DataTec exact - 3 barres avec points colorés */}
              <rect x="-28" y="-15" width="56" height="8" rx="4" fill="white" fillOpacity="0.9"/>
              <circle cx="-18" cy="-11" r="5" fill="#4CAF50"/>
              
              <rect x="-28" y="-3" width="56" height="8" rx="4" fill="white" fillOpacity="0.9"/>
              <circle cx="-18" cy="1" r="5" fill="#2196F3"/>
              
              <rect x="-28" y="9" width="56" height="8" rx="4" fill="white" fillOpacity="0.9"/>
              <circle cx="-18" cy="13" r="5" fill="#F44336"/>
            </g>
          </>
        )
      
      case 'monochrome':
        return (
          <>
            <rect width="100" height="100" rx="22" fill="currentColor" fillOpacity="0.1"/>
            <g transform="translate(50, 50)">
              <rect x="-28" y="-15" width="56" height="8" rx="4" fill="currentColor"/>
              <circle cx="-18" cy="-11" r="5" fill="currentColor" fillOpacity="0.7"/>
              
              <rect x="-28" y="-3" width="56" height="8" rx="4" fill="currentColor"/>
              <circle cx="-18" cy="1" r="5" fill="currentColor" fillOpacity="0.7"/>
              
              <rect x="-28" y="9" width="56" height="8" rx="4" fill="currentColor"/>
              <circle cx="-18" cy="13" r="5" fill="currentColor" fillOpacity="0.7"/>
            </g>
          </>
        )
      
      case 'favicon':
        return (
          <>
            {/* Version favicon adaptative */}
            <rect width="100" height="100" rx="22" fill="#f5f5f5"/>
            <g transform="translate(50, 50)">
              <rect x="-28" y="-15" width="56" height="8" rx="4" fill="#2c2c2c"/>
              <circle cx="-18" cy="-11" r="5" fill="#4CAF50"/>
              
              <rect x="-28" y="-3" width="56" height="8" rx="4" fill="#2c2c2c"/>
              <circle cx="-18" cy="1" r="5" fill="#2196F3"/>
              
              <rect x="-28" y="9" width="56" height="8" rx="4" fill="#2c2c2c"/>
              <circle cx="-18" cy="13" r="5" fill="#F44336"/>
            </g>
            <style>
              {`
                @media (prefers-color-scheme: dark) {
                  rect:first-child { fill: #2a2a2a; }
                  rect:not(:first-child) { fill: #ffffff; }
                }
              `}
            </style>
          </>
        )
      
      default:
        return (
          <>
            {/* Version par défaut avec fond adaptatif */}
            <rect 
              width="100" 
              height="100" 
              rx="22" 
              fill="var(--logo-bg, #f5f5f5)"
            />
            <g transform="translate(50, 50)">
              <rect 
                x="-28" 
                y="-15" 
                width="56" 
                height="8" 
                rx="4" 
                fill="var(--logo-bars, #2c2c2c)"
              />
              <circle cx="-18" cy="-11" r="5" fill="#4CAF50"/>
              
              <rect 
                x="-28" 
                y="-3" 
                width="56" 
                height="8" 
                rx="4" 
                fill="var(--logo-bars, #2c2c2c)"
              />
              <circle cx="-18" cy="1" r="5" fill="#2196F3"/>
              
              <rect 
                x="-28" 
                y="9" 
                width="56" 
                height="8" 
                rx="4" 
                fill="var(--logo-bars, #2c2c2c)"
              />
              <circle cx="-18" cy="13" r="5" fill="#F44336"/>
            </g>
          </>
        )
    }
  }

  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 100 100" 
      fill="none"
      className={className}
      style={style}
      xmlns="http://www.w3.org/2000/svg"
    >
      {getLogoContent()}
    </svg>
  )
}

// Composant avec texte
export interface DataTecLogoWithTextProps extends DataTecLogoProps {
  title?: string
  subtitle?: string
  layout?: 'horizontal' | 'vertical'
  textColor?: string
}

export const DataTecLogoWithText: React.FC<DataTecLogoWithTextProps> = ({
  size = 64,
  variant = 'default',
  title = 'DataTec Workspace',
  subtitle = 'Système de gestion des données',
  layout = 'vertical',
  textColor = 'currentColor',
  className,
  style
}) => {
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: layout === 'horizontal' ? '16px' : '12px',
    flexDirection: layout === 'horizontal' ? 'row' : 'column',
    textAlign: layout === 'horizontal' ? 'left' : 'center',
    ...style
  }

  const titleStyle: React.CSSProperties = {
    margin: 0,
    fontSize: size > 48 ? '24px' : '18px',
    fontWeight: 600,
    color: textColor,
    lineHeight: 1.2
  }

  const subtitleStyle: React.CSSProperties = {
    margin: 0,
    fontSize: size > 48 ? '14px' : '12px',
    color: textColor,
    opacity: 0.7,
    lineHeight: 1.3
  }

  return (
    <div className={className} style={containerStyle}>
      <DataTecLogo size={size} variant={variant} />
      <div>
        <h1 style={titleStyle}>{title}</h1>
        {subtitle && <p style={subtitleStyle}>{subtitle}</p>}
      </div>
    </div>
  )
}

// Hook pour les variables CSS du logo
export const useLogoTheme = () => {
  React.useEffect(() => {
    const updateLogoTheme = () => {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const root = document.documentElement
      
      if (isDark) {
        root.style.setProperty('--logo-bg', '#2a2a2a')
        root.style.setProperty('--logo-bars', '#ffffff')
      } else {
        root.style.setProperty('--logo-bg', '#f5f5f5')
        root.style.setProperty('--logo-bars', '#2c2c2c')
      }
    }

    // Mettre à jour au chargement
    updateLogoTheme()

    // Écouter les changements de thème
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', updateLogoTheme)

    return () => {
      mediaQuery.removeEventListener('change', updateLogoTheme)
    }
  }, [])
}

// Composant Logo adaptatif au thème
export const AdaptiveDataTecLogo: React.FC<DataTecLogoProps> = (props) => {
  useLogoTheme()
  return <DataTecLogo {...props} />
}

export default DataTecLogo
