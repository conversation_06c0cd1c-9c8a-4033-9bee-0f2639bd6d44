import NiceModal from '@ebay/nice-modal-react'
import EditIcon from '@mui/icons-material/Edit'
import ImageIcon from '@mui/icons-material/Image'
import { Box, Chip, IconButton, Tooltip, Typography, useTheme } from '@mui/material'
import { useAtom, useAtomValue } from 'jotai'
import { PanelRightClose, Settings2 } from 'lucide-react'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'
import { isChatSession, isPictureSession } from '../../shared/types'
import useNeedRoomForWinControls from '../hooks/useNeedRoomForWinControls'
import { useIsSmallScreen } from '../hooks/useScreenChange'
import * as atoms from '../stores/atoms'
import * as sessionActions from '../stores/sessionActions'
import * as settingActions from '../stores/settingActions'
import MiniButton from './MiniButton'
import Toolbar from './Toolbar'


export default function Header() {
  const { t } = useTranslation()
  const theme = useTheme()
  const currentSession = useAtomValue(atoms.currentSessionAtom)
  const [showSidebar, setShowSidebar] = useAtom(atoms.showSidebarAtom)


  const isSmallScreen = useIsSmallScreen()

  const { needRoomForMacWindowControls, needRoomForWindowsWindowControls } = useNeedRoomForWinControls()



  // 会话名称自动生成
  useEffect(() => {
    if (!currentSession) {
      return
    }
    const autoGenerateTitle = settingActions.getAutoGenerateTitle()
    if (!autoGenerateTitle) {
      return
    }
    if (currentSession.name === 'Untitled' && currentSession.messages.length >= 2) {
      sessionActions.generateNameAndThreadName(currentSession.id)
      return // 生成了会话名称，就不再生成 thread 名称
    }
    if (!currentSession.threadName && currentSession.messages.length >= 2) {
      sessionActions.generateThreadName(currentSession.id)
    }
  }, [currentSession?.messages.length])

  const editCurrentSession = () => {
    if (!currentSession) {
      return
    }
    NiceModal.show('session-settings', { session: currentSession })
  }

  let EditButton: React.ReactNode | null = null
  if (currentSession && isChatSession(currentSession) && currentSession.settings) {
    EditButton = (
      <Tooltip title={t('Current conversation configured with specific model settings')} className="cursor-pointer">
        <EditIcon
          className="ml-1 cursor-pointer w-4 h-4 opacity-30"
          fontSize="small"
          style={{ color: theme.palette.warning.main }}
        />
      </Tooltip>
    )
  } else if (currentSession && isPictureSession(currentSession)) {
    EditButton = (
      <Tooltip
        title={t('The Image Creator plugin has been activated for the current conversation')}
        className="cursor-pointer"
      >
        <Chip
          className="ml-2 cursor-pointer"
          variant="outlined"
          color="secondary"
          size="small"
          icon={<ImageIcon className="cursor-pointer" />}
          label={<span className="cursor-pointer">{t('Image Creator')}</span>}
        />
      </Tooltip>
    )
  } else {
    EditButton = <EditIcon className="ml-1 cursor-pointer w-4 h-4 opacity-30" fontSize="small" />
  }

  return (
    <div
      className={cn(
        'flex flex-row items-center pt-1',
        isSmallScreen ? '' : showSidebar ? 'sm:pl-3 sm:pr-0' : 'pr-0',
        (!showSidebar || isSmallScreen) && needRoomForMacWindowControls ? 'pl-20' : 'pl-3'
      )}
      style={{
        borderBottomWidth: '1px',
        borderBottomStyle: 'solid',
        borderBottomColor: theme.palette.divider,
      }}
    >
      {(!showSidebar || isSmallScreen) && (
        <Box onClick={() => setShowSidebar(!showSidebar)}>
          <IconButton
            sx={
              isSmallScreen
                ? {
                    borderColor: theme.palette.action.hover,
                    borderStyle: 'solid',
                    borderWidth: 1,
                  }
                : {}
            }
          >
            <PanelRightClose size="20" strokeWidth={1.5} />
          </IconButton>
        </Box>
      )}

      {/* 固定高度，和 Windows 的 win controls bar 高度一致 */}
      <div className={cn('title-bar w-full flex flex-row items-center justify-between', 'py-2 h-12')}>
        {/* Titre/Nom de session à gauche */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <Typography
              variant="h6"
              color="inherit"
              component="div"
              noWrap
              sx={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: isSmallScreen ? '12rem' : '18rem',
              }}
              className={cn('flex items-center', showSidebar ? 'ml-3' : 'ml-1')}
            >
              {currentSession?.name}
            </Typography>

            {/* Boutons d'édition */}
            {isSmallScreen ? (
              <MiniButton
                className="ml-1 sm:ml-2 controls cursor-pointer"
                style={{ color: theme.palette.text.secondary }}
                onClick={() => {
                  editCurrentSession()
                }}
                tooltipTitle={
                  <div className="text-center inline-block">
                    <span>{t('Customize settings for the current conversation')}</span>
                  </div>
                }
                tooltipPlacement="top"
              >
                <Settings2 size="16" strokeWidth={1} />
              </MiniButton>
            ) : (
              <a
                onClick={() => {
                  editCurrentSession()
                }}
                className="controls flex ml-2 cursor-pointer"
              >
                {EditButton}
              </a>
            )}
          </div>
        </div>

        {/* Toolbar à droite avec espace pour l'avatar fixe */}
        <div className={cn('flex items-center justify-end', needRoomForWindowsWindowControls ? 'mr-36' : 'mr-16')}>
          <Toolbar />
        </div>
      </div>
    </div>
  )
}
