import { 
  Box, 
  Flex
} from '@mantine/core'
import { useState } from 'react'
import CategoryPanel from './CategoryPanel'
import KnowledgeBaseSimpleList from './KnowledgeBaseSimpleList'
import { useKnowledgeBase } from '../hooks/useKnowledgeBase'

interface CopilotsCategoryInterfaceProps {
  onSelect?: (kb: any) => void
}

export default function CopilotsCategoryInterface({ 
  onSelect 
}: CopilotsCategoryInterfaceProps) {
  const { knowledgeBases, loading } = useKnowledgeBase()
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('general')

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategoryId(categoryId)
  }

  const handleKnowledgeBaseSelect = (kb: any) => {
    console.log('Base de connaissances sélectionnée:', kb)
    onSelect?.(kb)
  }

  return (
    <Flex style={{ height: '100%', width: '100%' }}>
      {/* Panneau gauche - Catégories */}
      <CategoryPanel
        selectedCategoryId={selectedCategoryId}
        onCategorySelect={handleCategorySelect}
      />

      {/* Panneau droit - Bases de connaissances */}
      <Box
        style={{
          flex: 1,
          padding: '1rem',
          backgroundColor: 'var(--mantine-color-dark-8)',
          height: '100%',
          overflow: 'auto'
        }}
      >
        <KnowledgeBaseSimpleList
          knowledgeBases={knowledgeBases}
          onSelect={handleKnowledgeBaseSelect}
          loading={loading}
          selectedCategoryId={selectedCategoryId}
        />
      </Box>
    </Flex>
  )
}
