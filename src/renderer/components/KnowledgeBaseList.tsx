import { 
  Box, 
  Button, 
  Flex, 
  Stack, 
  Text, 
  Badge,
  ActionIcon,
  Group,
  Card,
  Indicator
} from '@mantine/core'
import { 
  IconPlus, 
  IconEdit,
  IconTrash,
  IconCircle,
  IconCircleFilled
} from '@tabler/icons-react'
import { KnowledgeBaseData } from '../hooks/useKnowledgeBase'

interface KnowledgeBaseListProps {
  knowledgeBases: KnowledgeBaseData[]
  onAdd: () => void
  onEdit: (kb: KnowledgeBaseData) => void
  onDelete: (id: string) => void
  loading?: boolean
}

export default function KnowledgeBaseList({ 
  knowledgeBases, 
  onAdd, 
  onEdit, 
  onDelete,
  loading = false 
}: KnowledgeBaseListProps) {

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'green' : 'gray'
  }

  const getStatusText = (isActive: boolean) => {
    return isActive ? 'Activé' : 'Désactivé'
  }

  return (
    <Box>
      {/* Header avec bouton Ajouter */}
      <Flex justify="space-between" align="center" mb="lg">
        <Text size="lg" fw={500} c="var(--mantine-color-gray-0)">
          Bases de connaissances ({knowledgeBases.length})
        </Text>
        <Button
          leftSection={<IconPlus size={14} />}
          variant="outline"
          size="xs"
          color="blue"
          onClick={onAdd}
          loading={loading}
          styles={{
            root: {
              borderColor: 'var(--mantine-color-blue-6)',
              color: 'var(--mantine-color-blue-4)',
              fontSize: '0.75rem',
              '&:hover': {
                backgroundColor: 'var(--mantine-color-blue-9)',
                borderColor: 'var(--mantine-color-blue-5)',
              },
            },
          }}
        >
          Ajouter
        </Button>
      </Flex>

      {/* Liste des bases de connaissances */}
      <Stack gap="sm">
        {knowledgeBases.map((kb) => (
          <Card
            key={kb.id}
            padding="md"
            style={{
              backgroundColor: 'var(--mantine-color-dark-6)',
              border: '1px solid var(--mantine-color-dark-4)',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: 'var(--mantine-color-dark-5)',
                borderColor: 'var(--mantine-color-blue-6)',
              }
            }}
            onClick={() => onEdit(kb)}
          >
            <Flex justify="space-between" align="center">
              {/* Informations principales */}
              <Flex align="center" gap="md" style={{ flex: 1 }}>
                {/* Indicateur de statut */}
                <Box style={{ position: 'relative' }}>
                  {kb.isActive ? (
                    <IconCircleFilled 
                      size={12} 
                      color="var(--mantine-color-green-5)" 
                    />
                  ) : (
                    <IconCircle 
                      size={12} 
                      color="var(--mantine-color-gray-5)" 
                    />
                  )}
                </Box>

                {/* Nom et description */}
                <Box style={{ flex: 1 }}>
                  <Text 
                    size="sm" 
                    fw={500} 
                    c="var(--mantine-color-gray-0)"
                    mb={2}
                  >
                    {kb.name}
                  </Text>
                  {kb.description && (
                    <Text 
                      size="xs" 
                      c="var(--mantine-color-gray-4)"
                      lineClamp={1}
                    >
                      {kb.description}
                    </Text>
                  )}
                </Box>

                {/* Badge de statut */}
                <Badge
                  size="sm"
                  color={getStatusColor(kb.isActive)}
                  variant="light"
                  styles={{
                    root: {
                      backgroundColor: kb.isActive 
                        ? 'var(--mantine-color-green-9)' 
                        : 'var(--mantine-color-gray-8)',
                      color: kb.isActive 
                        ? 'var(--mantine-color-green-4)' 
                        : 'var(--mantine-color-gray-4)',
                      border: `1px solid ${kb.isActive 
                        ? 'var(--mantine-color-green-6)' 
                        : 'var(--mantine-color-gray-6)'}`,
                    }
                  }}
                >
                  {getStatusText(kb.isActive)}
                </Badge>

                {/* Informations supplémentaires */}
                <Group gap="xs">
                  {kb.personalityTags && kb.personalityTags.length > 0 && (
                    <Text size="xs" c="var(--mantine-color-gray-5)">
                      {kb.personalityTags.length} tag{kb.personalityTags.length > 1 ? 's' : ''}
                    </Text>
                  )}
                  {kb.files && kb.files.length > 0 && (
                    <Text size="xs" c="var(--mantine-color-gray-5)">
                      {kb.files.length} fichier{kb.files.length > 1 ? 's' : ''}
                    </Text>
                  )}
                </Group>
              </Flex>

              {/* Actions */}
              <Group gap="xs">
                <ActionIcon
                  variant="subtle"
                  color="blue"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onEdit(kb)
                  }}
                  style={{
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-blue-9)',
                    }
                  }}
                >
                  <IconEdit size={14} />
                </ActionIcon>
                <ActionIcon
                  variant="subtle"
                  color="red"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    if (kb.id && confirm('Êtes-vous sûr de vouloir supprimer cette base de connaissances ?')) {
                      onDelete(kb.id)
                    }
                  }}
                  style={{
                    '&:hover': {
                      backgroundColor: 'var(--mantine-color-red-9)',
                    }
                  }}
                >
                  <IconTrash size={14} />
                </ActionIcon>
              </Group>
            </Flex>

            {/* Informations de date */}
            {kb.createdAt && (
              <Text size="xs" c="var(--mantine-color-gray-6)" mt="xs">
                Créé le {new Date(kb.createdAt).toLocaleDateString('fr-FR', {
                  day: '2-digit',
                  month: '2-digit',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </Text>
            )}
          </Card>
        ))}

        {/* Message si aucune base de connaissances */}
        {knowledgeBases.length === 0 && (
          <Box
            style={{
              backgroundColor: 'var(--mantine-color-dark-7)',
              borderRadius: '8px',
              border: '1px solid var(--mantine-color-dark-4)',
              padding: '2rem',
              textAlign: 'center'
            }}
          >
            <Text size="sm" c="var(--mantine-color-gray-4)" mb="md">
              Aucune base de connaissances configurée
            </Text>
            <Button
              leftSection={<IconPlus size={14} />}
              variant="outline"
              size="sm"
              color="blue"
              onClick={onAdd}
              styles={{
                root: {
                  borderColor: 'var(--mantine-color-blue-6)',
                  color: 'var(--mantine-color-blue-4)',
                  '&:hover': {
                    backgroundColor: 'var(--mantine-color-blue-9)',
                  },
                },
              }}
            >
              Créer votre première base de connaissances
            </Button>
          </Box>
        )}
      </Stack>
    </Box>
  )
}
