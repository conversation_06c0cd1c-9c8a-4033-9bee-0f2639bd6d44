import {
  Box,
  Stack,
  Text,
  UnstyledButton,
  Flex,
  Button,
  Divider
} from '@mantine/core'
import {
  IconFolder,
  IconFolderFilled,
  IconSettings
} from '@tabler/icons-react'
import { useCategories, Category } from '../hooks/useCategories'
import CategoryManagementModal from './CategoryManagementModal'
import { useState, useEffect } from 'react'

interface CategoryPanelProps {
  selectedCategoryId?: string
  onCategorySelect: (categoryId: string) => void
}

export default function CategoryPanel({ 
  selectedCategoryId, 
  onCategorySelect 
}: CategoryPanelProps) {
  const { categories, loadCategories } = useCategories()
  const [selectedId, setSelectedId] = useState<string>(selectedCategoryId || 'general')
  const [managementModalOpened, setManagementModalOpened] = useState(false)

  // Charger les catégories au démarrage
  useEffect(() => {
    loadCategories()
  }, [loadCategories])

  // Écouter les changements de catégories
  useEffect(() => {
    const handleCategoriesChange = () => {
      loadCategories()
    }

    window.addEventListener('categoriesChanged', handleCategoriesChange)

    return () => {
      window.removeEventListener('categoriesChanged', handleCategoriesChange)
    }
  }, [loadCategories])

  // Rafraîchir quand le modal se ferme
  const handleModalClose = () => {
    setManagementModalOpened(false)
    loadCategories() // Rafraîchir la liste
  }

  // Gérer la sélection d'une catégorie
  const handleCategoryClick = (categoryId: string) => {
    setSelectedId(categoryId)
    onCategorySelect(categoryId)
  }

  return (
    <Box
      style={{
        width: '250px',
        height: '100%',
        backgroundColor: 'var(--mantine-color-dark-7)',
        borderRight: '1px solid rgba(255, 255, 255, 0.1)', // Bordure plus subtile
        padding: '20px 16px' // Padding cohérent avec les paramètres
      }}
    >
      {/* Header du panneau */}
      <Text
        size="xs"
        fw={600}
        c="rgba(255, 255, 255, 0.5)"
        mb="lg"
        style={{
          textTransform: 'uppercase',
          letterSpacing: '1px',
          fontSize: '11px',
          marginBottom: '20px'
        }}
      >
        Catégories
      </Text>

      {/* Bouton de gestion des catégories */}
      <Button
        variant="subtle"
        color="gray"
        size="xs"
        leftSection={<IconSettings size={14} />}
        onClick={() => setManagementModalOpened(true)}
        style={{
          marginBottom: '16px',
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: '12px'
        }}
      >
        Gérer les catégories
      </Button>

      <Divider
        color="rgba(255, 255, 255, 0.1)"
        style={{ marginBottom: '16px' }}
      />

      {/* Liste des catégories */}
      <Stack gap="4px">
        {categories.map((category) => (
          <UnstyledButton
            key={category.id}
            onClick={() => handleCategoryClick(category.id)}
            style={{
              width: '100%',
              padding: '12px 16px',
              borderRadius: '6px',
              backgroundColor: selectedId === category.id
                ? 'rgba(34, 139, 230, 0.15)' // Bleu subtil comme dans les paramètres
                : 'transparent',
              border: 'none',
              transition: 'all 0.15s ease-out',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}
            onMouseEnter={(e) => {
              if (selectedId !== category.id) {
                e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)'
              }
            }}
            onMouseLeave={(e) => {
              if (selectedId !== category.id) {
                e.currentTarget.style.backgroundColor = 'transparent'
              }
            }}
          >
            {/* Icône de dossier */}
            {selectedId === category.id ? (
              <IconFolderFilled
                size={18}
                color="rgba(34, 139, 230, 1)" // Bleu cohérent avec les paramètres
                style={{ flexShrink: 0 }}
              />
            ) : (
              <IconFolder
                size={18}
                color="rgba(255, 255, 255, 0.6)" // Gris subtil
                style={{ flexShrink: 0 }}
              />
            )}

            {/* Nom de la catégorie */}
            <Text
              size="sm"
              fw={selectedId === category.id ? 500 : 400}
              c={selectedId === category.id
                ? 'rgba(34, 139, 230, 1)' // Bleu cohérent
                : 'rgba(255, 255, 255, 0.9)' // Blanc légèrement transparent
              }
              style={{
                flex: 1,
                textAlign: 'left',
                fontSize: '14px',
                lineHeight: '20px'
              }}
            >
              {category.name}
            </Text>
          </UnstyledButton>
        ))}
      </Stack>

      {/* Message si aucune catégorie */}
      {categories.length === 0 && (
        <Box
          style={{
            padding: '1rem',
            textAlign: 'center',
            backgroundColor: 'var(--mantine-color-dark-6)',
            borderRadius: '8px',
            border: '1px solid var(--mantine-color-dark-4)'
          }}
        >
          <Text size="xs" c="var(--mantine-color-gray-5)">
            Aucune catégorie disponible
          </Text>
        </Box>
      )}

      {/* Modal de gestion des catégories */}
      <CategoryManagementModal
        opened={managementModalOpened}
        onClose={handleModalClose}
      />
    </Box>
  )
}
