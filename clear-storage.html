<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nettoyage du Stockage - DataTec Workspace</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            margin-bottom: 20px;
            font-size: 28px;
        }

        p {
            margin-bottom: 30px;
            line-height: 1.6;
            opacity: 0.9;
        }

        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .success {
            background: rgba(76, 175, 80, 0.2);
            border-color: rgba(76, 175, 80, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
        }

        .info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid rgba(33, 150, 243, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 14px;
        }

        .warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid rgba(255, 152, 0, 0.5);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Nettoyage du Stockage</h1>
        
        <div class="warning">
            ⚠️ <strong>Attention :</strong> Cette action supprimera toutes les données de l'application DataTec Workspace stockées dans votre navigateur.
        </div>

        <p>
            Cet outil permet de nettoyer complètement le stockage local pour tester l'application avec un état vierge.
        </p>

        <button onclick="clearAllStorage()">
            🗑️ Nettoyer Tout le Stockage
        </button>

        <button onclick="clearOnlySessions()">
            📝 Nettoyer Seulement les Sessions
        </button>

        <button onclick="showStorageInfo()">
            📊 Voir les Informations de Stockage
        </button>

        <button onclick="clearExampleSessionsOnly()">
            🎯 Nettoyer Seulement les Sessions d'Exemple
        </button>

        <div id="success" class="success">
            ✅ Nettoyage effectué avec succès ! Vous pouvez maintenant recharger l'application.
        </div>

        <div class="info">
            <strong>Utilisation :</strong><br>
            1. Cliquez sur "Nettoyer Tout le Stockage"<br>
            2. Rechargez l'application DataTec Workspace<br>
            3. Vous devriez voir l'écran de connexion puis l'écran de bienvenue
        </div>

        <div id="storageInfo" class="info" style="display: none;">
            <h3>📊 Informations de Stockage</h3>
            <div id="storageDetails"></div>
        </div>
    </div>

    <script>
        function clearAllStorage() {
            try {
                // Nettoyer localStorage
                localStorage.clear();
                
                // Nettoyer sessionStorage
                sessionStorage.clear();
                
                // Nettoyer IndexedDB si disponible
                if ('indexedDB' in window) {
                    indexedDB.databases().then(databases => {
                        databases.forEach(db => {
                            indexedDB.deleteDatabase(db.name);
                        });
                    }).catch(e => console.log('IndexedDB cleanup error:', e));
                }

                // Nettoyer les cookies du domaine actuel
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });

                showSuccess();
                console.log('✅ Tout le stockage a été nettoyé');
            } catch (error) {
                console.error('❌ Erreur lors du nettoyage:', error);
                alert('Erreur lors du nettoyage: ' + error.message);
            }
        }

        function clearOnlySessions() {
            try {
                const keysToRemove = [];
                
                // Identifier les clés liées aux sessions
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                        key.includes('session') || 
                        key.includes('chat') || 
                        key.includes('conversation') ||
                        key.includes('ChatSessionsList') ||
                        key.startsWith('session-') ||
                        key.includes('currentSessionId')
                    )) {
                        keysToRemove.push(key);
                    }
                }

                // Supprimer les clés identifiées
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                    console.log('🗑️ Supprimé:', key);
                });

                showSuccess();
                console.log(`✅ ${keysToRemove.length} clés de session supprimées`);
            } catch (error) {
                console.error('❌ Erreur lors du nettoyage des sessions:', error);
                alert('Erreur lors du nettoyage des sessions: ' + error.message);
            }
        }

        function showStorageInfo() {
            const storageInfo = document.getElementById('storageInfo');
            const storageDetails = document.getElementById('storageDetails');
            
            let info = '<strong>LocalStorage:</strong><br>';
            info += `Nombre de clés: ${localStorage.length}<br><br>`;
            
            if (localStorage.length > 0) {
                info += '<strong>Clés présentes:</strong><br>';
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    const size = new Blob([value]).size;
                    info += `• ${key} (${size} bytes)<br>`;
                }
            } else {
                info += '<em>Aucune donnée dans localStorage</em><br>';
            }

            info += '<br><strong>SessionStorage:</strong><br>';
            info += `Nombre de clés: ${sessionStorage.length}<br>`;

            storageDetails.innerHTML = info;
            storageInfo.style.display = 'block';
        }

        function clearExampleSessionsOnly() {
            try {
                const exampleKeywords = [
                    'example', 'Example', 'EXAMPLE', '(Example)', '(example)',
                    'demo', 'Demo', 'DEMO', 'sample', 'Sample', 'SAMPLE',
                    'Software Developer', 'Translator', 'Social Media Influencer',
                    'Travel Guide', 'Image Creator', 'Just chat', 'Markdown 101',
                    'ChartWhiz', 'Snake Game', 'Artifact', '示例', '演示', '样例'
                ];

                const keysToRemove = [];

                // Identifier les clés liées aux sessions d'exemple
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key) {
                        const value = localStorage.getItem(key);
                        if (value && (
                            key.includes('session') ||
                            key.includes('ChatSessionsList')
                        )) {
                            try {
                                const data = JSON.parse(value);
                                // Vérifier si c'est une session d'exemple
                                if (data.name && exampleKeywords.some(keyword => data.name.includes(keyword))) {
                                    keysToRemove.push(key);
                                } else if (Array.isArray(data)) {
                                    // C'est probablement la liste des sessions
                                    const filteredData = data.filter(session =>
                                        !session.name || !exampleKeywords.some(keyword => session.name.includes(keyword))
                                    );
                                    if (filteredData.length !== data.length) {
                                        localStorage.setItem(key, JSON.stringify(filteredData));
                                        console.log('🧹 Liste des sessions nettoyée:', key);
                                    }
                                }
                            } catch (e) {
                                // Ignorer les erreurs de parsing JSON
                            }
                        }
                    }
                }

                // Supprimer les clés identifiées
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                    console.log('🗑️ Session d\'exemple supprimée:', key);
                });

                showSuccess();
                console.log(`✅ ${keysToRemove.length} sessions d'exemple supprimées`);
            } catch (error) {
                console.error('❌ Erreur lors du nettoyage des sessions d\'exemple:', error);
                alert('Erreur lors du nettoyage des sessions d\'exemple: ' + error.message);
            }
        }

        function showSuccess() {
            document.getElementById('success').style.display = 'block';
            setTimeout(() => {
                document.getElementById('success').style.display = 'none';
            }, 5000);
        }

        // Afficher les informations au chargement
        window.addEventListener('load', () => {
            console.log('🧹 Outil de nettoyage du stockage DataTec Workspace chargé');
            console.log('📊 LocalStorage contient', localStorage.length, 'clés');
        });
    </script>
</body>
</html>
